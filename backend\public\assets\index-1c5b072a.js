function Lh(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in e)){const o=Object.getOwnPropertyDescriptor(r,i);o&&Object.defineProperty(e,i,o.get?o:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const a of o.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();var Ve=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function hl(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function $h(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var i=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,i.get?i:{enumerable:!0,get:function(){return e[r]}})}),n}var wd={exports:{}},Mo={},xd={exports:{}},$={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var pi=Symbol.for("react.element"),zh=Symbol.for("react.portal"),Uh=Symbol.for("react.fragment"),Fh=Symbol.for("react.strict_mode"),Mh=Symbol.for("react.profiler"),Bh=Symbol.for("react.provider"),Wh=Symbol.for("react.context"),qh=Symbol.for("react.forward_ref"),Vh=Symbol.for("react.suspense"),Hh=Symbol.for("react.memo"),Kh=Symbol.for("react.lazy"),ju=Symbol.iterator;function Gh(e){return e===null||typeof e!="object"?null:(e=ju&&e[ju]||e["@@iterator"],typeof e=="function"?e:null)}var _d={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},kd=Object.assign,Sd={};function lr(e,t,n){this.props=e,this.context=t,this.refs=Sd,this.updater=n||_d}lr.prototype.isReactComponent={};lr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};lr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Ed(){}Ed.prototype=lr.prototype;function ml(e,t,n){this.props=e,this.context=t,this.refs=Sd,this.updater=n||_d}var vl=ml.prototype=new Ed;vl.constructor=ml;kd(vl,lr.prototype);vl.isPureReactComponent=!0;var Cu=Array.isArray,bd=Object.prototype.hasOwnProperty,gl={current:null},jd={key:!0,ref:!0,__self:!0,__source:!0};function Cd(e,t,n){var r,i={},o=null,a=null;if(t!=null)for(r in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(o=""+t.key),t)bd.call(t,r)&&!jd.hasOwnProperty(r)&&(i[r]=t[r]);var s=arguments.length-2;if(s===1)i.children=n;else if(1<s){for(var l=Array(s),u=0;u<s;u++)l[u]=arguments[u+2];i.children=l}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)i[r]===void 0&&(i[r]=s[r]);return{$$typeof:pi,type:e,key:o,ref:a,props:i,_owner:gl.current}}function Jh(e,t){return{$$typeof:pi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function yl(e){return typeof e=="object"&&e!==null&&e.$$typeof===pi}function Qh(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Pu=/\/+/g;function Sa(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Qh(""+e.key):t.toString(36)}function Yi(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var a=!1;if(e===null)a=!0;else switch(o){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case pi:case zh:a=!0}}if(a)return a=e,i=i(a),e=r===""?"."+Sa(a,0):r,Cu(i)?(n="",e!=null&&(n=e.replace(Pu,"$&/")+"/"),Yi(i,t,n,"",function(u){return u})):i!=null&&(yl(i)&&(i=Jh(i,n+(!i.key||a&&a.key===i.key?"":(""+i.key).replace(Pu,"$&/")+"/")+e)),t.push(i)),1;if(a=0,r=r===""?".":r+":",Cu(e))for(var s=0;s<e.length;s++){o=e[s];var l=r+Sa(o,s);a+=Yi(o,t,n,l,i)}else if(l=Gh(e),typeof l=="function")for(e=l.call(e),s=0;!(o=e.next()).done;)o=o.value,l=r+Sa(o,s++),a+=Yi(o,t,n,l,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function ji(e,t,n){if(e==null)return e;var r=[],i=0;return Yi(e,r,"","",function(o){return t.call(n,o,i++)}),r}function Yh(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ke={current:null},Xi={transition:null},Xh={ReactCurrentDispatcher:ke,ReactCurrentBatchConfig:Xi,ReactCurrentOwner:gl};function Pd(){throw Error("act(...) is not supported in production builds of React.")}$.Children={map:ji,forEach:function(e,t,n){ji(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ji(e,function(){t++}),t},toArray:function(e){return ji(e,function(t){return t})||[]},only:function(e){if(!yl(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};$.Component=lr;$.Fragment=Uh;$.Profiler=Mh;$.PureComponent=ml;$.StrictMode=Fh;$.Suspense=Vh;$.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Xh;$.act=Pd;$.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=kd({},e.props),i=e.key,o=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,a=gl.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(l in t)bd.call(t,l)&&!jd.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&s!==void 0?s[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){s=Array(l);for(var u=0;u<l;u++)s[u]=arguments[u+2];r.children=s}return{$$typeof:pi,type:e.type,key:i,ref:o,props:r,_owner:a}};$.createContext=function(e){return e={$$typeof:Wh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Bh,_context:e},e.Consumer=e};$.createElement=Cd;$.createFactory=function(e){var t=Cd.bind(null,e);return t.type=e,t};$.createRef=function(){return{current:null}};$.forwardRef=function(e){return{$$typeof:qh,render:e}};$.isValidElement=yl;$.lazy=function(e){return{$$typeof:Kh,_payload:{_status:-1,_result:e},_init:Yh}};$.memo=function(e,t){return{$$typeof:Hh,type:e,compare:t===void 0?null:t}};$.startTransition=function(e){var t=Xi.transition;Xi.transition={};try{e()}finally{Xi.transition=t}};$.unstable_act=Pd;$.useCallback=function(e,t){return ke.current.useCallback(e,t)};$.useContext=function(e){return ke.current.useContext(e)};$.useDebugValue=function(){};$.useDeferredValue=function(e){return ke.current.useDeferredValue(e)};$.useEffect=function(e,t){return ke.current.useEffect(e,t)};$.useId=function(){return ke.current.useId()};$.useImperativeHandle=function(e,t,n){return ke.current.useImperativeHandle(e,t,n)};$.useInsertionEffect=function(e,t){return ke.current.useInsertionEffect(e,t)};$.useLayoutEffect=function(e,t){return ke.current.useLayoutEffect(e,t)};$.useMemo=function(e,t){return ke.current.useMemo(e,t)};$.useReducer=function(e,t,n){return ke.current.useReducer(e,t,n)};$.useRef=function(e){return ke.current.useRef(e)};$.useState=function(e){return ke.current.useState(e)};$.useSyncExternalStore=function(e,t,n){return ke.current.useSyncExternalStore(e,t,n)};$.useTransition=function(){return ke.current.useTransition()};$.version="18.3.1";xd.exports=$;var k=xd.exports;const Bo=hl(k),Zh=Lh({__proto__:null,default:Bo},[k]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var em=k,tm=Symbol.for("react.element"),nm=Symbol.for("react.fragment"),rm=Object.prototype.hasOwnProperty,im=em.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,om={key:!0,ref:!0,__self:!0,__source:!0};function Td(e,t,n){var r,i={},o=null,a=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(a=t.ref);for(r in t)rm.call(t,r)&&!om.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:tm,type:e,key:o,ref:a,props:i,_owner:im.current}}Mo.Fragment=nm;Mo.jsx=Td;Mo.jsxs=Td;wd.exports=Mo;var v=wd.exports,os={},Od={exports:{}},Ue={},Rd={exports:{}},Id={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(P,R){var D=P.length;P.push(R);e:for(;0<D;){var H=D-1>>>1,G=P[H];if(0<i(G,R))P[H]=R,P[D]=G,D=H;else break e}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var R=P[0],D=P.pop();if(D!==R){P[0]=D;e:for(var H=0,G=P.length,en=G>>>1;H<en;){var je=2*(H+1)-1,En=P[je],we=je+1,tn=P[we];if(0>i(En,D))we<G&&0>i(tn,En)?(P[H]=tn,P[we]=D,H=we):(P[H]=En,P[je]=D,H=je);else if(we<G&&0>i(tn,D))P[H]=tn,P[we]=D,H=we;else break e}}return R}function i(P,R){var D=P.sortIndex-R.sortIndex;return D!==0?D:P.id-R.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var a=Date,s=a.now();e.unstable_now=function(){return a.now()-s}}var l=[],u=[],c=1,d=null,p=3,g=!1,y=!1,w=!1,_=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function f(P){for(var R=n(u);R!==null;){if(R.callback===null)r(u);else if(R.startTime<=P)r(u),R.sortIndex=R.expirationTime,t(l,R);else break;R=n(u)}}function x(P){if(w=!1,f(P),!y)if(n(l)!==null)y=!0,vr(S);else{var R=n(u);R!==null&&Sn(x,R.startTime-P)}}function S(P,R){y=!1,w&&(w=!1,m(O),O=-1),g=!0;var D=p;try{for(f(R),d=n(l);d!==null&&(!(d.expirationTime>R)||P&&!Ee());){var H=d.callback;if(typeof H=="function"){d.callback=null,p=d.priorityLevel;var G=H(d.expirationTime<=R);R=e.unstable_now(),typeof G=="function"?d.callback=G:d===n(l)&&r(l),f(R)}else r(l);d=n(l)}if(d!==null)var en=!0;else{var je=n(u);je!==null&&Sn(x,je.startTime-R),en=!1}return en}finally{d=null,p=D,g=!1}}var E=!1,j=null,O=-1,F=5,N=-1;function Ee(){return!(e.unstable_now()-N<F)}function dt(){if(j!==null){var P=e.unstable_now();N=P;var R=!0;try{R=j(!0,P)}finally{R?rt():(E=!1,j=null)}}else E=!1}var rt;if(typeof h=="function")rt=function(){h(dt)};else if(typeof MessageChannel<"u"){var ae=new MessageChannel,be=ae.port2;ae.port1.onmessage=dt,rt=function(){be.postMessage(null)}}else rt=function(){_(dt,0)};function vr(P){j=P,E||(E=!0,rt())}function Sn(P,R){O=_(function(){P(e.unstable_now())},R)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(P){P.callback=null},e.unstable_continueExecution=function(){y||g||(y=!0,vr(S))},e.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):F=0<P?Math.floor(1e3/P):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(P){switch(p){case 1:case 2:case 3:var R=3;break;default:R=p}var D=p;p=R;try{return P()}finally{p=D}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(P,R){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var D=p;p=P;try{return R()}finally{p=D}},e.unstable_scheduleCallback=function(P,R,D){var H=e.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?H+D:H):D=H,P){case 1:var G=-1;break;case 2:G=250;break;case 5:G=**********;break;case 4:G=1e4;break;default:G=5e3}return G=D+G,P={id:c++,callback:R,priorityLevel:P,startTime:D,expirationTime:G,sortIndex:-1},D>H?(P.sortIndex=D,t(u,P),n(l)===null&&P===n(u)&&(w?(m(O),O=-1):w=!0,Sn(x,D-H))):(P.sortIndex=G,t(l,P),y||g||(y=!0,vr(S))),P},e.unstable_shouldYield=Ee,e.unstable_wrapCallback=function(P){var R=p;return function(){var D=p;p=R;try{return P.apply(this,arguments)}finally{p=D}}}})(Id);Rd.exports=Id;var am=Rd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sm=k,ze=am;function b(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Ad=new Set,Vr={};function wn(e,t){Xn(e,t),Xn(e+"Capture",t)}function Xn(e,t){for(Vr[e]=t,e=0;e<t.length;e++)Ad.add(t[e])}var wt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),as=Object.prototype.hasOwnProperty,lm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Tu={},Ou={};function um(e){return as.call(Ou,e)?!0:as.call(Tu,e)?!1:lm.test(e)?Ou[e]=!0:(Tu[e]=!0,!1)}function cm(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function dm(e,t,n,r){if(t===null||typeof t>"u"||cm(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Se(e,t,n,r,i,o,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=a}var fe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){fe[e]=new Se(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];fe[t]=new Se(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){fe[e]=new Se(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){fe[e]=new Se(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){fe[e]=new Se(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){fe[e]=new Se(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){fe[e]=new Se(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){fe[e]=new Se(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){fe[e]=new Se(e,5,!1,e.toLowerCase(),null,!1,!1)});var wl=/[\-:]([a-z])/g;function xl(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(wl,xl);fe[t]=new Se(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(wl,xl);fe[t]=new Se(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(wl,xl);fe[t]=new Se(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){fe[e]=new Se(e,1,!1,e.toLowerCase(),null,!1,!1)});fe.xlinkHref=new Se("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){fe[e]=new Se(e,1,!1,e.toLowerCase(),null,!0,!0)});function _l(e,t,n,r){var i=fe.hasOwnProperty(t)?fe[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(dm(t,n,i,r)&&(n=null),r||i===null?um(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var St=sm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ci=Symbol.for("react.element"),Nn=Symbol.for("react.portal"),Dn=Symbol.for("react.fragment"),kl=Symbol.for("react.strict_mode"),ss=Symbol.for("react.profiler"),Nd=Symbol.for("react.provider"),Dd=Symbol.for("react.context"),Sl=Symbol.for("react.forward_ref"),ls=Symbol.for("react.suspense"),us=Symbol.for("react.suspense_list"),El=Symbol.for("react.memo"),Ct=Symbol.for("react.lazy"),Ld=Symbol.for("react.offscreen"),Ru=Symbol.iterator;function yr(e){return e===null||typeof e!="object"?null:(e=Ru&&e[Ru]||e["@@iterator"],typeof e=="function"?e:null)}var X=Object.assign,Ea;function Cr(e){if(Ea===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ea=t&&t[1]||""}return`
`+Ea+e}var ba=!1;function ja(e,t){if(!e||ba)return"";ba=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),o=r.stack.split(`
`),a=i.length-1,s=o.length-1;1<=a&&0<=s&&i[a]!==o[s];)s--;for(;1<=a&&0<=s;a--,s--)if(i[a]!==o[s]){if(a!==1||s!==1)do if(a--,s--,0>s||i[a]!==o[s]){var l=`
`+i[a].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=a&&0<=s);break}}}finally{ba=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Cr(e):""}function pm(e){switch(e.tag){case 5:return Cr(e.type);case 16:return Cr("Lazy");case 13:return Cr("Suspense");case 19:return Cr("SuspenseList");case 0:case 2:case 15:return e=ja(e.type,!1),e;case 11:return e=ja(e.type.render,!1),e;case 1:return e=ja(e.type,!0),e;default:return""}}function cs(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Dn:return"Fragment";case Nn:return"Portal";case ss:return"Profiler";case kl:return"StrictMode";case ls:return"Suspense";case us:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Dd:return(e.displayName||"Context")+".Consumer";case Nd:return(e._context.displayName||"Context")+".Provider";case Sl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case El:return t=e.displayName||null,t!==null?t:cs(e.type)||"Memo";case Ct:t=e._payload,e=e._init;try{return cs(e(t))}catch{}}return null}function fm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return cs(t);case 8:return t===kl?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Ht(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function $d(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function hm(e){var t=$d(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(a){r=""+a,o.call(this,a)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Pi(e){e._valueTracker||(e._valueTracker=hm(e))}function zd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=$d(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function uo(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ds(e,t){var n=t.checked;return X({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Iu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Ht(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Ud(e,t){t=t.checked,t!=null&&_l(e,"checked",t,!1)}function ps(e,t){Ud(e,t);var n=Ht(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?fs(e,t.type,n):t.hasOwnProperty("defaultValue")&&fs(e,t.type,Ht(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Au(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function fs(e,t,n){(t!=="number"||uo(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Pr=Array.isArray;function Hn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Ht(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function hs(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(b(91));return X({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Nu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(b(92));if(Pr(n)){if(1<n.length)throw Error(b(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Ht(n)}}function Fd(e,t){var n=Ht(t.value),r=Ht(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Du(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Md(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ms(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Md(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ti,Bd=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ti=Ti||document.createElement("div"),Ti.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ti.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Hr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Ir={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},mm=["Webkit","ms","Moz","O"];Object.keys(Ir).forEach(function(e){mm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Ir[t]=Ir[e]})});function Wd(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Ir.hasOwnProperty(e)&&Ir[e]?(""+t).trim():t+"px"}function qd(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Wd(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var vm=X({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function vs(e,t){if(t){if(vm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(b(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(b(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(b(61))}if(t.style!=null&&typeof t.style!="object")throw Error(b(62))}}function gs(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ys=null;function bl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ws=null,Kn=null,Gn=null;function Lu(e){if(e=mi(e)){if(typeof ws!="function")throw Error(b(280));var t=e.stateNode;t&&(t=Ko(t),ws(e.stateNode,e.type,t))}}function Vd(e){Kn?Gn?Gn.push(e):Gn=[e]:Kn=e}function Hd(){if(Kn){var e=Kn,t=Gn;if(Gn=Kn=null,Lu(e),t)for(e=0;e<t.length;e++)Lu(t[e])}}function Kd(e,t){return e(t)}function Gd(){}var Ca=!1;function Jd(e,t,n){if(Ca)return e(t,n);Ca=!0;try{return Kd(e,t,n)}finally{Ca=!1,(Kn!==null||Gn!==null)&&(Gd(),Hd())}}function Kr(e,t){var n=e.stateNode;if(n===null)return null;var r=Ko(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(b(231,t,typeof n));return n}var xs=!1;if(wt)try{var wr={};Object.defineProperty(wr,"passive",{get:function(){xs=!0}}),window.addEventListener("test",wr,wr),window.removeEventListener("test",wr,wr)}catch{xs=!1}function gm(e,t,n,r,i,o,a,s,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Ar=!1,co=null,po=!1,_s=null,ym={onError:function(e){Ar=!0,co=e}};function wm(e,t,n,r,i,o,a,s,l){Ar=!1,co=null,gm.apply(ym,arguments)}function xm(e,t,n,r,i,o,a,s,l){if(wm.apply(this,arguments),Ar){if(Ar){var u=co;Ar=!1,co=null}else throw Error(b(198));po||(po=!0,_s=u)}}function xn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Qd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function $u(e){if(xn(e)!==e)throw Error(b(188))}function _m(e){var t=e.alternate;if(!t){if(t=xn(e),t===null)throw Error(b(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return $u(i),e;if(o===r)return $u(i),t;o=o.sibling}throw Error(b(188))}if(n.return!==r.return)n=i,r=o;else{for(var a=!1,s=i.child;s;){if(s===n){a=!0,n=i,r=o;break}if(s===r){a=!0,r=i,n=o;break}s=s.sibling}if(!a){for(s=o.child;s;){if(s===n){a=!0,n=o,r=i;break}if(s===r){a=!0,r=o,n=i;break}s=s.sibling}if(!a)throw Error(b(189))}}if(n.alternate!==r)throw Error(b(190))}if(n.tag!==3)throw Error(b(188));return n.stateNode.current===n?e:t}function Yd(e){return e=_m(e),e!==null?Xd(e):null}function Xd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Xd(e);if(t!==null)return t;e=e.sibling}return null}var Zd=ze.unstable_scheduleCallback,zu=ze.unstable_cancelCallback,km=ze.unstable_shouldYield,Sm=ze.unstable_requestPaint,ee=ze.unstable_now,Em=ze.unstable_getCurrentPriorityLevel,jl=ze.unstable_ImmediatePriority,ep=ze.unstable_UserBlockingPriority,fo=ze.unstable_NormalPriority,bm=ze.unstable_LowPriority,tp=ze.unstable_IdlePriority,Wo=null,ut=null;function jm(e){if(ut&&typeof ut.onCommitFiberRoot=="function")try{ut.onCommitFiberRoot(Wo,e,void 0,(e.current.flags&128)===128)}catch{}}var et=Math.clz32?Math.clz32:Tm,Cm=Math.log,Pm=Math.LN2;function Tm(e){return e>>>=0,e===0?32:31-(Cm(e)/Pm|0)|0}var Oi=64,Ri=4194304;function Tr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ho(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,a=n&268435455;if(a!==0){var s=a&~i;s!==0?r=Tr(s):(o&=a,o!==0&&(r=Tr(o)))}else a=n&~i,a!==0?r=Tr(a):o!==0&&(r=Tr(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-et(t),i=1<<n,r|=e[n],t&=~i;return r}function Om(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Rm(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var a=31-et(o),s=1<<a,l=i[a];l===-1?(!(s&n)||s&r)&&(i[a]=Om(s,t)):l<=t&&(e.expiredLanes|=s),o&=~s}}function ks(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function np(){var e=Oi;return Oi<<=1,!(Oi&4194240)&&(Oi=64),e}function Pa(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function fi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-et(t),e[t]=n}function Im(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-et(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function Cl(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-et(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var U=0;function rp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var ip,Pl,op,ap,sp,Ss=!1,Ii=[],$t=null,zt=null,Ut=null,Gr=new Map,Jr=new Map,Ot=[],Am="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Uu(e,t){switch(e){case"focusin":case"focusout":$t=null;break;case"dragenter":case"dragleave":zt=null;break;case"mouseover":case"mouseout":Ut=null;break;case"pointerover":case"pointerout":Gr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Jr.delete(t.pointerId)}}function xr(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=mi(t),t!==null&&Pl(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Nm(e,t,n,r,i){switch(t){case"focusin":return $t=xr($t,e,t,n,r,i),!0;case"dragenter":return zt=xr(zt,e,t,n,r,i),!0;case"mouseover":return Ut=xr(Ut,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return Gr.set(o,xr(Gr.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,Jr.set(o,xr(Jr.get(o)||null,e,t,n,r,i)),!0}return!1}function lp(e){var t=un(e.target);if(t!==null){var n=xn(t);if(n!==null){if(t=n.tag,t===13){if(t=Qd(n),t!==null){e.blockedOn=t,sp(e.priority,function(){op(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Zi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Es(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);ys=r,n.target.dispatchEvent(r),ys=null}else return t=mi(n),t!==null&&Pl(t),e.blockedOn=n,!1;t.shift()}return!0}function Fu(e,t,n){Zi(e)&&n.delete(t)}function Dm(){Ss=!1,$t!==null&&Zi($t)&&($t=null),zt!==null&&Zi(zt)&&(zt=null),Ut!==null&&Zi(Ut)&&(Ut=null),Gr.forEach(Fu),Jr.forEach(Fu)}function _r(e,t){e.blockedOn===t&&(e.blockedOn=null,Ss||(Ss=!0,ze.unstable_scheduleCallback(ze.unstable_NormalPriority,Dm)))}function Qr(e){function t(i){return _r(i,e)}if(0<Ii.length){_r(Ii[0],e);for(var n=1;n<Ii.length;n++){var r=Ii[n];r.blockedOn===e&&(r.blockedOn=null)}}for($t!==null&&_r($t,e),zt!==null&&_r(zt,e),Ut!==null&&_r(Ut,e),Gr.forEach(t),Jr.forEach(t),n=0;n<Ot.length;n++)r=Ot[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Ot.length&&(n=Ot[0],n.blockedOn===null);)lp(n),n.blockedOn===null&&Ot.shift()}var Jn=St.ReactCurrentBatchConfig,mo=!0;function Lm(e,t,n,r){var i=U,o=Jn.transition;Jn.transition=null;try{U=1,Tl(e,t,n,r)}finally{U=i,Jn.transition=o}}function $m(e,t,n,r){var i=U,o=Jn.transition;Jn.transition=null;try{U=4,Tl(e,t,n,r)}finally{U=i,Jn.transition=o}}function Tl(e,t,n,r){if(mo){var i=Es(e,t,n,r);if(i===null)za(e,t,r,vo,n),Uu(e,r);else if(Nm(i,e,t,n,r))r.stopPropagation();else if(Uu(e,r),t&4&&-1<Am.indexOf(e)){for(;i!==null;){var o=mi(i);if(o!==null&&ip(o),o=Es(e,t,n,r),o===null&&za(e,t,r,vo,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else za(e,t,r,null,n)}}var vo=null;function Es(e,t,n,r){if(vo=null,e=bl(r),e=un(e),e!==null)if(t=xn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Qd(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return vo=e,null}function up(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Em()){case jl:return 1;case ep:return 4;case fo:case bm:return 16;case tp:return 536870912;default:return 16}default:return 16}}var At=null,Ol=null,eo=null;function cp(){if(eo)return eo;var e,t=Ol,n=t.length,r,i="value"in At?At.value:At.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var a=n-e;for(r=1;r<=a&&t[n-r]===i[o-r];r++);return eo=i.slice(e,1<r?1-r:void 0)}function to(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ai(){return!0}function Mu(){return!1}function Fe(e){function t(n,r,i,o,a){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=a,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(o):o[s]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Ai:Mu,this.isPropagationStopped=Mu,this}return X(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ai)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ai)},persist:function(){},isPersistent:Ai}),t}var ur={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Rl=Fe(ur),hi=X({},ur,{view:0,detail:0}),zm=Fe(hi),Ta,Oa,kr,qo=X({},hi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Il,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==kr&&(kr&&e.type==="mousemove"?(Ta=e.screenX-kr.screenX,Oa=e.screenY-kr.screenY):Oa=Ta=0,kr=e),Ta)},movementY:function(e){return"movementY"in e?e.movementY:Oa}}),Bu=Fe(qo),Um=X({},qo,{dataTransfer:0}),Fm=Fe(Um),Mm=X({},hi,{relatedTarget:0}),Ra=Fe(Mm),Bm=X({},ur,{animationName:0,elapsedTime:0,pseudoElement:0}),Wm=Fe(Bm),qm=X({},ur,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Vm=Fe(qm),Hm=X({},ur,{data:0}),Wu=Fe(Hm),Km={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Gm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Jm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Qm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Jm[e])?!!t[e]:!1}function Il(){return Qm}var Ym=X({},hi,{key:function(e){if(e.key){var t=Km[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=to(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Gm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Il,charCode:function(e){return e.type==="keypress"?to(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?to(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Xm=Fe(Ym),Zm=X({},qo,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),qu=Fe(Zm),ev=X({},hi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Il}),tv=Fe(ev),nv=X({},ur,{propertyName:0,elapsedTime:0,pseudoElement:0}),rv=Fe(nv),iv=X({},qo,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ov=Fe(iv),av=[9,13,27,32],Al=wt&&"CompositionEvent"in window,Nr=null;wt&&"documentMode"in document&&(Nr=document.documentMode);var sv=wt&&"TextEvent"in window&&!Nr,dp=wt&&(!Al||Nr&&8<Nr&&11>=Nr),Vu=String.fromCharCode(32),Hu=!1;function pp(e,t){switch(e){case"keyup":return av.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function fp(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Ln=!1;function lv(e,t){switch(e){case"compositionend":return fp(t);case"keypress":return t.which!==32?null:(Hu=!0,Vu);case"textInput":return e=t.data,e===Vu&&Hu?null:e;default:return null}}function uv(e,t){if(Ln)return e==="compositionend"||!Al&&pp(e,t)?(e=cp(),eo=Ol=At=null,Ln=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return dp&&t.locale!=="ko"?null:t.data;default:return null}}var cv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ku(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!cv[e.type]:t==="textarea"}function hp(e,t,n,r){Vd(r),t=go(t,"onChange"),0<t.length&&(n=new Rl("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Dr=null,Yr=null;function dv(e){bp(e,0)}function Vo(e){var t=Un(e);if(zd(t))return e}function pv(e,t){if(e==="change")return t}var mp=!1;if(wt){var Ia;if(wt){var Aa="oninput"in document;if(!Aa){var Gu=document.createElement("div");Gu.setAttribute("oninput","return;"),Aa=typeof Gu.oninput=="function"}Ia=Aa}else Ia=!1;mp=Ia&&(!document.documentMode||9<document.documentMode)}function Ju(){Dr&&(Dr.detachEvent("onpropertychange",vp),Yr=Dr=null)}function vp(e){if(e.propertyName==="value"&&Vo(Yr)){var t=[];hp(t,Yr,e,bl(e)),Jd(dv,t)}}function fv(e,t,n){e==="focusin"?(Ju(),Dr=t,Yr=n,Dr.attachEvent("onpropertychange",vp)):e==="focusout"&&Ju()}function hv(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Vo(Yr)}function mv(e,t){if(e==="click")return Vo(t)}function vv(e,t){if(e==="input"||e==="change")return Vo(t)}function gv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var nt=typeof Object.is=="function"?Object.is:gv;function Xr(e,t){if(nt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!as.call(t,i)||!nt(e[i],t[i]))return!1}return!0}function Qu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Yu(e,t){var n=Qu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Qu(n)}}function gp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?gp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function yp(){for(var e=window,t=uo();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=uo(e.document)}return t}function Nl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function yv(e){var t=yp(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&gp(n.ownerDocument.documentElement,n)){if(r!==null&&Nl(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=Yu(n,o);var a=Yu(n,r);i&&a&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var wv=wt&&"documentMode"in document&&11>=document.documentMode,$n=null,bs=null,Lr=null,js=!1;function Xu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;js||$n==null||$n!==uo(r)||(r=$n,"selectionStart"in r&&Nl(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Lr&&Xr(Lr,r)||(Lr=r,r=go(bs,"onSelect"),0<r.length&&(t=new Rl("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=$n)))}function Ni(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var zn={animationend:Ni("Animation","AnimationEnd"),animationiteration:Ni("Animation","AnimationIteration"),animationstart:Ni("Animation","AnimationStart"),transitionend:Ni("Transition","TransitionEnd")},Na={},wp={};wt&&(wp=document.createElement("div").style,"AnimationEvent"in window||(delete zn.animationend.animation,delete zn.animationiteration.animation,delete zn.animationstart.animation),"TransitionEvent"in window||delete zn.transitionend.transition);function Ho(e){if(Na[e])return Na[e];if(!zn[e])return e;var t=zn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in wp)return Na[e]=t[n];return e}var xp=Ho("animationend"),_p=Ho("animationiteration"),kp=Ho("animationstart"),Sp=Ho("transitionend"),Ep=new Map,Zu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Gt(e,t){Ep.set(e,t),wn(t,[e])}for(var Da=0;Da<Zu.length;Da++){var La=Zu[Da],xv=La.toLowerCase(),_v=La[0].toUpperCase()+La.slice(1);Gt(xv,"on"+_v)}Gt(xp,"onAnimationEnd");Gt(_p,"onAnimationIteration");Gt(kp,"onAnimationStart");Gt("dblclick","onDoubleClick");Gt("focusin","onFocus");Gt("focusout","onBlur");Gt(Sp,"onTransitionEnd");Xn("onMouseEnter",["mouseout","mouseover"]);Xn("onMouseLeave",["mouseout","mouseover"]);Xn("onPointerEnter",["pointerout","pointerover"]);Xn("onPointerLeave",["pointerout","pointerover"]);wn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));wn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));wn("onBeforeInput",["compositionend","keypress","textInput","paste"]);wn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));wn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));wn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Or="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),kv=new Set("cancel close invalid load scroll toggle".split(" ").concat(Or));function ec(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,xm(r,t,void 0,e),e.currentTarget=null}function bp(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var a=r.length-1;0<=a;a--){var s=r[a],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==o&&i.isPropagationStopped())break e;ec(i,s,u),o=l}else for(a=0;a<r.length;a++){if(s=r[a],l=s.instance,u=s.currentTarget,s=s.listener,l!==o&&i.isPropagationStopped())break e;ec(i,s,u),o=l}}}if(po)throw e=_s,po=!1,_s=null,e}function q(e,t){var n=t[Rs];n===void 0&&(n=t[Rs]=new Set);var r=e+"__bubble";n.has(r)||(jp(t,e,2,!1),n.add(r))}function $a(e,t,n){var r=0;t&&(r|=4),jp(n,e,r,t)}var Di="_reactListening"+Math.random().toString(36).slice(2);function Zr(e){if(!e[Di]){e[Di]=!0,Ad.forEach(function(n){n!=="selectionchange"&&(kv.has(n)||$a(n,!1,e),$a(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Di]||(t[Di]=!0,$a("selectionchange",!1,t))}}function jp(e,t,n,r){switch(up(t)){case 1:var i=Lm;break;case 4:i=$m;break;default:i=Tl}n=i.bind(null,t,n,e),i=void 0,!xs||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function za(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var s=r.stateNode.containerInfo;if(s===i||s.nodeType===8&&s.parentNode===i)break;if(a===4)for(a=r.return;a!==null;){var l=a.tag;if((l===3||l===4)&&(l=a.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;a=a.return}for(;s!==null;){if(a=un(s),a===null)return;if(l=a.tag,l===5||l===6){r=o=a;continue e}s=s.parentNode}}r=r.return}Jd(function(){var u=o,c=bl(n),d=[];e:{var p=Ep.get(e);if(p!==void 0){var g=Rl,y=e;switch(e){case"keypress":if(to(n)===0)break e;case"keydown":case"keyup":g=Xm;break;case"focusin":y="focus",g=Ra;break;case"focusout":y="blur",g=Ra;break;case"beforeblur":case"afterblur":g=Ra;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=Bu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=Fm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=tv;break;case xp:case _p:case kp:g=Wm;break;case Sp:g=rv;break;case"scroll":g=zm;break;case"wheel":g=ov;break;case"copy":case"cut":case"paste":g=Vm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=qu}var w=(t&4)!==0,_=!w&&e==="scroll",m=w?p!==null?p+"Capture":null:p;w=[];for(var h=u,f;h!==null;){f=h;var x=f.stateNode;if(f.tag===5&&x!==null&&(f=x,m!==null&&(x=Kr(h,m),x!=null&&w.push(ei(h,x,f)))),_)break;h=h.return}0<w.length&&(p=new g(p,y,null,n,c),d.push({event:p,listeners:w}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",p&&n!==ys&&(y=n.relatedTarget||n.fromElement)&&(un(y)||y[xt]))break e;if((g||p)&&(p=c.window===c?c:(p=c.ownerDocument)?p.defaultView||p.parentWindow:window,g?(y=n.relatedTarget||n.toElement,g=u,y=y?un(y):null,y!==null&&(_=xn(y),y!==_||y.tag!==5&&y.tag!==6)&&(y=null)):(g=null,y=u),g!==y)){if(w=Bu,x="onMouseLeave",m="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(w=qu,x="onPointerLeave",m="onPointerEnter",h="pointer"),_=g==null?p:Un(g),f=y==null?p:Un(y),p=new w(x,h+"leave",g,n,c),p.target=_,p.relatedTarget=f,x=null,un(c)===u&&(w=new w(m,h+"enter",y,n,c),w.target=f,w.relatedTarget=_,x=w),_=x,g&&y)t:{for(w=g,m=y,h=0,f=w;f;f=Pn(f))h++;for(f=0,x=m;x;x=Pn(x))f++;for(;0<h-f;)w=Pn(w),h--;for(;0<f-h;)m=Pn(m),f--;for(;h--;){if(w===m||m!==null&&w===m.alternate)break t;w=Pn(w),m=Pn(m)}w=null}else w=null;g!==null&&tc(d,p,g,w,!1),y!==null&&_!==null&&tc(d,_,y,w,!0)}}e:{if(p=u?Un(u):window,g=p.nodeName&&p.nodeName.toLowerCase(),g==="select"||g==="input"&&p.type==="file")var S=pv;else if(Ku(p))if(mp)S=vv;else{S=hv;var E=fv}else(g=p.nodeName)&&g.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(S=mv);if(S&&(S=S(e,u))){hp(d,S,n,c);break e}E&&E(e,p,u),e==="focusout"&&(E=p._wrapperState)&&E.controlled&&p.type==="number"&&fs(p,"number",p.value)}switch(E=u?Un(u):window,e){case"focusin":(Ku(E)||E.contentEditable==="true")&&($n=E,bs=u,Lr=null);break;case"focusout":Lr=bs=$n=null;break;case"mousedown":js=!0;break;case"contextmenu":case"mouseup":case"dragend":js=!1,Xu(d,n,c);break;case"selectionchange":if(wv)break;case"keydown":case"keyup":Xu(d,n,c)}var j;if(Al)e:{switch(e){case"compositionstart":var O="onCompositionStart";break e;case"compositionend":O="onCompositionEnd";break e;case"compositionupdate":O="onCompositionUpdate";break e}O=void 0}else Ln?pp(e,n)&&(O="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(O="onCompositionStart");O&&(dp&&n.locale!=="ko"&&(Ln||O!=="onCompositionStart"?O==="onCompositionEnd"&&Ln&&(j=cp()):(At=c,Ol="value"in At?At.value:At.textContent,Ln=!0)),E=go(u,O),0<E.length&&(O=new Wu(O,e,null,n,c),d.push({event:O,listeners:E}),j?O.data=j:(j=fp(n),j!==null&&(O.data=j)))),(j=sv?lv(e,n):uv(e,n))&&(u=go(u,"onBeforeInput"),0<u.length&&(c=new Wu("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=j))}bp(d,t)})}function ei(e,t,n){return{instance:e,listener:t,currentTarget:n}}function go(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=Kr(e,n),o!=null&&r.unshift(ei(e,o,i)),o=Kr(e,t),o!=null&&r.push(ei(e,o,i))),e=e.return}return r}function Pn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function tc(e,t,n,r,i){for(var o=t._reactName,a=[];n!==null&&n!==r;){var s=n,l=s.alternate,u=s.stateNode;if(l!==null&&l===r)break;s.tag===5&&u!==null&&(s=u,i?(l=Kr(n,o),l!=null&&a.unshift(ei(n,l,s))):i||(l=Kr(n,o),l!=null&&a.push(ei(n,l,s)))),n=n.return}a.length!==0&&e.push({event:t,listeners:a})}var Sv=/\r\n?/g,Ev=/\u0000|\uFFFD/g;function nc(e){return(typeof e=="string"?e:""+e).replace(Sv,`
`).replace(Ev,"")}function Li(e,t,n){if(t=nc(t),nc(e)!==t&&n)throw Error(b(425))}function yo(){}var Cs=null,Ps=null;function Ts(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Os=typeof setTimeout=="function"?setTimeout:void 0,bv=typeof clearTimeout=="function"?clearTimeout:void 0,rc=typeof Promise=="function"?Promise:void 0,jv=typeof queueMicrotask=="function"?queueMicrotask:typeof rc<"u"?function(e){return rc.resolve(null).then(e).catch(Cv)}:Os;function Cv(e){setTimeout(function(){throw e})}function Ua(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Qr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Qr(t)}function Ft(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function ic(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var cr=Math.random().toString(36).slice(2),st="__reactFiber$"+cr,ti="__reactProps$"+cr,xt="__reactContainer$"+cr,Rs="__reactEvents$"+cr,Pv="__reactListeners$"+cr,Tv="__reactHandles$"+cr;function un(e){var t=e[st];if(t)return t;for(var n=e.parentNode;n;){if(t=n[xt]||n[st]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=ic(e);e!==null;){if(n=e[st])return n;e=ic(e)}return t}e=n,n=e.parentNode}return null}function mi(e){return e=e[st]||e[xt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Un(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(b(33))}function Ko(e){return e[ti]||null}var Is=[],Fn=-1;function Jt(e){return{current:e}}function V(e){0>Fn||(e.current=Is[Fn],Is[Fn]=null,Fn--)}function W(e,t){Fn++,Is[Fn]=e.current,e.current=t}var Kt={},ye=Jt(Kt),Re=Jt(!1),hn=Kt;function Zn(e,t){var n=e.type.contextTypes;if(!n)return Kt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Ie(e){return e=e.childContextTypes,e!=null}function wo(){V(Re),V(ye)}function oc(e,t,n){if(ye.current!==Kt)throw Error(b(168));W(ye,t),W(Re,n)}function Cp(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(b(108,fm(e)||"Unknown",i));return X({},n,r)}function xo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Kt,hn=ye.current,W(ye,e),W(Re,Re.current),!0}function ac(e,t,n){var r=e.stateNode;if(!r)throw Error(b(169));n?(e=Cp(e,t,hn),r.__reactInternalMemoizedMergedChildContext=e,V(Re),V(ye),W(ye,e)):V(Re),W(Re,n)}var mt=null,Go=!1,Fa=!1;function Pp(e){mt===null?mt=[e]:mt.push(e)}function Ov(e){Go=!0,Pp(e)}function Qt(){if(!Fa&&mt!==null){Fa=!0;var e=0,t=U;try{var n=mt;for(U=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}mt=null,Go=!1}catch(i){throw mt!==null&&(mt=mt.slice(e+1)),Zd(jl,Qt),i}finally{U=t,Fa=!1}}return null}var Mn=[],Bn=0,_o=null,ko=0,Me=[],Be=0,mn=null,vt=1,gt="";function rn(e,t){Mn[Bn++]=ko,Mn[Bn++]=_o,_o=e,ko=t}function Tp(e,t,n){Me[Be++]=vt,Me[Be++]=gt,Me[Be++]=mn,mn=e;var r=vt;e=gt;var i=32-et(r)-1;r&=~(1<<i),n+=1;var o=32-et(t)+i;if(30<o){var a=i-i%5;o=(r&(1<<a)-1).toString(32),r>>=a,i-=a,vt=1<<32-et(t)+i|n<<i|r,gt=o+e}else vt=1<<o|n<<i|r,gt=e}function Dl(e){e.return!==null&&(rn(e,1),Tp(e,1,0))}function Ll(e){for(;e===_o;)_o=Mn[--Bn],Mn[Bn]=null,ko=Mn[--Bn],Mn[Bn]=null;for(;e===mn;)mn=Me[--Be],Me[Be]=null,gt=Me[--Be],Me[Be]=null,vt=Me[--Be],Me[Be]=null}var $e=null,Le=null,K=!1,Ze=null;function Op(e,t){var n=We(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function sc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,$e=e,Le=Ft(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,$e=e,Le=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=mn!==null?{id:vt,overflow:gt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=We(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,$e=e,Le=null,!0):!1;default:return!1}}function As(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ns(e){if(K){var t=Le;if(t){var n=t;if(!sc(e,t)){if(As(e))throw Error(b(418));t=Ft(n.nextSibling);var r=$e;t&&sc(e,t)?Op(r,n):(e.flags=e.flags&-4097|2,K=!1,$e=e)}}else{if(As(e))throw Error(b(418));e.flags=e.flags&-4097|2,K=!1,$e=e}}}function lc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;$e=e}function $i(e){if(e!==$e)return!1;if(!K)return lc(e),K=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ts(e.type,e.memoizedProps)),t&&(t=Le)){if(As(e))throw Rp(),Error(b(418));for(;t;)Op(e,t),t=Ft(t.nextSibling)}if(lc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(b(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Le=Ft(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Le=null}}else Le=$e?Ft(e.stateNode.nextSibling):null;return!0}function Rp(){for(var e=Le;e;)e=Ft(e.nextSibling)}function er(){Le=$e=null,K=!1}function $l(e){Ze===null?Ze=[e]:Ze.push(e)}var Rv=St.ReactCurrentBatchConfig;function Sr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(b(309));var r=n.stateNode}if(!r)throw Error(b(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(a){var s=i.refs;a===null?delete s[o]:s[o]=a},t._stringRef=o,t)}if(typeof e!="string")throw Error(b(284));if(!n._owner)throw Error(b(290,e))}return e}function zi(e,t){throw e=Object.prototype.toString.call(t),Error(b(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function uc(e){var t=e._init;return t(e._payload)}function Ip(e){function t(m,h){if(e){var f=m.deletions;f===null?(m.deletions=[h],m.flags|=16):f.push(h)}}function n(m,h){if(!e)return null;for(;h!==null;)t(m,h),h=h.sibling;return null}function r(m,h){for(m=new Map;h!==null;)h.key!==null?m.set(h.key,h):m.set(h.index,h),h=h.sibling;return m}function i(m,h){return m=qt(m,h),m.index=0,m.sibling=null,m}function o(m,h,f){return m.index=f,e?(f=m.alternate,f!==null?(f=f.index,f<h?(m.flags|=2,h):f):(m.flags|=2,h)):(m.flags|=1048576,h)}function a(m){return e&&m.alternate===null&&(m.flags|=2),m}function s(m,h,f,x){return h===null||h.tag!==6?(h=Ka(f,m.mode,x),h.return=m,h):(h=i(h,f),h.return=m,h)}function l(m,h,f,x){var S=f.type;return S===Dn?c(m,h,f.props.children,x,f.key):h!==null&&(h.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Ct&&uc(S)===h.type)?(x=i(h,f.props),x.ref=Sr(m,h,f),x.return=m,x):(x=lo(f.type,f.key,f.props,null,m.mode,x),x.ref=Sr(m,h,f),x.return=m,x)}function u(m,h,f,x){return h===null||h.tag!==4||h.stateNode.containerInfo!==f.containerInfo||h.stateNode.implementation!==f.implementation?(h=Ga(f,m.mode,x),h.return=m,h):(h=i(h,f.children||[]),h.return=m,h)}function c(m,h,f,x,S){return h===null||h.tag!==7?(h=fn(f,m.mode,x,S),h.return=m,h):(h=i(h,f),h.return=m,h)}function d(m,h,f){if(typeof h=="string"&&h!==""||typeof h=="number")return h=Ka(""+h,m.mode,f),h.return=m,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Ci:return f=lo(h.type,h.key,h.props,null,m.mode,f),f.ref=Sr(m,null,h),f.return=m,f;case Nn:return h=Ga(h,m.mode,f),h.return=m,h;case Ct:var x=h._init;return d(m,x(h._payload),f)}if(Pr(h)||yr(h))return h=fn(h,m.mode,f,null),h.return=m,h;zi(m,h)}return null}function p(m,h,f,x){var S=h!==null?h.key:null;if(typeof f=="string"&&f!==""||typeof f=="number")return S!==null?null:s(m,h,""+f,x);if(typeof f=="object"&&f!==null){switch(f.$$typeof){case Ci:return f.key===S?l(m,h,f,x):null;case Nn:return f.key===S?u(m,h,f,x):null;case Ct:return S=f._init,p(m,h,S(f._payload),x)}if(Pr(f)||yr(f))return S!==null?null:c(m,h,f,x,null);zi(m,f)}return null}function g(m,h,f,x,S){if(typeof x=="string"&&x!==""||typeof x=="number")return m=m.get(f)||null,s(h,m,""+x,S);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case Ci:return m=m.get(x.key===null?f:x.key)||null,l(h,m,x,S);case Nn:return m=m.get(x.key===null?f:x.key)||null,u(h,m,x,S);case Ct:var E=x._init;return g(m,h,f,E(x._payload),S)}if(Pr(x)||yr(x))return m=m.get(f)||null,c(h,m,x,S,null);zi(h,x)}return null}function y(m,h,f,x){for(var S=null,E=null,j=h,O=h=0,F=null;j!==null&&O<f.length;O++){j.index>O?(F=j,j=null):F=j.sibling;var N=p(m,j,f[O],x);if(N===null){j===null&&(j=F);break}e&&j&&N.alternate===null&&t(m,j),h=o(N,h,O),E===null?S=N:E.sibling=N,E=N,j=F}if(O===f.length)return n(m,j),K&&rn(m,O),S;if(j===null){for(;O<f.length;O++)j=d(m,f[O],x),j!==null&&(h=o(j,h,O),E===null?S=j:E.sibling=j,E=j);return K&&rn(m,O),S}for(j=r(m,j);O<f.length;O++)F=g(j,m,O,f[O],x),F!==null&&(e&&F.alternate!==null&&j.delete(F.key===null?O:F.key),h=o(F,h,O),E===null?S=F:E.sibling=F,E=F);return e&&j.forEach(function(Ee){return t(m,Ee)}),K&&rn(m,O),S}function w(m,h,f,x){var S=yr(f);if(typeof S!="function")throw Error(b(150));if(f=S.call(f),f==null)throw Error(b(151));for(var E=S=null,j=h,O=h=0,F=null,N=f.next();j!==null&&!N.done;O++,N=f.next()){j.index>O?(F=j,j=null):F=j.sibling;var Ee=p(m,j,N.value,x);if(Ee===null){j===null&&(j=F);break}e&&j&&Ee.alternate===null&&t(m,j),h=o(Ee,h,O),E===null?S=Ee:E.sibling=Ee,E=Ee,j=F}if(N.done)return n(m,j),K&&rn(m,O),S;if(j===null){for(;!N.done;O++,N=f.next())N=d(m,N.value,x),N!==null&&(h=o(N,h,O),E===null?S=N:E.sibling=N,E=N);return K&&rn(m,O),S}for(j=r(m,j);!N.done;O++,N=f.next())N=g(j,m,O,N.value,x),N!==null&&(e&&N.alternate!==null&&j.delete(N.key===null?O:N.key),h=o(N,h,O),E===null?S=N:E.sibling=N,E=N);return e&&j.forEach(function(dt){return t(m,dt)}),K&&rn(m,O),S}function _(m,h,f,x){if(typeof f=="object"&&f!==null&&f.type===Dn&&f.key===null&&(f=f.props.children),typeof f=="object"&&f!==null){switch(f.$$typeof){case Ci:e:{for(var S=f.key,E=h;E!==null;){if(E.key===S){if(S=f.type,S===Dn){if(E.tag===7){n(m,E.sibling),h=i(E,f.props.children),h.return=m,m=h;break e}}else if(E.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Ct&&uc(S)===E.type){n(m,E.sibling),h=i(E,f.props),h.ref=Sr(m,E,f),h.return=m,m=h;break e}n(m,E);break}else t(m,E);E=E.sibling}f.type===Dn?(h=fn(f.props.children,m.mode,x,f.key),h.return=m,m=h):(x=lo(f.type,f.key,f.props,null,m.mode,x),x.ref=Sr(m,h,f),x.return=m,m=x)}return a(m);case Nn:e:{for(E=f.key;h!==null;){if(h.key===E)if(h.tag===4&&h.stateNode.containerInfo===f.containerInfo&&h.stateNode.implementation===f.implementation){n(m,h.sibling),h=i(h,f.children||[]),h.return=m,m=h;break e}else{n(m,h);break}else t(m,h);h=h.sibling}h=Ga(f,m.mode,x),h.return=m,m=h}return a(m);case Ct:return E=f._init,_(m,h,E(f._payload),x)}if(Pr(f))return y(m,h,f,x);if(yr(f))return w(m,h,f,x);zi(m,f)}return typeof f=="string"&&f!==""||typeof f=="number"?(f=""+f,h!==null&&h.tag===6?(n(m,h.sibling),h=i(h,f),h.return=m,m=h):(n(m,h),h=Ka(f,m.mode,x),h.return=m,m=h),a(m)):n(m,h)}return _}var tr=Ip(!0),Ap=Ip(!1),So=Jt(null),Eo=null,Wn=null,zl=null;function Ul(){zl=Wn=Eo=null}function Fl(e){var t=So.current;V(So),e._currentValue=t}function Ds(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Qn(e,t){Eo=e,zl=Wn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Oe=!0),e.firstContext=null)}function He(e){var t=e._currentValue;if(zl!==e)if(e={context:e,memoizedValue:t,next:null},Wn===null){if(Eo===null)throw Error(b(308));Wn=e,Eo.dependencies={lanes:0,firstContext:e}}else Wn=Wn.next=e;return t}var cn=null;function Ml(e){cn===null?cn=[e]:cn.push(e)}function Np(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,Ml(t)):(n.next=i.next,i.next=n),t.interleaved=n,_t(e,r)}function _t(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Pt=!1;function Bl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Dp(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function yt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Mt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,z&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,_t(e,n)}return i=r.interleaved,i===null?(t.next=t,Ml(r)):(t.next=i.next,i.next=t),r.interleaved=t,_t(e,n)}function no(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Cl(e,n)}}function cc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=a:o=o.next=a,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function bo(e,t,n,r){var i=e.updateQueue;Pt=!1;var o=i.firstBaseUpdate,a=i.lastBaseUpdate,s=i.shared.pending;if(s!==null){i.shared.pending=null;var l=s,u=l.next;l.next=null,a===null?o=u:a.next=u,a=l;var c=e.alternate;c!==null&&(c=c.updateQueue,s=c.lastBaseUpdate,s!==a&&(s===null?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=l))}if(o!==null){var d=i.baseState;a=0,c=u=l=null,s=o;do{var p=s.lane,g=s.eventTime;if((r&p)===p){c!==null&&(c=c.next={eventTime:g,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var y=e,w=s;switch(p=t,g=n,w.tag){case 1:if(y=w.payload,typeof y=="function"){d=y.call(g,d,p);break e}d=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=w.payload,p=typeof y=="function"?y.call(g,d,p):y,p==null)break e;d=X({},d,p);break e;case 2:Pt=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,p=i.effects,p===null?i.effects=[s]:p.push(s))}else g={eventTime:g,lane:p,tag:s.tag,payload:s.payload,callback:s.callback,next:null},c===null?(u=c=g,l=d):c=c.next=g,a|=p;if(s=s.next,s===null){if(s=i.shared.pending,s===null)break;p=s,s=p.next,p.next=null,i.lastBaseUpdate=p,i.shared.pending=null}}while(1);if(c===null&&(l=d),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do a|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);gn|=a,e.lanes=a,e.memoizedState=d}}function dc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(b(191,i));i.call(r)}}}var vi={},ct=Jt(vi),ni=Jt(vi),ri=Jt(vi);function dn(e){if(e===vi)throw Error(b(174));return e}function Wl(e,t){switch(W(ri,t),W(ni,e),W(ct,vi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ms(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ms(t,e)}V(ct),W(ct,t)}function nr(){V(ct),V(ni),V(ri)}function Lp(e){dn(ri.current);var t=dn(ct.current),n=ms(t,e.type);t!==n&&(W(ni,e),W(ct,n))}function ql(e){ni.current===e&&(V(ct),V(ni))}var Q=Jt(0);function jo(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ma=[];function Vl(){for(var e=0;e<Ma.length;e++)Ma[e]._workInProgressVersionPrimary=null;Ma.length=0}var ro=St.ReactCurrentDispatcher,Ba=St.ReactCurrentBatchConfig,vn=0,Y=null,ie=null,ue=null,Co=!1,$r=!1,ii=0,Iv=0;function he(){throw Error(b(321))}function Hl(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!nt(e[n],t[n]))return!1;return!0}function Kl(e,t,n,r,i,o){if(vn=o,Y=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ro.current=e===null||e.memoizedState===null?Lv:$v,e=n(r,i),$r){o=0;do{if($r=!1,ii=0,25<=o)throw Error(b(301));o+=1,ue=ie=null,t.updateQueue=null,ro.current=zv,e=n(r,i)}while($r)}if(ro.current=Po,t=ie!==null&&ie.next!==null,vn=0,ue=ie=Y=null,Co=!1,t)throw Error(b(300));return e}function Gl(){var e=ii!==0;return ii=0,e}function at(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ue===null?Y.memoizedState=ue=e:ue=ue.next=e,ue}function Ke(){if(ie===null){var e=Y.alternate;e=e!==null?e.memoizedState:null}else e=ie.next;var t=ue===null?Y.memoizedState:ue.next;if(t!==null)ue=t,ie=e;else{if(e===null)throw Error(b(310));ie=e,e={memoizedState:ie.memoizedState,baseState:ie.baseState,baseQueue:ie.baseQueue,queue:ie.queue,next:null},ue===null?Y.memoizedState=ue=e:ue=ue.next=e}return ue}function oi(e,t){return typeof t=="function"?t(e):t}function Wa(e){var t=Ke(),n=t.queue;if(n===null)throw Error(b(311));n.lastRenderedReducer=e;var r=ie,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var a=i.next;i.next=o.next,o.next=a}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var s=a=null,l=null,u=o;do{var c=u.lane;if((vn&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(s=l=d,a=r):l=l.next=d,Y.lanes|=c,gn|=c}u=u.next}while(u!==null&&u!==o);l===null?a=r:l.next=s,nt(r,t.memoizedState)||(Oe=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,Y.lanes|=o,gn|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function qa(e){var t=Ke(),n=t.queue;if(n===null)throw Error(b(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var a=i=i.next;do o=e(o,a.action),a=a.next;while(a!==i);nt(o,t.memoizedState)||(Oe=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function $p(){}function zp(e,t){var n=Y,r=Ke(),i=t(),o=!nt(r.memoizedState,i);if(o&&(r.memoizedState=i,Oe=!0),r=r.queue,Jl(Mp.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||ue!==null&&ue.memoizedState.tag&1){if(n.flags|=2048,ai(9,Fp.bind(null,n,r,i,t),void 0,null),ce===null)throw Error(b(349));vn&30||Up(n,t,i)}return i}function Up(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Fp(e,t,n,r){t.value=n,t.getSnapshot=r,Bp(t)&&Wp(e)}function Mp(e,t,n){return n(function(){Bp(t)&&Wp(e)})}function Bp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!nt(e,n)}catch{return!0}}function Wp(e){var t=_t(e,1);t!==null&&tt(t,e,1,-1)}function pc(e){var t=at();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:oi,lastRenderedState:e},t.queue=e,e=e.dispatch=Dv.bind(null,Y,e),[t.memoizedState,e]}function ai(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function qp(){return Ke().memoizedState}function io(e,t,n,r){var i=at();Y.flags|=e,i.memoizedState=ai(1|t,n,void 0,r===void 0?null:r)}function Jo(e,t,n,r){var i=Ke();r=r===void 0?null:r;var o=void 0;if(ie!==null){var a=ie.memoizedState;if(o=a.destroy,r!==null&&Hl(r,a.deps)){i.memoizedState=ai(t,n,o,r);return}}Y.flags|=e,i.memoizedState=ai(1|t,n,o,r)}function fc(e,t){return io(8390656,8,e,t)}function Jl(e,t){return Jo(2048,8,e,t)}function Vp(e,t){return Jo(4,2,e,t)}function Hp(e,t){return Jo(4,4,e,t)}function Kp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Gp(e,t,n){return n=n!=null?n.concat([e]):null,Jo(4,4,Kp.bind(null,t,e),n)}function Ql(){}function Jp(e,t){var n=Ke();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Hl(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Qp(e,t){var n=Ke();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Hl(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Yp(e,t,n){return vn&21?(nt(n,t)||(n=np(),Y.lanes|=n,gn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Oe=!0),e.memoizedState=n)}function Av(e,t){var n=U;U=n!==0&&4>n?n:4,e(!0);var r=Ba.transition;Ba.transition={};try{e(!1),t()}finally{U=n,Ba.transition=r}}function Xp(){return Ke().memoizedState}function Nv(e,t,n){var r=Wt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Zp(e))ef(t,n);else if(n=Np(e,t,n,r),n!==null){var i=_e();tt(n,e,r,i),tf(n,t,r)}}function Dv(e,t,n){var r=Wt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Zp(e))ef(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var a=t.lastRenderedState,s=o(a,n);if(i.hasEagerState=!0,i.eagerState=s,nt(s,a)){var l=t.interleaved;l===null?(i.next=i,Ml(t)):(i.next=l.next,l.next=i),t.interleaved=i;return}}catch{}finally{}n=Np(e,t,i,r),n!==null&&(i=_e(),tt(n,e,r,i),tf(n,t,r))}}function Zp(e){var t=e.alternate;return e===Y||t!==null&&t===Y}function ef(e,t){$r=Co=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function tf(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Cl(e,n)}}var Po={readContext:He,useCallback:he,useContext:he,useEffect:he,useImperativeHandle:he,useInsertionEffect:he,useLayoutEffect:he,useMemo:he,useReducer:he,useRef:he,useState:he,useDebugValue:he,useDeferredValue:he,useTransition:he,useMutableSource:he,useSyncExternalStore:he,useId:he,unstable_isNewReconciler:!1},Lv={readContext:He,useCallback:function(e,t){return at().memoizedState=[e,t===void 0?null:t],e},useContext:He,useEffect:fc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,io(4194308,4,Kp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return io(4194308,4,e,t)},useInsertionEffect:function(e,t){return io(4,2,e,t)},useMemo:function(e,t){var n=at();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=at();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Nv.bind(null,Y,e),[r.memoizedState,e]},useRef:function(e){var t=at();return e={current:e},t.memoizedState=e},useState:pc,useDebugValue:Ql,useDeferredValue:function(e){return at().memoizedState=e},useTransition:function(){var e=pc(!1),t=e[0];return e=Av.bind(null,e[1]),at().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Y,i=at();if(K){if(n===void 0)throw Error(b(407));n=n()}else{if(n=t(),ce===null)throw Error(b(349));vn&30||Up(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,fc(Mp.bind(null,r,o,e),[e]),r.flags|=2048,ai(9,Fp.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=at(),t=ce.identifierPrefix;if(K){var n=gt,r=vt;n=(r&~(1<<32-et(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=ii++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Iv++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},$v={readContext:He,useCallback:Jp,useContext:He,useEffect:Jl,useImperativeHandle:Gp,useInsertionEffect:Vp,useLayoutEffect:Hp,useMemo:Qp,useReducer:Wa,useRef:qp,useState:function(){return Wa(oi)},useDebugValue:Ql,useDeferredValue:function(e){var t=Ke();return Yp(t,ie.memoizedState,e)},useTransition:function(){var e=Wa(oi)[0],t=Ke().memoizedState;return[e,t]},useMutableSource:$p,useSyncExternalStore:zp,useId:Xp,unstable_isNewReconciler:!1},zv={readContext:He,useCallback:Jp,useContext:He,useEffect:Jl,useImperativeHandle:Gp,useInsertionEffect:Vp,useLayoutEffect:Hp,useMemo:Qp,useReducer:qa,useRef:qp,useState:function(){return qa(oi)},useDebugValue:Ql,useDeferredValue:function(e){var t=Ke();return ie===null?t.memoizedState=e:Yp(t,ie.memoizedState,e)},useTransition:function(){var e=qa(oi)[0],t=Ke().memoizedState;return[e,t]},useMutableSource:$p,useSyncExternalStore:zp,useId:Xp,unstable_isNewReconciler:!1};function Qe(e,t){if(e&&e.defaultProps){t=X({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Ls(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:X({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Qo={isMounted:function(e){return(e=e._reactInternals)?xn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=_e(),i=Wt(e),o=yt(r,i);o.payload=t,n!=null&&(o.callback=n),t=Mt(e,o,i),t!==null&&(tt(t,e,i,r),no(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=_e(),i=Wt(e),o=yt(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Mt(e,o,i),t!==null&&(tt(t,e,i,r),no(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=_e(),r=Wt(e),i=yt(n,r);i.tag=2,t!=null&&(i.callback=t),t=Mt(e,i,r),t!==null&&(tt(t,e,r,n),no(t,e,r))}};function hc(e,t,n,r,i,o,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,a):t.prototype&&t.prototype.isPureReactComponent?!Xr(n,r)||!Xr(i,o):!0}function nf(e,t,n){var r=!1,i=Kt,o=t.contextType;return typeof o=="object"&&o!==null?o=He(o):(i=Ie(t)?hn:ye.current,r=t.contextTypes,o=(r=r!=null)?Zn(e,i):Kt),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Qo,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function mc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Qo.enqueueReplaceState(t,t.state,null)}function $s(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Bl(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=He(o):(o=Ie(t)?hn:ye.current,i.context=Zn(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Ls(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Qo.enqueueReplaceState(i,i.state,null),bo(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function rr(e,t){try{var n="",r=t;do n+=pm(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function Va(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function zs(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Uv=typeof WeakMap=="function"?WeakMap:Map;function rf(e,t,n){n=yt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Oo||(Oo=!0,Gs=r),zs(e,t)},n}function of(e,t,n){n=yt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){zs(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){zs(e,t),typeof r!="function"&&(Bt===null?Bt=new Set([this]):Bt.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),n}function vc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Uv;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=Zv.bind(null,e,t,n),t.then(e,e))}function gc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function yc(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=yt(-1,1),t.tag=2,Mt(n,t,1))),n.lanes|=1),e)}var Fv=St.ReactCurrentOwner,Oe=!1;function xe(e,t,n,r){t.child=e===null?Ap(t,null,n,r):tr(t,e.child,n,r)}function wc(e,t,n,r,i){n=n.render;var o=t.ref;return Qn(t,i),r=Kl(e,t,n,r,o,i),n=Gl(),e!==null&&!Oe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,kt(e,t,i)):(K&&n&&Dl(t),t.flags|=1,xe(e,t,r,i),t.child)}function xc(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!iu(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,af(e,t,o,r,i)):(e=lo(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var a=o.memoizedProps;if(n=n.compare,n=n!==null?n:Xr,n(a,r)&&e.ref===t.ref)return kt(e,t,i)}return t.flags|=1,e=qt(o,r),e.ref=t.ref,e.return=t,t.child=e}function af(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(Xr(o,r)&&e.ref===t.ref)if(Oe=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(Oe=!0);else return t.lanes=e.lanes,kt(e,t,i)}return Us(e,t,n,r,i)}function sf(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},W(Vn,De),De|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,W(Vn,De),De|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,W(Vn,De),De|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,W(Vn,De),De|=r;return xe(e,t,i,n),t.child}function lf(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Us(e,t,n,r,i){var o=Ie(n)?hn:ye.current;return o=Zn(t,o),Qn(t,i),n=Kl(e,t,n,r,o,i),r=Gl(),e!==null&&!Oe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,kt(e,t,i)):(K&&r&&Dl(t),t.flags|=1,xe(e,t,n,i),t.child)}function _c(e,t,n,r,i){if(Ie(n)){var o=!0;xo(t)}else o=!1;if(Qn(t,i),t.stateNode===null)oo(e,t),nf(t,n,r),$s(t,n,r,i),r=!0;else if(e===null){var a=t.stateNode,s=t.memoizedProps;a.props=s;var l=a.context,u=n.contextType;typeof u=="object"&&u!==null?u=He(u):(u=Ie(n)?hn:ye.current,u=Zn(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof a.getSnapshotBeforeUpdate=="function";d||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(s!==r||l!==u)&&mc(t,a,r,u),Pt=!1;var p=t.memoizedState;a.state=p,bo(t,r,a,i),l=t.memoizedState,s!==r||p!==l||Re.current||Pt?(typeof c=="function"&&(Ls(t,n,c,r),l=t.memoizedState),(s=Pt||hc(t,n,s,r,p,l,u))?(d||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),a.props=r,a.state=l,a.context=u,r=s):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Dp(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:Qe(t.type,s),a.props=u,d=t.pendingProps,p=a.context,l=n.contextType,typeof l=="object"&&l!==null?l=He(l):(l=Ie(n)?hn:ye.current,l=Zn(t,l));var g=n.getDerivedStateFromProps;(c=typeof g=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(s!==d||p!==l)&&mc(t,a,r,l),Pt=!1,p=t.memoizedState,a.state=p,bo(t,r,a,i);var y=t.memoizedState;s!==d||p!==y||Re.current||Pt?(typeof g=="function"&&(Ls(t,n,g,r),y=t.memoizedState),(u=Pt||hc(t,n,u,r,p,y,l)||!1)?(c||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,y,l),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,y,l)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),a.props=r,a.state=y,a.context=l,r=u):(typeof a.componentDidUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return Fs(e,t,n,r,o,i)}function Fs(e,t,n,r,i,o){lf(e,t);var a=(t.flags&128)!==0;if(!r&&!a)return i&&ac(t,n,!1),kt(e,t,o);r=t.stateNode,Fv.current=t;var s=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&a?(t.child=tr(t,e.child,null,o),t.child=tr(t,null,s,o)):xe(e,t,s,o),t.memoizedState=r.state,i&&ac(t,n,!0),t.child}function uf(e){var t=e.stateNode;t.pendingContext?oc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&oc(e,t.context,!1),Wl(e,t.containerInfo)}function kc(e,t,n,r,i){return er(),$l(i),t.flags|=256,xe(e,t,n,r),t.child}var Ms={dehydrated:null,treeContext:null,retryLane:0};function Bs(e){return{baseLanes:e,cachePool:null,transitions:null}}function cf(e,t,n){var r=t.pendingProps,i=Q.current,o=!1,a=(t.flags&128)!==0,s;if((s=a)||(s=e!==null&&e.memoizedState===null?!1:(i&2)!==0),s?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),W(Q,i&1),e===null)return Ns(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(a=r.children,e=r.fallback,o?(r=t.mode,o=t.child,a={mode:"hidden",children:a},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=a):o=Zo(a,r,0,null),e=fn(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Bs(n),t.memoizedState=Ms,e):Yl(t,a));if(i=e.memoizedState,i!==null&&(s=i.dehydrated,s!==null))return Mv(e,t,a,r,s,i,n);if(o){o=r.fallback,a=t.mode,i=e.child,s=i.sibling;var l={mode:"hidden",children:r.children};return!(a&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=qt(i,l),r.subtreeFlags=i.subtreeFlags&14680064),s!==null?o=qt(s,o):(o=fn(o,a,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,a=e.child.memoizedState,a=a===null?Bs(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},o.memoizedState=a,o.childLanes=e.childLanes&~n,t.memoizedState=Ms,r}return o=e.child,e=o.sibling,r=qt(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Yl(e,t){return t=Zo({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ui(e,t,n,r){return r!==null&&$l(r),tr(t,e.child,null,n),e=Yl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Mv(e,t,n,r,i,o,a){if(n)return t.flags&256?(t.flags&=-257,r=Va(Error(b(422))),Ui(e,t,a,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=Zo({mode:"visible",children:r.children},i,0,null),o=fn(o,i,a,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&tr(t,e.child,null,a),t.child.memoizedState=Bs(a),t.memoizedState=Ms,o);if(!(t.mode&1))return Ui(e,t,a,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var s=r.dgst;return r=s,o=Error(b(419)),r=Va(o,r,void 0),Ui(e,t,a,r)}if(s=(a&e.childLanes)!==0,Oe||s){if(r=ce,r!==null){switch(a&-a){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|a)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,_t(e,i),tt(r,e,i,-1))}return ru(),r=Va(Error(b(421))),Ui(e,t,a,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=eg.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,Le=Ft(i.nextSibling),$e=t,K=!0,Ze=null,e!==null&&(Me[Be++]=vt,Me[Be++]=gt,Me[Be++]=mn,vt=e.id,gt=e.overflow,mn=t),t=Yl(t,r.children),t.flags|=4096,t)}function Sc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ds(e.return,t,n)}function Ha(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function df(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(xe(e,t,r.children,n),r=Q.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Sc(e,n,t);else if(e.tag===19)Sc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(W(Q,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&jo(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Ha(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&jo(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Ha(t,!0,n,null,o);break;case"together":Ha(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function oo(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function kt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),gn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(b(153));if(t.child!==null){for(e=t.child,n=qt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=qt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Bv(e,t,n){switch(t.tag){case 3:uf(t),er();break;case 5:Lp(t);break;case 1:Ie(t.type)&&xo(t);break;case 4:Wl(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;W(So,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(W(Q,Q.current&1),t.flags|=128,null):n&t.child.childLanes?cf(e,t,n):(W(Q,Q.current&1),e=kt(e,t,n),e!==null?e.sibling:null);W(Q,Q.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return df(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),W(Q,Q.current),r)break;return null;case 22:case 23:return t.lanes=0,sf(e,t,n)}return kt(e,t,n)}var pf,Ws,ff,hf;pf=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Ws=function(){};ff=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,dn(ct.current);var o=null;switch(n){case"input":i=ds(e,i),r=ds(e,r),o=[];break;case"select":i=X({},i,{value:void 0}),r=X({},r,{value:void 0}),o=[];break;case"textarea":i=hs(e,i),r=hs(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=yo)}vs(n,r);var a;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var s=i[u];for(a in s)s.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Vr.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var l=r[u];if(s=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==s&&(l!=null||s!=null))if(u==="style")if(s){for(a in s)!s.hasOwnProperty(a)||l&&l.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in l)l.hasOwnProperty(a)&&s[a]!==l[a]&&(n||(n={}),n[a]=l[a])}else n||(o||(o=[]),o.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,s=s?s.__html:void 0,l!=null&&s!==l&&(o=o||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(o=o||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Vr.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&q("scroll",e),o||s===l||(o=[])):(o=o||[]).push(u,l))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};hf=function(e,t,n,r){n!==r&&(t.flags|=4)};function Er(e,t){if(!K)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function me(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Wv(e,t,n){var r=t.pendingProps;switch(Ll(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return me(t),null;case 1:return Ie(t.type)&&wo(),me(t),null;case 3:return r=t.stateNode,nr(),V(Re),V(ye),Vl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&($i(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ze!==null&&(Ys(Ze),Ze=null))),Ws(e,t),me(t),null;case 5:ql(t);var i=dn(ri.current);if(n=t.type,e!==null&&t.stateNode!=null)ff(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(b(166));return me(t),null}if(e=dn(ct.current),$i(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[st]=t,r[ti]=o,e=(t.mode&1)!==0,n){case"dialog":q("cancel",r),q("close",r);break;case"iframe":case"object":case"embed":q("load",r);break;case"video":case"audio":for(i=0;i<Or.length;i++)q(Or[i],r);break;case"source":q("error",r);break;case"img":case"image":case"link":q("error",r),q("load",r);break;case"details":q("toggle",r);break;case"input":Iu(r,o),q("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},q("invalid",r);break;case"textarea":Nu(r,o),q("invalid",r)}vs(n,o),i=null;for(var a in o)if(o.hasOwnProperty(a)){var s=o[a];a==="children"?typeof s=="string"?r.textContent!==s&&(o.suppressHydrationWarning!==!0&&Li(r.textContent,s,e),i=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(o.suppressHydrationWarning!==!0&&Li(r.textContent,s,e),i=["children",""+s]):Vr.hasOwnProperty(a)&&s!=null&&a==="onScroll"&&q("scroll",r)}switch(n){case"input":Pi(r),Au(r,o,!0);break;case"textarea":Pi(r),Du(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=yo)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{a=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Md(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=a.createElement(n,{is:r.is}):(e=a.createElement(n),n==="select"&&(a=e,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):e=a.createElementNS(e,n),e[st]=t,e[ti]=r,pf(e,t,!1,!1),t.stateNode=e;e:{switch(a=gs(n,r),n){case"dialog":q("cancel",e),q("close",e),i=r;break;case"iframe":case"object":case"embed":q("load",e),i=r;break;case"video":case"audio":for(i=0;i<Or.length;i++)q(Or[i],e);i=r;break;case"source":q("error",e),i=r;break;case"img":case"image":case"link":q("error",e),q("load",e),i=r;break;case"details":q("toggle",e),i=r;break;case"input":Iu(e,r),i=ds(e,r),q("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=X({},r,{value:void 0}),q("invalid",e);break;case"textarea":Nu(e,r),i=hs(e,r),q("invalid",e);break;default:i=r}vs(n,i),s=i;for(o in s)if(s.hasOwnProperty(o)){var l=s[o];o==="style"?qd(e,l):o==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Bd(e,l)):o==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Hr(e,l):typeof l=="number"&&Hr(e,""+l):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Vr.hasOwnProperty(o)?l!=null&&o==="onScroll"&&q("scroll",e):l!=null&&_l(e,o,l,a))}switch(n){case"input":Pi(e),Au(e,r,!1);break;case"textarea":Pi(e),Du(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Ht(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?Hn(e,!!r.multiple,o,!1):r.defaultValue!=null&&Hn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=yo)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return me(t),null;case 6:if(e&&t.stateNode!=null)hf(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(b(166));if(n=dn(ri.current),dn(ct.current),$i(t)){if(r=t.stateNode,n=t.memoizedProps,r[st]=t,(o=r.nodeValue!==n)&&(e=$e,e!==null))switch(e.tag){case 3:Li(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Li(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[st]=t,t.stateNode=r}return me(t),null;case 13:if(V(Q),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(K&&Le!==null&&t.mode&1&&!(t.flags&128))Rp(),er(),t.flags|=98560,o=!1;else if(o=$i(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(b(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(b(317));o[st]=t}else er(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;me(t),o=!1}else Ze!==null&&(Ys(Ze),Ze=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Q.current&1?oe===0&&(oe=3):ru())),t.updateQueue!==null&&(t.flags|=4),me(t),null);case 4:return nr(),Ws(e,t),e===null&&Zr(t.stateNode.containerInfo),me(t),null;case 10:return Fl(t.type._context),me(t),null;case 17:return Ie(t.type)&&wo(),me(t),null;case 19:if(V(Q),o=t.memoizedState,o===null)return me(t),null;if(r=(t.flags&128)!==0,a=o.rendering,a===null)if(r)Er(o,!1);else{if(oe!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(a=jo(e),a!==null){for(t.flags|=128,Er(o,!1),r=a.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,a=o.alternate,a===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=a.childLanes,o.lanes=a.lanes,o.child=a.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=a.memoizedProps,o.memoizedState=a.memoizedState,o.updateQueue=a.updateQueue,o.type=a.type,e=a.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return W(Q,Q.current&1|2),t.child}e=e.sibling}o.tail!==null&&ee()>ir&&(t.flags|=128,r=!0,Er(o,!1),t.lanes=4194304)}else{if(!r)if(e=jo(a),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Er(o,!0),o.tail===null&&o.tailMode==="hidden"&&!a.alternate&&!K)return me(t),null}else 2*ee()-o.renderingStartTime>ir&&n!==1073741824&&(t.flags|=128,r=!0,Er(o,!1),t.lanes=4194304);o.isBackwards?(a.sibling=t.child,t.child=a):(n=o.last,n!==null?n.sibling=a:t.child=a,o.last=a)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=ee(),t.sibling=null,n=Q.current,W(Q,r?n&1|2:n&1),t):(me(t),null);case 22:case 23:return nu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?De&1073741824&&(me(t),t.subtreeFlags&6&&(t.flags|=8192)):me(t),null;case 24:return null;case 25:return null}throw Error(b(156,t.tag))}function qv(e,t){switch(Ll(t),t.tag){case 1:return Ie(t.type)&&wo(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return nr(),V(Re),V(ye),Vl(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return ql(t),null;case 13:if(V(Q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(b(340));er()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return V(Q),null;case 4:return nr(),null;case 10:return Fl(t.type._context),null;case 22:case 23:return nu(),null;case 24:return null;default:return null}}var Fi=!1,ge=!1,Vv=typeof WeakSet=="function"?WeakSet:Set,T=null;function qn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Z(e,t,r)}else n.current=null}function qs(e,t,n){try{n()}catch(r){Z(e,t,r)}}var Ec=!1;function Hv(e,t){if(Cs=mo,e=yp(),Nl(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var a=0,s=-1,l=-1,u=0,c=0,d=e,p=null;t:for(;;){for(var g;d!==n||i!==0&&d.nodeType!==3||(s=a+i),d!==o||r!==0&&d.nodeType!==3||(l=a+r),d.nodeType===3&&(a+=d.nodeValue.length),(g=d.firstChild)!==null;)p=d,d=g;for(;;){if(d===e)break t;if(p===n&&++u===i&&(s=a),p===o&&++c===r&&(l=a),(g=d.nextSibling)!==null)break;d=p,p=d.parentNode}d=g}n=s===-1||l===-1?null:{start:s,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ps={focusedElem:e,selectionRange:n},mo=!1,T=t;T!==null;)if(t=T,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,T=e;else for(;T!==null;){t=T;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var w=y.memoizedProps,_=y.memoizedState,m=t.stateNode,h=m.getSnapshotBeforeUpdate(t.elementType===t.type?w:Qe(t.type,w),_);m.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var f=t.stateNode.containerInfo;f.nodeType===1?f.textContent="":f.nodeType===9&&f.documentElement&&f.removeChild(f.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(b(163))}}catch(x){Z(t,t.return,x)}if(e=t.sibling,e!==null){e.return=t.return,T=e;break}T=t.return}return y=Ec,Ec=!1,y}function zr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&qs(t,n,o)}i=i.next}while(i!==r)}}function Yo(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Vs(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function mf(e){var t=e.alternate;t!==null&&(e.alternate=null,mf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[st],delete t[ti],delete t[Rs],delete t[Pv],delete t[Tv])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function vf(e){return e.tag===5||e.tag===3||e.tag===4}function bc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||vf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Hs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=yo));else if(r!==4&&(e=e.child,e!==null))for(Hs(e,t,n),e=e.sibling;e!==null;)Hs(e,t,n),e=e.sibling}function Ks(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Ks(e,t,n),e=e.sibling;e!==null;)Ks(e,t,n),e=e.sibling}var de=null,Ye=!1;function Et(e,t,n){for(n=n.child;n!==null;)gf(e,t,n),n=n.sibling}function gf(e,t,n){if(ut&&typeof ut.onCommitFiberUnmount=="function")try{ut.onCommitFiberUnmount(Wo,n)}catch{}switch(n.tag){case 5:ge||qn(n,t);case 6:var r=de,i=Ye;de=null,Et(e,t,n),de=r,Ye=i,de!==null&&(Ye?(e=de,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):de.removeChild(n.stateNode));break;case 18:de!==null&&(Ye?(e=de,n=n.stateNode,e.nodeType===8?Ua(e.parentNode,n):e.nodeType===1&&Ua(e,n),Qr(e)):Ua(de,n.stateNode));break;case 4:r=de,i=Ye,de=n.stateNode.containerInfo,Ye=!0,Et(e,t,n),de=r,Ye=i;break;case 0:case 11:case 14:case 15:if(!ge&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,a=o.destroy;o=o.tag,a!==void 0&&(o&2||o&4)&&qs(n,t,a),i=i.next}while(i!==r)}Et(e,t,n);break;case 1:if(!ge&&(qn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){Z(n,t,s)}Et(e,t,n);break;case 21:Et(e,t,n);break;case 22:n.mode&1?(ge=(r=ge)||n.memoizedState!==null,Et(e,t,n),ge=r):Et(e,t,n);break;default:Et(e,t,n)}}function jc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Vv),t.forEach(function(r){var i=tg.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Ge(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,a=t,s=a;e:for(;s!==null;){switch(s.tag){case 5:de=s.stateNode,Ye=!1;break e;case 3:de=s.stateNode.containerInfo,Ye=!0;break e;case 4:de=s.stateNode.containerInfo,Ye=!0;break e}s=s.return}if(de===null)throw Error(b(160));gf(o,a,i),de=null,Ye=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){Z(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)yf(t,e),t=t.sibling}function yf(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ge(t,e),it(e),r&4){try{zr(3,e,e.return),Yo(3,e)}catch(w){Z(e,e.return,w)}try{zr(5,e,e.return)}catch(w){Z(e,e.return,w)}}break;case 1:Ge(t,e),it(e),r&512&&n!==null&&qn(n,n.return);break;case 5:if(Ge(t,e),it(e),r&512&&n!==null&&qn(n,n.return),e.flags&32){var i=e.stateNode;try{Hr(i,"")}catch(w){Z(e,e.return,w)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,a=n!==null?n.memoizedProps:o,s=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{s==="input"&&o.type==="radio"&&o.name!=null&&Ud(i,o),gs(s,a);var u=gs(s,o);for(a=0;a<l.length;a+=2){var c=l[a],d=l[a+1];c==="style"?qd(i,d):c==="dangerouslySetInnerHTML"?Bd(i,d):c==="children"?Hr(i,d):_l(i,c,d,u)}switch(s){case"input":ps(i,o);break;case"textarea":Fd(i,o);break;case"select":var p=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var g=o.value;g!=null?Hn(i,!!o.multiple,g,!1):p!==!!o.multiple&&(o.defaultValue!=null?Hn(i,!!o.multiple,o.defaultValue,!0):Hn(i,!!o.multiple,o.multiple?[]:"",!1))}i[ti]=o}catch(w){Z(e,e.return,w)}}break;case 6:if(Ge(t,e),it(e),r&4){if(e.stateNode===null)throw Error(b(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(w){Z(e,e.return,w)}}break;case 3:if(Ge(t,e),it(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Qr(t.containerInfo)}catch(w){Z(e,e.return,w)}break;case 4:Ge(t,e),it(e);break;case 13:Ge(t,e),it(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(eu=ee())),r&4&&jc(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(ge=(u=ge)||c,Ge(t,e),ge=u):Ge(t,e),it(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(T=e,c=e.child;c!==null;){for(d=T=c;T!==null;){switch(p=T,g=p.child,p.tag){case 0:case 11:case 14:case 15:zr(4,p,p.return);break;case 1:qn(p,p.return);var y=p.stateNode;if(typeof y.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(w){Z(r,n,w)}}break;case 5:qn(p,p.return);break;case 22:if(p.memoizedState!==null){Pc(d);continue}}g!==null?(g.return=p,T=g):Pc(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{i=d.stateNode,u?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(s=d.stateNode,l=d.memoizedProps.style,a=l!=null&&l.hasOwnProperty("display")?l.display:null,s.style.display=Wd("display",a))}catch(w){Z(e,e.return,w)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(w){Z(e,e.return,w)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:Ge(t,e),it(e),r&4&&jc(e);break;case 21:break;default:Ge(t,e),it(e)}}function it(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(vf(n)){var r=n;break e}n=n.return}throw Error(b(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Hr(i,""),r.flags&=-33);var o=bc(e);Ks(e,o,i);break;case 3:case 4:var a=r.stateNode.containerInfo,s=bc(e);Hs(e,s,a);break;default:throw Error(b(161))}}catch(l){Z(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Kv(e,t,n){T=e,wf(e)}function wf(e,t,n){for(var r=(e.mode&1)!==0;T!==null;){var i=T,o=i.child;if(i.tag===22&&r){var a=i.memoizedState!==null||Fi;if(!a){var s=i.alternate,l=s!==null&&s.memoizedState!==null||ge;s=Fi;var u=ge;if(Fi=a,(ge=l)&&!u)for(T=i;T!==null;)a=T,l=a.child,a.tag===22&&a.memoizedState!==null?Tc(i):l!==null?(l.return=a,T=l):Tc(i);for(;o!==null;)T=o,wf(o),o=o.sibling;T=i,Fi=s,ge=u}Cc(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,T=o):Cc(e)}}function Cc(e){for(;T!==null;){var t=T;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ge||Yo(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ge)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Qe(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&dc(t,o,r);break;case 3:var a=t.updateQueue;if(a!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}dc(t,a,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&Qr(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(b(163))}ge||t.flags&512&&Vs(t)}catch(p){Z(t,t.return,p)}}if(t===e){T=null;break}if(n=t.sibling,n!==null){n.return=t.return,T=n;break}T=t.return}}function Pc(e){for(;T!==null;){var t=T;if(t===e){T=null;break}var n=t.sibling;if(n!==null){n.return=t.return,T=n;break}T=t.return}}function Tc(e){for(;T!==null;){var t=T;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Yo(4,t)}catch(l){Z(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(l){Z(t,i,l)}}var o=t.return;try{Vs(t)}catch(l){Z(t,o,l)}break;case 5:var a=t.return;try{Vs(t)}catch(l){Z(t,a,l)}}}catch(l){Z(t,t.return,l)}if(t===e){T=null;break}var s=t.sibling;if(s!==null){s.return=t.return,T=s;break}T=t.return}}var Gv=Math.ceil,To=St.ReactCurrentDispatcher,Xl=St.ReactCurrentOwner,qe=St.ReactCurrentBatchConfig,z=0,ce=null,re=null,pe=0,De=0,Vn=Jt(0),oe=0,si=null,gn=0,Xo=0,Zl=0,Ur=null,Pe=null,eu=0,ir=1/0,ft=null,Oo=!1,Gs=null,Bt=null,Mi=!1,Nt=null,Ro=0,Fr=0,Js=null,ao=-1,so=0;function _e(){return z&6?ee():ao!==-1?ao:ao=ee()}function Wt(e){return e.mode&1?z&2&&pe!==0?pe&-pe:Rv.transition!==null?(so===0&&(so=np()),so):(e=U,e!==0||(e=window.event,e=e===void 0?16:up(e.type)),e):1}function tt(e,t,n,r){if(50<Fr)throw Fr=0,Js=null,Error(b(185));fi(e,n,r),(!(z&2)||e!==ce)&&(e===ce&&(!(z&2)&&(Xo|=n),oe===4&&Rt(e,pe)),Ae(e,r),n===1&&z===0&&!(t.mode&1)&&(ir=ee()+500,Go&&Qt()))}function Ae(e,t){var n=e.callbackNode;Rm(e,t);var r=ho(e,e===ce?pe:0);if(r===0)n!==null&&zu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&zu(n),t===1)e.tag===0?Ov(Oc.bind(null,e)):Pp(Oc.bind(null,e)),jv(function(){!(z&6)&&Qt()}),n=null;else{switch(rp(r)){case 1:n=jl;break;case 4:n=ep;break;case 16:n=fo;break;case 536870912:n=tp;break;default:n=fo}n=Cf(n,xf.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function xf(e,t){if(ao=-1,so=0,z&6)throw Error(b(327));var n=e.callbackNode;if(Yn()&&e.callbackNode!==n)return null;var r=ho(e,e===ce?pe:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Io(e,r);else{t=r;var i=z;z|=2;var o=kf();(ce!==e||pe!==t)&&(ft=null,ir=ee()+500,pn(e,t));do try{Yv();break}catch(s){_f(e,s)}while(1);Ul(),To.current=o,z=i,re!==null?t=0:(ce=null,pe=0,t=oe)}if(t!==0){if(t===2&&(i=ks(e),i!==0&&(r=i,t=Qs(e,i))),t===1)throw n=si,pn(e,0),Rt(e,r),Ae(e,ee()),n;if(t===6)Rt(e,r);else{if(i=e.current.alternate,!(r&30)&&!Jv(i)&&(t=Io(e,r),t===2&&(o=ks(e),o!==0&&(r=o,t=Qs(e,o))),t===1))throw n=si,pn(e,0),Rt(e,r),Ae(e,ee()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(b(345));case 2:on(e,Pe,ft);break;case 3:if(Rt(e,r),(r&130023424)===r&&(t=eu+500-ee(),10<t)){if(ho(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){_e(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Os(on.bind(null,e,Pe,ft),t);break}on(e,Pe,ft);break;case 4:if(Rt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var a=31-et(r);o=1<<a,a=t[a],a>i&&(i=a),r&=~o}if(r=i,r=ee()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Gv(r/1960))-r,10<r){e.timeoutHandle=Os(on.bind(null,e,Pe,ft),r);break}on(e,Pe,ft);break;case 5:on(e,Pe,ft);break;default:throw Error(b(329))}}}return Ae(e,ee()),e.callbackNode===n?xf.bind(null,e):null}function Qs(e,t){var n=Ur;return e.current.memoizedState.isDehydrated&&(pn(e,t).flags|=256),e=Io(e,t),e!==2&&(t=Pe,Pe=n,t!==null&&Ys(t)),e}function Ys(e){Pe===null?Pe=e:Pe.push.apply(Pe,e)}function Jv(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!nt(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Rt(e,t){for(t&=~Zl,t&=~Xo,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-et(t),r=1<<n;e[n]=-1,t&=~r}}function Oc(e){if(z&6)throw Error(b(327));Yn();var t=ho(e,0);if(!(t&1))return Ae(e,ee()),null;var n=Io(e,t);if(e.tag!==0&&n===2){var r=ks(e);r!==0&&(t=r,n=Qs(e,r))}if(n===1)throw n=si,pn(e,0),Rt(e,t),Ae(e,ee()),n;if(n===6)throw Error(b(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,on(e,Pe,ft),Ae(e,ee()),null}function tu(e,t){var n=z;z|=1;try{return e(t)}finally{z=n,z===0&&(ir=ee()+500,Go&&Qt())}}function yn(e){Nt!==null&&Nt.tag===0&&!(z&6)&&Yn();var t=z;z|=1;var n=qe.transition,r=U;try{if(qe.transition=null,U=1,e)return e()}finally{U=r,qe.transition=n,z=t,!(z&6)&&Qt()}}function nu(){De=Vn.current,V(Vn)}function pn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,bv(n)),re!==null)for(n=re.return;n!==null;){var r=n;switch(Ll(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&wo();break;case 3:nr(),V(Re),V(ye),Vl();break;case 5:ql(r);break;case 4:nr();break;case 13:V(Q);break;case 19:V(Q);break;case 10:Fl(r.type._context);break;case 22:case 23:nu()}n=n.return}if(ce=e,re=e=qt(e.current,null),pe=De=t,oe=0,si=null,Zl=Xo=gn=0,Pe=Ur=null,cn!==null){for(t=0;t<cn.length;t++)if(n=cn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var a=o.next;o.next=i,r.next=a}n.pending=r}cn=null}return e}function _f(e,t){do{var n=re;try{if(Ul(),ro.current=Po,Co){for(var r=Y.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}Co=!1}if(vn=0,ue=ie=Y=null,$r=!1,ii=0,Xl.current=null,n===null||n.return===null){oe=1,si=t,re=null;break}e:{var o=e,a=n.return,s=n,l=t;if(t=pe,s.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=s,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var p=c.alternate;p?(c.updateQueue=p.updateQueue,c.memoizedState=p.memoizedState,c.lanes=p.lanes):(c.updateQueue=null,c.memoizedState=null)}var g=gc(a);if(g!==null){g.flags&=-257,yc(g,a,s,o,t),g.mode&1&&vc(o,u,t),t=g,l=u;var y=t.updateQueue;if(y===null){var w=new Set;w.add(l),t.updateQueue=w}else y.add(l);break e}else{if(!(t&1)){vc(o,u,t),ru();break e}l=Error(b(426))}}else if(K&&s.mode&1){var _=gc(a);if(_!==null){!(_.flags&65536)&&(_.flags|=256),yc(_,a,s,o,t),$l(rr(l,s));break e}}o=l=rr(l,s),oe!==4&&(oe=2),Ur===null?Ur=[o]:Ur.push(o),o=a;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var m=rf(o,l,t);cc(o,m);break e;case 1:s=l;var h=o.type,f=o.stateNode;if(!(o.flags&128)&&(typeof h.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(Bt===null||!Bt.has(f)))){o.flags|=65536,t&=-t,o.lanes|=t;var x=of(o,s,t);cc(o,x);break e}}o=o.return}while(o!==null)}Ef(n)}catch(S){t=S,re===n&&n!==null&&(re=n=n.return);continue}break}while(1)}function kf(){var e=To.current;return To.current=Po,e===null?Po:e}function ru(){(oe===0||oe===3||oe===2)&&(oe=4),ce===null||!(gn&268435455)&&!(Xo&268435455)||Rt(ce,pe)}function Io(e,t){var n=z;z|=2;var r=kf();(ce!==e||pe!==t)&&(ft=null,pn(e,t));do try{Qv();break}catch(i){_f(e,i)}while(1);if(Ul(),z=n,To.current=r,re!==null)throw Error(b(261));return ce=null,pe=0,oe}function Qv(){for(;re!==null;)Sf(re)}function Yv(){for(;re!==null&&!km();)Sf(re)}function Sf(e){var t=jf(e.alternate,e,De);e.memoizedProps=e.pendingProps,t===null?Ef(e):re=t,Xl.current=null}function Ef(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=qv(n,t),n!==null){n.flags&=32767,re=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{oe=6,re=null;return}}else if(n=Wv(n,t,De),n!==null){re=n;return}if(t=t.sibling,t!==null){re=t;return}re=t=e}while(t!==null);oe===0&&(oe=5)}function on(e,t,n){var r=U,i=qe.transition;try{qe.transition=null,U=1,Xv(e,t,n,r)}finally{qe.transition=i,U=r}return null}function Xv(e,t,n,r){do Yn();while(Nt!==null);if(z&6)throw Error(b(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(b(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Im(e,o),e===ce&&(re=ce=null,pe=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Mi||(Mi=!0,Cf(fo,function(){return Yn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=qe.transition,qe.transition=null;var a=U;U=1;var s=z;z|=4,Xl.current=null,Hv(e,n),yf(n,e),yv(Ps),mo=!!Cs,Ps=Cs=null,e.current=n,Kv(n),Sm(),z=s,U=a,qe.transition=o}else e.current=n;if(Mi&&(Mi=!1,Nt=e,Ro=i),o=e.pendingLanes,o===0&&(Bt=null),jm(n.stateNode),Ae(e,ee()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Oo)throw Oo=!1,e=Gs,Gs=null,e;return Ro&1&&e.tag!==0&&Yn(),o=e.pendingLanes,o&1?e===Js?Fr++:(Fr=0,Js=e):Fr=0,Qt(),null}function Yn(){if(Nt!==null){var e=rp(Ro),t=qe.transition,n=U;try{if(qe.transition=null,U=16>e?16:e,Nt===null)var r=!1;else{if(e=Nt,Nt=null,Ro=0,z&6)throw Error(b(331));var i=z;for(z|=4,T=e.current;T!==null;){var o=T,a=o.child;if(T.flags&16){var s=o.deletions;if(s!==null){for(var l=0;l<s.length;l++){var u=s[l];for(T=u;T!==null;){var c=T;switch(c.tag){case 0:case 11:case 15:zr(8,c,o)}var d=c.child;if(d!==null)d.return=c,T=d;else for(;T!==null;){c=T;var p=c.sibling,g=c.return;if(mf(c),c===u){T=null;break}if(p!==null){p.return=g,T=p;break}T=g}}}var y=o.alternate;if(y!==null){var w=y.child;if(w!==null){y.child=null;do{var _=w.sibling;w.sibling=null,w=_}while(w!==null)}}T=o}}if(o.subtreeFlags&2064&&a!==null)a.return=o,T=a;else e:for(;T!==null;){if(o=T,o.flags&2048)switch(o.tag){case 0:case 11:case 15:zr(9,o,o.return)}var m=o.sibling;if(m!==null){m.return=o.return,T=m;break e}T=o.return}}var h=e.current;for(T=h;T!==null;){a=T;var f=a.child;if(a.subtreeFlags&2064&&f!==null)f.return=a,T=f;else e:for(a=h;T!==null;){if(s=T,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:Yo(9,s)}}catch(S){Z(s,s.return,S)}if(s===a){T=null;break e}var x=s.sibling;if(x!==null){x.return=s.return,T=x;break e}T=s.return}}if(z=i,Qt(),ut&&typeof ut.onPostCommitFiberRoot=="function")try{ut.onPostCommitFiberRoot(Wo,e)}catch{}r=!0}return r}finally{U=n,qe.transition=t}}return!1}function Rc(e,t,n){t=rr(n,t),t=rf(e,t,1),e=Mt(e,t,1),t=_e(),e!==null&&(fi(e,1,t),Ae(e,t))}function Z(e,t,n){if(e.tag===3)Rc(e,e,n);else for(;t!==null;){if(t.tag===3){Rc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Bt===null||!Bt.has(r))){e=rr(n,e),e=of(t,e,1),t=Mt(t,e,1),e=_e(),t!==null&&(fi(t,1,e),Ae(t,e));break}}t=t.return}}function Zv(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=_e(),e.pingedLanes|=e.suspendedLanes&n,ce===e&&(pe&n)===n&&(oe===4||oe===3&&(pe&130023424)===pe&&500>ee()-eu?pn(e,0):Zl|=n),Ae(e,t)}function bf(e,t){t===0&&(e.mode&1?(t=Ri,Ri<<=1,!(Ri&130023424)&&(Ri=4194304)):t=1);var n=_e();e=_t(e,t),e!==null&&(fi(e,t,n),Ae(e,n))}function eg(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),bf(e,n)}function tg(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(b(314))}r!==null&&r.delete(t),bf(e,n)}var jf;jf=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Re.current)Oe=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Oe=!1,Bv(e,t,n);Oe=!!(e.flags&131072)}else Oe=!1,K&&t.flags&1048576&&Tp(t,ko,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;oo(e,t),e=t.pendingProps;var i=Zn(t,ye.current);Qn(t,n),i=Kl(null,t,r,e,i,n);var o=Gl();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ie(r)?(o=!0,xo(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Bl(t),i.updater=Qo,t.stateNode=i,i._reactInternals=t,$s(t,r,e,n),t=Fs(null,t,r,!0,o,n)):(t.tag=0,K&&o&&Dl(t),xe(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(oo(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=rg(r),e=Qe(r,e),i){case 0:t=Us(null,t,r,e,n);break e;case 1:t=_c(null,t,r,e,n);break e;case 11:t=wc(null,t,r,e,n);break e;case 14:t=xc(null,t,r,Qe(r.type,e),n);break e}throw Error(b(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Qe(r,i),Us(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Qe(r,i),_c(e,t,r,i,n);case 3:e:{if(uf(t),e===null)throw Error(b(387));r=t.pendingProps,o=t.memoizedState,i=o.element,Dp(e,t),bo(t,r,null,n);var a=t.memoizedState;if(r=a.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=rr(Error(b(423)),t),t=kc(e,t,r,n,i);break e}else if(r!==i){i=rr(Error(b(424)),t),t=kc(e,t,r,n,i);break e}else for(Le=Ft(t.stateNode.containerInfo.firstChild),$e=t,K=!0,Ze=null,n=Ap(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(er(),r===i){t=kt(e,t,n);break e}xe(e,t,r,n)}t=t.child}return t;case 5:return Lp(t),e===null&&Ns(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,a=i.children,Ts(r,i)?a=null:o!==null&&Ts(r,o)&&(t.flags|=32),lf(e,t),xe(e,t,a,n),t.child;case 6:return e===null&&Ns(t),null;case 13:return cf(e,t,n);case 4:return Wl(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=tr(t,null,r,n):xe(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Qe(r,i),wc(e,t,r,i,n);case 7:return xe(e,t,t.pendingProps,n),t.child;case 8:return xe(e,t,t.pendingProps.children,n),t.child;case 12:return xe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,a=i.value,W(So,r._currentValue),r._currentValue=a,o!==null)if(nt(o.value,a)){if(o.children===i.children&&!Re.current){t=kt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var s=o.dependencies;if(s!==null){a=o.child;for(var l=s.firstContext;l!==null;){if(l.context===r){if(o.tag===1){l=yt(-1,n&-n),l.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),Ds(o.return,n,t),s.lanes|=n;break}l=l.next}}else if(o.tag===10)a=o.type===t.type?null:o.child;else if(o.tag===18){if(a=o.return,a===null)throw Error(b(341));a.lanes|=n,s=a.alternate,s!==null&&(s.lanes|=n),Ds(a,n,t),a=o.sibling}else a=o.child;if(a!==null)a.return=o;else for(a=o;a!==null;){if(a===t){a=null;break}if(o=a.sibling,o!==null){o.return=a.return,a=o;break}a=a.return}o=a}xe(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Qn(t,n),i=He(i),r=r(i),t.flags|=1,xe(e,t,r,n),t.child;case 14:return r=t.type,i=Qe(r,t.pendingProps),i=Qe(r.type,i),xc(e,t,r,i,n);case 15:return af(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Qe(r,i),oo(e,t),t.tag=1,Ie(r)?(e=!0,xo(t)):e=!1,Qn(t,n),nf(t,r,i),$s(t,r,i,n),Fs(null,t,r,!0,e,n);case 19:return df(e,t,n);case 22:return sf(e,t,n)}throw Error(b(156,t.tag))};function Cf(e,t){return Zd(e,t)}function ng(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function We(e,t,n,r){return new ng(e,t,n,r)}function iu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function rg(e){if(typeof e=="function")return iu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Sl)return 11;if(e===El)return 14}return 2}function qt(e,t){var n=e.alternate;return n===null?(n=We(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function lo(e,t,n,r,i,o){var a=2;if(r=e,typeof e=="function")iu(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case Dn:return fn(n.children,i,o,t);case kl:a=8,i|=8;break;case ss:return e=We(12,n,t,i|2),e.elementType=ss,e.lanes=o,e;case ls:return e=We(13,n,t,i),e.elementType=ls,e.lanes=o,e;case us:return e=We(19,n,t,i),e.elementType=us,e.lanes=o,e;case Ld:return Zo(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Nd:a=10;break e;case Dd:a=9;break e;case Sl:a=11;break e;case El:a=14;break e;case Ct:a=16,r=null;break e}throw Error(b(130,e==null?e:typeof e,""))}return t=We(a,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function fn(e,t,n,r){return e=We(7,e,r,t),e.lanes=n,e}function Zo(e,t,n,r){return e=We(22,e,r,t),e.elementType=Ld,e.lanes=n,e.stateNode={isHidden:!1},e}function Ka(e,t,n){return e=We(6,e,null,t),e.lanes=n,e}function Ga(e,t,n){return t=We(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ig(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Pa(0),this.expirationTimes=Pa(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Pa(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function ou(e,t,n,r,i,o,a,s,l){return e=new ig(e,t,n,s,l),t===1?(t=1,o===!0&&(t|=8)):t=0,o=We(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Bl(o),e}function og(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Nn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Pf(e){if(!e)return Kt;e=e._reactInternals;e:{if(xn(e)!==e||e.tag!==1)throw Error(b(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ie(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(b(171))}if(e.tag===1){var n=e.type;if(Ie(n))return Cp(e,n,t)}return t}function Tf(e,t,n,r,i,o,a,s,l){return e=ou(n,r,!0,e,i,o,a,s,l),e.context=Pf(null),n=e.current,r=_e(),i=Wt(n),o=yt(r,i),o.callback=t??null,Mt(n,o,i),e.current.lanes=i,fi(e,i,r),Ae(e,r),e}function ea(e,t,n,r){var i=t.current,o=_e(),a=Wt(i);return n=Pf(n),t.context===null?t.context=n:t.pendingContext=n,t=yt(o,a),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Mt(i,t,a),e!==null&&(tt(e,i,a,o),no(e,i,a)),a}function Ao(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Ic(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function au(e,t){Ic(e,t),(e=e.alternate)&&Ic(e,t)}function ag(){return null}var Of=typeof reportError=="function"?reportError:function(e){console.error(e)};function su(e){this._internalRoot=e}ta.prototype.render=su.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(b(409));ea(e,t,null,null)};ta.prototype.unmount=su.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;yn(function(){ea(null,e,null,null)}),t[xt]=null}};function ta(e){this._internalRoot=e}ta.prototype.unstable_scheduleHydration=function(e){if(e){var t=ap();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ot.length&&t!==0&&t<Ot[n].priority;n++);Ot.splice(n,0,e),n===0&&lp(e)}};function lu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function na(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ac(){}function sg(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var u=Ao(a);o.call(u)}}var a=Tf(t,r,e,0,null,!1,!1,"",Ac);return e._reactRootContainer=a,e[xt]=a.current,Zr(e.nodeType===8?e.parentNode:e),yn(),a}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var s=r;r=function(){var u=Ao(l);s.call(u)}}var l=ou(e,0,!1,null,null,!1,!1,"",Ac);return e._reactRootContainer=l,e[xt]=l.current,Zr(e.nodeType===8?e.parentNode:e),yn(function(){ea(t,l,n,r)}),l}function ra(e,t,n,r,i){var o=n._reactRootContainer;if(o){var a=o;if(typeof i=="function"){var s=i;i=function(){var l=Ao(a);s.call(l)}}ea(t,a,e,i)}else a=sg(n,t,e,i,r);return Ao(a)}ip=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Tr(t.pendingLanes);n!==0&&(Cl(t,n|1),Ae(t,ee()),!(z&6)&&(ir=ee()+500,Qt()))}break;case 13:yn(function(){var r=_t(e,1);if(r!==null){var i=_e();tt(r,e,1,i)}}),au(e,1)}};Pl=function(e){if(e.tag===13){var t=_t(e,134217728);if(t!==null){var n=_e();tt(t,e,134217728,n)}au(e,134217728)}};op=function(e){if(e.tag===13){var t=Wt(e),n=_t(e,t);if(n!==null){var r=_e();tt(n,e,t,r)}au(e,t)}};ap=function(){return U};sp=function(e,t){var n=U;try{return U=e,t()}finally{U=n}};ws=function(e,t,n){switch(t){case"input":if(ps(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Ko(r);if(!i)throw Error(b(90));zd(r),ps(r,i)}}}break;case"textarea":Fd(e,n);break;case"select":t=n.value,t!=null&&Hn(e,!!n.multiple,t,!1)}};Kd=tu;Gd=yn;var lg={usingClientEntryPoint:!1,Events:[mi,Un,Ko,Vd,Hd,tu]},br={findFiberByHostInstance:un,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},ug={bundleType:br.bundleType,version:br.version,rendererPackageName:br.rendererPackageName,rendererConfig:br.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:St.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Yd(e),e===null?null:e.stateNode},findFiberByHostInstance:br.findFiberByHostInstance||ag,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Bi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Bi.isDisabled&&Bi.supportsFiber)try{Wo=Bi.inject(ug),ut=Bi}catch{}}Ue.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=lg;Ue.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!lu(t))throw Error(b(200));return og(e,t,null,n)};Ue.createRoot=function(e,t){if(!lu(e))throw Error(b(299));var n=!1,r="",i=Of;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=ou(e,1,!1,null,null,n,!1,r,i),e[xt]=t.current,Zr(e.nodeType===8?e.parentNode:e),new su(t)};Ue.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(b(188)):(e=Object.keys(e).join(","),Error(b(268,e)));return e=Yd(t),e=e===null?null:e.stateNode,e};Ue.flushSync=function(e){return yn(e)};Ue.hydrate=function(e,t,n){if(!na(t))throw Error(b(200));return ra(null,e,t,!0,n)};Ue.hydrateRoot=function(e,t,n){if(!lu(e))throw Error(b(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",a=Of;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),t=Tf(t,null,e,1,n??null,i,!1,o,a),e[xt]=t.current,Zr(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new ta(t)};Ue.render=function(e,t,n){if(!na(t))throw Error(b(200));return ra(null,e,t,!1,n)};Ue.unmountComponentAtNode=function(e){if(!na(e))throw Error(b(40));return e._reactRootContainer?(yn(function(){ra(null,null,e,!1,function(){e._reactRootContainer=null,e[xt]=null})}),!0):!1};Ue.unstable_batchedUpdates=tu;Ue.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!na(n))throw Error(b(200));if(e==null||e._reactInternals===void 0)throw Error(b(38));return ra(e,t,n,!1,r)};Ue.version="18.3.1-next-f1338f8080-20240426";function Rf(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Rf)}catch(e){console.error(e)}}Rf(),Od.exports=Ue;var cg=Od.exports,Nc=cg;os.createRoot=Nc.createRoot,os.hydrateRoot=Nc.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function li(){return li=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},li.apply(this,arguments)}var Dt;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Dt||(Dt={}));const Dc="popstate";function dg(e){e===void 0&&(e={});function t(r,i){let{pathname:o,search:a,hash:s}=r.location;return Xs("",{pathname:o,search:a,hash:s},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:No(i)}return fg(t,n,null,e)}function te(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function If(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function pg(){return Math.random().toString(36).substr(2,8)}function Lc(e,t){return{usr:e.state,key:e.key,idx:t}}function Xs(e,t,n,r){return n===void 0&&(n=null),li({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?dr(t):t,{state:n,key:t&&t.key||r||pg()})}function No(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function dr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function fg(e,t,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:o=!1}=r,a=i.history,s=Dt.Pop,l=null,u=c();u==null&&(u=0,a.replaceState(li({},a.state,{idx:u}),""));function c(){return(a.state||{idx:null}).idx}function d(){s=Dt.Pop;let _=c(),m=_==null?null:_-u;u=_,l&&l({action:s,location:w.location,delta:m})}function p(_,m){s=Dt.Push;let h=Xs(w.location,_,m);n&&n(h,_),u=c()+1;let f=Lc(h,u),x=w.createHref(h);try{a.pushState(f,"",x)}catch(S){if(S instanceof DOMException&&S.name==="DataCloneError")throw S;i.location.assign(x)}o&&l&&l({action:s,location:w.location,delta:1})}function g(_,m){s=Dt.Replace;let h=Xs(w.location,_,m);n&&n(h,_),u=c();let f=Lc(h,u),x=w.createHref(h);a.replaceState(f,"",x),o&&l&&l({action:s,location:w.location,delta:0})}function y(_){let m=i.location.origin!=="null"?i.location.origin:i.location.href,h=typeof _=="string"?_:No(_);return h=h.replace(/ $/,"%20"),te(m,"No window.location.(origin|href) available to create URL for href: "+h),new URL(h,m)}let w={get action(){return s},get location(){return e(i,a)},listen(_){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener(Dc,d),l=_,()=>{i.removeEventListener(Dc,d),l=null}},createHref(_){return t(i,_)},createURL:y,encodeLocation(_){let m=y(_);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:p,replace:g,go(_){return a.go(_)}};return w}var $c;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})($c||($c={}));function hg(e,t,n){return n===void 0&&(n="/"),mg(e,t,n,!1)}function mg(e,t,n,r){let i=typeof t=="string"?dr(t):t,o=uu(i.pathname||"/",n);if(o==null)return null;let a=Af(e);vg(a);let s=null;for(let l=0;s==null&&l<a.length;++l){let u=Cg(o);s=bg(a[l],u,r)}return s}function Af(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(o,a,s)=>{let l={relativePath:s===void 0?o.path||"":s,caseSensitive:o.caseSensitive===!0,childrenIndex:a,route:o};l.relativePath.startsWith("/")&&(te(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=Vt([r,l.relativePath]),c=n.concat(l);o.children&&o.children.length>0&&(te(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Af(o.children,t,c,u)),!(o.path==null&&!o.index)&&t.push({path:u,score:Sg(u,o.index),routesMeta:c})};return e.forEach((o,a)=>{var s;if(o.path===""||!((s=o.path)!=null&&s.includes("?")))i(o,a);else for(let l of Nf(o.path))i(o,a,l)}),t}function Nf(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,i=n.endsWith("?"),o=n.replace(/\?$/,"");if(r.length===0)return i?[o,""]:[o];let a=Nf(r.join("/")),s=[];return s.push(...a.map(l=>l===""?o:[o,l].join("/"))),i&&s.push(...a),s.map(l=>e.startsWith("/")&&l===""?"/":l)}function vg(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Eg(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const gg=/^:[\w-]+$/,yg=3,wg=2,xg=1,_g=10,kg=-2,zc=e=>e==="*";function Sg(e,t){let n=e.split("/"),r=n.length;return n.some(zc)&&(r+=kg),t&&(r+=wg),n.filter(i=>!zc(i)).reduce((i,o)=>i+(gg.test(o)?yg:o===""?xg:_g),r)}function Eg(e,t){return e.length===t.length&&e.slice(0,-1).every((r,i)=>r===t[i])?e[e.length-1]-t[t.length-1]:0}function bg(e,t,n){n===void 0&&(n=!1);let{routesMeta:r}=e,i={},o="/",a=[];for(let s=0;s<r.length;++s){let l=r[s],u=s===r.length-1,c=o==="/"?t:t.slice(o.length)||"/",d=Uc({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},c),p=l.route;if(!d&&u&&n&&!r[r.length-1].route.index&&(d=Uc({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},c)),!d)return null;Object.assign(i,d.params),a.push({params:i,pathname:Vt([o,d.pathname]),pathnameBase:Rg(Vt([o,d.pathnameBase])),route:p}),d.pathnameBase!=="/"&&(o=Vt([o,d.pathnameBase]))}return a}function Uc(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=jg(e.path,e.caseSensitive,e.end),i=t.match(n);if(!i)return null;let o=i[0],a=o.replace(/(.)\/+$/,"$1"),s=i.slice(1);return{params:r.reduce((u,c,d)=>{let{paramName:p,isOptional:g}=c;if(p==="*"){let w=s[d]||"";a=o.slice(0,o.length-w.length).replace(/(.)\/+$/,"$1")}const y=s[d];return g&&!y?u[p]=void 0:u[p]=(y||"").replace(/%2F/g,"/"),u},{}),pathname:o,pathnameBase:a,pattern:e}}function jg(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),If(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(a,s,l)=>(r.push({paramName:s,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),i+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":e!==""&&e!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,t?void 0:"i"),r]}function Cg(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return If(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function uu(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Pg(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:i=""}=typeof e=="string"?dr(e):e;return{pathname:n?n.startsWith("/")?n:Tg(n,t):t,search:Ig(r),hash:Ag(i)}}function Tg(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function Ja(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Og(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function cu(e,t){let n=Og(e);return t?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function du(e,t,n,r){r===void 0&&(r=!1);let i;typeof e=="string"?i=dr(e):(i=li({},e),te(!i.pathname||!i.pathname.includes("?"),Ja("?","pathname","search",i)),te(!i.pathname||!i.pathname.includes("#"),Ja("#","pathname","hash",i)),te(!i.search||!i.search.includes("#"),Ja("#","search","hash",i)));let o=e===""||i.pathname==="",a=o?"/":i.pathname,s;if(a==null)s=n;else{let d=t.length-1;if(!r&&a.startsWith("..")){let p=a.split("/");for(;p[0]==="..";)p.shift(),d-=1;i.pathname=p.join("/")}s=d>=0?t[d]:"/"}let l=Pg(i,s),u=a&&a!=="/"&&a.endsWith("/"),c=(o||a===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||c)&&(l.pathname+="/"),l}const Vt=e=>e.join("/").replace(/\/\/+/g,"/"),Rg=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ig=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ag=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Ng(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Df=["post","put","patch","delete"];new Set(Df);const Dg=["get",...Df];new Set(Dg);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ui(){return ui=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ui.apply(this,arguments)}const pu=k.createContext(null),Lg=k.createContext(null),Yt=k.createContext(null),ia=k.createContext(null),Xt=k.createContext({outlet:null,matches:[],isDataRoute:!1}),Lf=k.createContext(null);function $g(e,t){let{relative:n}=t===void 0?{}:t;pr()||te(!1);let{basename:r,navigator:i}=k.useContext(Yt),{hash:o,pathname:a,search:s}=zf(e,{relative:n}),l=a;return r!=="/"&&(l=a==="/"?r:Vt([r,a])),i.createHref({pathname:l,search:s,hash:o})}function pr(){return k.useContext(ia)!=null}function fr(){return pr()||te(!1),k.useContext(ia).location}function $f(e){k.useContext(Yt).static||k.useLayoutEffect(e)}function gi(){let{isDataRoute:e}=k.useContext(Xt);return e?Qg():zg()}function zg(){pr()||te(!1);let e=k.useContext(pu),{basename:t,future:n,navigator:r}=k.useContext(Yt),{matches:i}=k.useContext(Xt),{pathname:o}=fr(),a=JSON.stringify(cu(i,n.v7_relativeSplatPath)),s=k.useRef(!1);return $f(()=>{s.current=!0}),k.useCallback(function(u,c){if(c===void 0&&(c={}),!s.current)return;if(typeof u=="number"){r.go(u);return}let d=du(u,JSON.parse(a),o,c.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:Vt([t,d.pathname])),(c.replace?r.replace:r.push)(d,c.state,c)},[t,r,a,o,e])}function zf(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=k.useContext(Yt),{matches:i}=k.useContext(Xt),{pathname:o}=fr(),a=JSON.stringify(cu(i,r.v7_relativeSplatPath));return k.useMemo(()=>du(e,JSON.parse(a),o,n==="path"),[e,a,o,n])}function Ug(e,t){return Fg(e,t)}function Fg(e,t,n,r){pr()||te(!1);let{navigator:i}=k.useContext(Yt),{matches:o}=k.useContext(Xt),a=o[o.length-1],s=a?a.params:{};a&&a.pathname;let l=a?a.pathnameBase:"/";a&&a.route;let u=fr(),c;if(t){var d;let _=typeof t=="string"?dr(t):t;l==="/"||(d=_.pathname)!=null&&d.startsWith(l)||te(!1),c=_}else c=u;let p=c.pathname||"/",g=p;if(l!=="/"){let _=l.replace(/^\//,"").split("/");g="/"+p.replace(/^\//,"").split("/").slice(_.length).join("/")}let y=hg(e,{pathname:g}),w=Vg(y&&y.map(_=>Object.assign({},_,{params:Object.assign({},s,_.params),pathname:Vt([l,i.encodeLocation?i.encodeLocation(_.pathname).pathname:_.pathname]),pathnameBase:_.pathnameBase==="/"?l:Vt([l,i.encodeLocation?i.encodeLocation(_.pathnameBase).pathname:_.pathnameBase])})),o,n,r);return t&&w?k.createElement(ia.Provider,{value:{location:ui({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:Dt.Pop}},w):w}function Mg(){let e=Jg(),t=Ng(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},o=null;return k.createElement(k.Fragment,null,k.createElement("h2",null,"Unexpected Application Error!"),k.createElement("h3",{style:{fontStyle:"italic"}},t),n?k.createElement("pre",{style:i},n):null,o)}const Bg=k.createElement(Mg,null);class Wg extends k.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?k.createElement(Xt.Provider,{value:this.props.routeContext},k.createElement(Lf.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function qg(e){let{routeContext:t,match:n,children:r}=e,i=k.useContext(pu);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),k.createElement(Xt.Provider,{value:t},r)}function Vg(e,t,n,r){var i;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var o;if(!n)return null;if(n.errors)e=n.matches;else if((o=r)!=null&&o.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let a=e,s=(i=n)==null?void 0:i.errors;if(s!=null){let c=a.findIndex(d=>d.route.id&&(s==null?void 0:s[d.route.id])!==void 0);c>=0||te(!1),a=a.slice(0,Math.min(a.length,c+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<a.length;c++){let d=a[c];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(u=c),d.route.id){let{loaderData:p,errors:g}=n,y=d.route.loader&&p[d.route.id]===void 0&&(!g||g[d.route.id]===void 0);if(d.route.lazy||y){l=!0,u>=0?a=a.slice(0,u+1):a=[a[0]];break}}}return a.reduceRight((c,d,p)=>{let g,y=!1,w=null,_=null;n&&(g=s&&d.route.id?s[d.route.id]:void 0,w=d.route.errorElement||Bg,l&&(u<0&&p===0?(Yg("route-fallback",!1),y=!0,_=null):u===p&&(y=!0,_=d.route.hydrateFallbackElement||null)));let m=t.concat(a.slice(0,p+1)),h=()=>{let f;return g?f=w:y?f=_:d.route.Component?f=k.createElement(d.route.Component,null):d.route.element?f=d.route.element:f=c,k.createElement(qg,{match:d,routeContext:{outlet:c,matches:m,isDataRoute:n!=null},children:f})};return n&&(d.route.ErrorBoundary||d.route.errorElement||p===0)?k.createElement(Wg,{location:n.location,revalidation:n.revalidation,component:w,error:g,children:h(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):h()},null)}var Uf=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Uf||{}),Do=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Do||{});function Hg(e){let t=k.useContext(pu);return t||te(!1),t}function Kg(e){let t=k.useContext(Lg);return t||te(!1),t}function Gg(e){let t=k.useContext(Xt);return t||te(!1),t}function Ff(e){let t=Gg(),n=t.matches[t.matches.length-1];return n.route.id||te(!1),n.route.id}function Jg(){var e;let t=k.useContext(Lf),n=Kg(Do.UseRouteError),r=Ff(Do.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Qg(){let{router:e}=Hg(Uf.UseNavigateStable),t=Ff(Do.UseNavigateStable),n=k.useRef(!1);return $f(()=>{n.current=!0}),k.useCallback(function(i,o){o===void 0&&(o={}),n.current&&(typeof i=="number"?e.navigate(i):e.navigate(i,ui({fromRouteId:t},o)))},[e,t])}const Fc={};function Yg(e,t,n){!t&&!Fc[e]&&(Fc[e]=!0)}function Xg(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function Mf(e){let{to:t,replace:n,state:r,relative:i}=e;pr()||te(!1);let{future:o,static:a}=k.useContext(Yt),{matches:s}=k.useContext(Xt),{pathname:l}=fr(),u=gi(),c=du(t,cu(s,o.v7_relativeSplatPath),l,i==="path"),d=JSON.stringify(c);return k.useEffect(()=>u(JSON.parse(d),{replace:n,state:r,relative:i}),[u,d,i,n,r]),null}function bt(e){te(!1)}function Zg(e){let{basename:t="/",children:n=null,location:r,navigationType:i=Dt.Pop,navigator:o,static:a=!1,future:s}=e;pr()&&te(!1);let l=t.replace(/^\/*/,"/"),u=k.useMemo(()=>({basename:l,navigator:o,static:a,future:ui({v7_relativeSplatPath:!1},s)}),[l,s,o,a]);typeof r=="string"&&(r=dr(r));let{pathname:c="/",search:d="",hash:p="",state:g=null,key:y="default"}=r,w=k.useMemo(()=>{let _=uu(c,l);return _==null?null:{location:{pathname:_,search:d,hash:p,state:g,key:y},navigationType:i}},[l,c,d,p,g,y,i]);return w==null?null:k.createElement(Yt.Provider,{value:u},k.createElement(ia.Provider,{children:n,value:w}))}function ey(e){let{children:t,location:n}=e;return Ug(Zs(t),n)}new Promise(()=>{});function Zs(e,t){t===void 0&&(t=[]);let n=[];return k.Children.forEach(e,(r,i)=>{if(!k.isValidElement(r))return;let o=[...t,i];if(r.type===k.Fragment){n.push.apply(n,Zs(r.props.children,o));return}r.type!==bt&&te(!1),!r.props.index||!r.props.children||te(!1);let a={id:r.props.id||o.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(a.children=Zs(r.props.children,o)),n.push(a)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function el(){return el=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},el.apply(this,arguments)}function ty(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,o;for(o=0;o<r.length;o++)i=r[o],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}function ny(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function ry(e,t){return e.button===0&&(!t||t==="_self")&&!ny(e)}const iy=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],oy="6";try{window.__reactRouterVersion=oy}catch{}const ay="startTransition",Mc=Zh[ay];function sy(e){let{basename:t,children:n,future:r,window:i}=e,o=k.useRef();o.current==null&&(o.current=dg({window:i,v5Compat:!0}));let a=o.current,[s,l]=k.useState({action:a.action,location:a.location}),{v7_startTransition:u}=r||{},c=k.useCallback(d=>{u&&Mc?Mc(()=>l(d)):l(d)},[l,u]);return k.useLayoutEffect(()=>a.listen(c),[a,c]),k.useEffect(()=>Xg(r),[r]),k.createElement(Zg,{basename:t,children:n,location:s.location,navigationType:s.action,navigator:a,future:r})}const ly=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",uy=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Bf=k.forwardRef(function(t,n){let{onClick:r,relative:i,reloadDocument:o,replace:a,state:s,target:l,to:u,preventScrollReset:c,viewTransition:d}=t,p=ty(t,iy),{basename:g}=k.useContext(Yt),y,w=!1;if(typeof u=="string"&&uy.test(u)&&(y=u,ly))try{let f=new URL(window.location.href),x=u.startsWith("//")?new URL(f.protocol+u):new URL(u),S=uu(x.pathname,g);x.origin===f.origin&&S!=null?u=S+x.search+x.hash:w=!0}catch{}let _=$g(u,{relative:i}),m=cy(u,{replace:a,state:s,target:l,preventScrollReset:c,relative:i,viewTransition:d});function h(f){r&&r(f),f.defaultPrevented||m(f)}return k.createElement("a",el({},p,{href:y||_,onClick:w||o?r:h,ref:n,target:l}))});var Bc;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Bc||(Bc={}));var Wc;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Wc||(Wc={}));function cy(e,t){let{target:n,replace:r,state:i,preventScrollReset:o,relative:a,viewTransition:s}=t===void 0?{}:t,l=gi(),u=fr(),c=zf(e,{relative:a});return k.useCallback(d=>{if(ry(d,n)){d.preventDefault();let p=r!==void 0?r:No(u)===No(c);l(e,{replace:p,state:i,preventScrollReset:o,relative:a,viewTransition:s})}},[u,l,c,r,i,n,e,o,a,s])}const qc=e=>{let t;const n=new Set,r=(c,d)=>{const p=typeof c=="function"?c(t):c;if(!Object.is(p,t)){const g=t;t=d??(typeof p!="object"||p===null)?p:Object.assign({},t,p),n.forEach(y=>y(t,g))}},i=()=>t,l={setState:r,getState:i,getInitialState:()=>u,subscribe:c=>(n.add(c),()=>n.delete(c)),destroy:()=>{n.clear()}},u=t=e(r,i,l);return l},dy=e=>e?qc(e):qc;var Wf={exports:{}},qf={},Vf={exports:{}},Hf={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var or=k;function py(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var fy=typeof Object.is=="function"?Object.is:py,hy=or.useState,my=or.useEffect,vy=or.useLayoutEffect,gy=or.useDebugValue;function yy(e,t){var n=t(),r=hy({inst:{value:n,getSnapshot:t}}),i=r[0].inst,o=r[1];return vy(function(){i.value=n,i.getSnapshot=t,Qa(i)&&o({inst:i})},[e,n,t]),my(function(){return Qa(i)&&o({inst:i}),e(function(){Qa(i)&&o({inst:i})})},[e]),gy(n),n}function Qa(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!fy(e,n)}catch{return!0}}function wy(e,t){return t()}var xy=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?wy:yy;Hf.useSyncExternalStore=or.useSyncExternalStore!==void 0?or.useSyncExternalStore:xy;Vf.exports=Hf;var _y=Vf.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var oa=k,ky=_y;function Sy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ey=typeof Object.is=="function"?Object.is:Sy,by=ky.useSyncExternalStore,jy=oa.useRef,Cy=oa.useEffect,Py=oa.useMemo,Ty=oa.useDebugValue;qf.useSyncExternalStoreWithSelector=function(e,t,n,r,i){var o=jy(null);if(o.current===null){var a={hasValue:!1,value:null};o.current=a}else a=o.current;o=Py(function(){function l(g){if(!u){if(u=!0,c=g,g=r(g),i!==void 0&&a.hasValue){var y=a.value;if(i(y,g))return d=y}return d=g}if(y=d,Ey(c,g))return y;var w=r(g);return i!==void 0&&i(y,w)?(c=g,y):(c=g,d=w)}var u=!1,c,d,p=n===void 0?null:n;return[function(){return l(t())},p===null?void 0:function(){return l(p())}]},[t,n,r,i]);var s=by(e,o[0],o[1]);return Cy(function(){a.hasValue=!0,a.value=s},[s]),Ty(s),s};Wf.exports=qf;var Oy=Wf.exports;const Ry=hl(Oy),{useDebugValue:Iy}=Bo,{useSyncExternalStoreWithSelector:Ay}=Ry;const Ny=e=>e;function Dy(e,t=Ny,n){const r=Ay(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return Iy(r),r}const Vc=e=>{const t=typeof e=="function"?dy(e):e,n=(r,i)=>Dy(t,r,i);return Object.assign(n,t),n},fu=e=>e?Vc(e):Vc,Ly="modulepreload",$y=function(e){return"/"+e},Hc={},yi=function(t,n,r){if(!n||n.length===0)return t();const i=document.getElementsByTagName("link");return Promise.all(n.map(o=>{if(o=$y(o),o in Hc)return;Hc[o]=!0;const a=o.endsWith(".css"),s=a?'[rel="stylesheet"]':"";if(!!r)for(let c=i.length-1;c>=0;c--){const d=i[c];if(d.href===o&&(!a||d.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${o}"]${s}`))return;const u=document.createElement("link");if(u.rel=a?"stylesheet":Ly,a||(u.as="script",u.crossOrigin=""),u.href=o,document.head.appendChild(u),a)return new Promise((c,d)=>{u.addEventListener("load",c),u.addEventListener("error",()=>d(new Error(`Unable to preload CSS for ${o}`)))})})).then(()=>t()).catch(o=>{const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=o,window.dispatchEvent(a),!a.defaultPrevented)throw o})},zy=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>yi(()=>Promise.resolve().then(()=>hr),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)};class hu extends Error{constructor(t,n="FunctionsError",r){super(t),this.name=n,this.context=r}}class Uy extends hu{constructor(t){super("Failed to send a request to the Edge Function","FunctionsFetchError",t)}}class Fy extends hu{constructor(t){super("Relay Error invoking the Edge Function","FunctionsRelayError",t)}}class My extends hu{constructor(t){super("Edge Function returned a non-2xx status code","FunctionsHttpError",t)}}var tl;(function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"})(tl||(tl={}));var By=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(c){try{u(r.next(c))}catch(d){a(d)}}function l(c){try{u(r.throw(c))}catch(d){a(d)}}function u(c){c.done?o(c.value):i(c.value).then(s,l)}u((r=r.apply(e,t||[])).next())})};class Wy{constructor(t,{headers:n={},customFetch:r,region:i=tl.Any}={}){this.url=t,this.headers=n,this.region=i,this.fetch=zy(r)}setAuth(t){this.headers.Authorization=`Bearer ${t}`}invoke(t,n={}){var r;return By(this,void 0,void 0,function*(){try{const{headers:i,method:o,body:a}=n;let s={},{region:l}=n;l||(l=this.region),l&&l!=="any"&&(s["x-region"]=l);let u;a&&(i&&!Object.prototype.hasOwnProperty.call(i,"Content-Type")||!i)&&(typeof Blob<"u"&&a instanceof Blob||a instanceof ArrayBuffer?(s["Content-Type"]="application/octet-stream",u=a):typeof a=="string"?(s["Content-Type"]="text/plain",u=a):typeof FormData<"u"&&a instanceof FormData?u=a:(s["Content-Type"]="application/json",u=JSON.stringify(a)));const c=yield this.fetch(`${this.url}/${t}`,{method:o||"POST",headers:Object.assign(Object.assign(Object.assign({},s),this.headers),i),body:u}).catch(y=>{throw new Uy(y)}),d=c.headers.get("x-relay-error");if(d&&d==="true")throw new Fy(c);if(!c.ok)throw new My(c);let p=((r=c.headers.get("Content-Type"))!==null&&r!==void 0?r:"text/plain").split(";")[0].trim(),g;return p==="application/json"?g=yield c.json():p==="application/octet-stream"?g=yield c.blob():p==="text/event-stream"?g=c:p==="multipart/form-data"?g=yield c.formData():g=yield c.text(),{data:g,error:null}}catch(i){return{data:null,error:i}}})}}var Te={},mu={},aa={},wi={},sa={},la={},qy=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},ar=qy();const Vy=ar.fetch,Kf=ar.fetch.bind(ar),Gf=ar.Headers,Hy=ar.Request,Ky=ar.Response,hr=Object.freeze(Object.defineProperty({__proto__:null,Headers:Gf,Request:Hy,Response:Ky,default:Kf,fetch:Vy},Symbol.toStringTag,{value:"Module"})),Gy=$h(hr);var ua={};Object.defineProperty(ua,"__esModule",{value:!0});let Jy=class extends Error{constructor(t){super(t.message),this.name="PostgrestError",this.details=t.details,this.hint=t.hint,this.code=t.code}};ua.default=Jy;var Jf=Ve&&Ve.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(la,"__esModule",{value:!0});const Qy=Jf(Gy),Yy=Jf(ua);let Xy=class{constructor(t){this.shouldThrowOnError=!1,this.method=t.method,this.url=t.url,this.headers=t.headers,this.schema=t.schema,this.body=t.body,this.shouldThrowOnError=t.shouldThrowOnError,this.signal=t.signal,this.isMaybeSingle=t.isMaybeSingle,t.fetch?this.fetch=t.fetch:typeof fetch>"u"?this.fetch=Qy.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(t,n){return this.headers=Object.assign({},this.headers),this.headers[t]=n,this}then(t,n){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const r=this.fetch;let i=r(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async o=>{var a,s,l;let u=null,c=null,d=null,p=o.status,g=o.statusText;if(o.ok){if(this.method!=="HEAD"){const m=await o.text();m===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?c=m:c=JSON.parse(m))}const w=(a=this.headers.Prefer)===null||a===void 0?void 0:a.match(/count=(exact|planned|estimated)/),_=(s=o.headers.get("content-range"))===null||s===void 0?void 0:s.split("/");w&&_&&_.length>1&&(d=parseInt(_[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(c)&&(c.length>1?(u={code:"PGRST116",details:`Results contain ${c.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},c=null,d=null,p=406,g="Not Acceptable"):c.length===1?c=c[0]:c=null)}else{const w=await o.text();try{u=JSON.parse(w),Array.isArray(u)&&o.status===404&&(c=[],u=null,p=200,g="OK")}catch{o.status===404&&w===""?(p=204,g="No Content"):u={message:w}}if(u&&this.isMaybeSingle&&(!((l=u==null?void 0:u.details)===null||l===void 0)&&l.includes("0 rows"))&&(u=null,p=200,g="OK"),u&&this.shouldThrowOnError)throw new Yy.default(u)}return{error:u,data:c,count:d,status:p,statusText:g}});return this.shouldThrowOnError||(i=i.catch(o=>{var a,s,l;return{error:{message:`${(a=o==null?void 0:o.name)!==null&&a!==void 0?a:"FetchError"}: ${o==null?void 0:o.message}`,details:`${(s=o==null?void 0:o.stack)!==null&&s!==void 0?s:""}`,hint:"",code:`${(l=o==null?void 0:o.code)!==null&&l!==void 0?l:""}`},data:null,count:null,status:0,statusText:""}})),i.then(t,n)}returns(){return this}overrideTypes(){return this}};la.default=Xy;var Zy=Ve&&Ve.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(sa,"__esModule",{value:!0});const e0=Zy(la);let t0=class extends e0.default{select(t){let n=!1;const r=(t??"*").split("").map(i=>/\s/.test(i)&&!n?"":(i==='"'&&(n=!n),i)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(t,{ascending:n=!0,nullsFirst:r,foreignTable:i,referencedTable:o=i}={}){const a=o?`${o}.order`:"order",s=this.url.searchParams.get(a);return this.url.searchParams.set(a,`${s?`${s},`:""}${t}.${n?"asc":"desc"}${r===void 0?"":r?".nullsfirst":".nullslast"}`),this}limit(t,{foreignTable:n,referencedTable:r=n}={}){const i=typeof r>"u"?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${t}`),this}range(t,n,{foreignTable:r,referencedTable:i=r}={}){const o=typeof i>"u"?"offset":`${i}.offset`,a=typeof i>"u"?"limit":`${i}.limit`;return this.url.searchParams.set(o,`${t}`),this.url.searchParams.set(a,`${n-t+1}`),this}abortSignal(t){return this.signal=t,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:t=!1,verbose:n=!1,settings:r=!1,buffers:i=!1,wal:o=!1,format:a="text"}={}){var s;const l=[t?"analyze":null,n?"verbose":null,r?"settings":null,i?"buffers":null,o?"wal":null].filter(Boolean).join("|"),u=(s=this.headers.Accept)!==null&&s!==void 0?s:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${a}; for="${u}"; options=${l};`,a==="json"?this:this}rollback(){var t;return((t=this.headers.Prefer)!==null&&t!==void 0?t:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};sa.default=t0;var n0=Ve&&Ve.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(wi,"__esModule",{value:!0});const r0=n0(sa);let i0=class extends r0.default{eq(t,n){return this.url.searchParams.append(t,`eq.${n}`),this}neq(t,n){return this.url.searchParams.append(t,`neq.${n}`),this}gt(t,n){return this.url.searchParams.append(t,`gt.${n}`),this}gte(t,n){return this.url.searchParams.append(t,`gte.${n}`),this}lt(t,n){return this.url.searchParams.append(t,`lt.${n}`),this}lte(t,n){return this.url.searchParams.append(t,`lte.${n}`),this}like(t,n){return this.url.searchParams.append(t,`like.${n}`),this}likeAllOf(t,n){return this.url.searchParams.append(t,`like(all).{${n.join(",")}}`),this}likeAnyOf(t,n){return this.url.searchParams.append(t,`like(any).{${n.join(",")}}`),this}ilike(t,n){return this.url.searchParams.append(t,`ilike.${n}`),this}ilikeAllOf(t,n){return this.url.searchParams.append(t,`ilike(all).{${n.join(",")}}`),this}ilikeAnyOf(t,n){return this.url.searchParams.append(t,`ilike(any).{${n.join(",")}}`),this}is(t,n){return this.url.searchParams.append(t,`is.${n}`),this}in(t,n){const r=Array.from(new Set(n)).map(i=>typeof i=="string"&&new RegExp("[,()]").test(i)?`"${i}"`:`${i}`).join(",");return this.url.searchParams.append(t,`in.(${r})`),this}contains(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cs.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cs.{${n.join(",")}}`):this.url.searchParams.append(t,`cs.${JSON.stringify(n)}`),this}containedBy(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cd.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cd.{${n.join(",")}}`):this.url.searchParams.append(t,`cd.${JSON.stringify(n)}`),this}rangeGt(t,n){return this.url.searchParams.append(t,`sr.${n}`),this}rangeGte(t,n){return this.url.searchParams.append(t,`nxl.${n}`),this}rangeLt(t,n){return this.url.searchParams.append(t,`sl.${n}`),this}rangeLte(t,n){return this.url.searchParams.append(t,`nxr.${n}`),this}rangeAdjacent(t,n){return this.url.searchParams.append(t,`adj.${n}`),this}overlaps(t,n){return typeof n=="string"?this.url.searchParams.append(t,`ov.${n}`):this.url.searchParams.append(t,`ov.{${n.join(",")}}`),this}textSearch(t,n,{config:r,type:i}={}){let o="";i==="plain"?o="pl":i==="phrase"?o="ph":i==="websearch"&&(o="w");const a=r===void 0?"":`(${r})`;return this.url.searchParams.append(t,`${o}fts${a}.${n}`),this}match(t){return Object.entries(t).forEach(([n,r])=>{this.url.searchParams.append(n,`eq.${r}`)}),this}not(t,n,r){return this.url.searchParams.append(t,`not.${n}.${r}`),this}or(t,{foreignTable:n,referencedTable:r=n}={}){const i=r?`${r}.or`:"or";return this.url.searchParams.append(i,`(${t})`),this}filter(t,n,r){return this.url.searchParams.append(t,`${n}.${r}`),this}};wi.default=i0;var o0=Ve&&Ve.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(aa,"__esModule",{value:!0});const jr=o0(wi);let a0=class{constructor(t,{headers:n={},schema:r,fetch:i}){this.url=t,this.headers=n,this.schema=r,this.fetch=i}select(t,{head:n=!1,count:r}={}){const i=n?"HEAD":"GET";let o=!1;const a=(t??"*").split("").map(s=>/\s/.test(s)&&!o?"":(s==='"'&&(o=!o),s)).join("");return this.url.searchParams.set("select",a),r&&(this.headers.Prefer=`count=${r}`),new jr.default({method:i,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(t,{count:n,defaultToNull:r=!0}={}){const i="POST",o=[];if(this.headers.Prefer&&o.push(this.headers.Prefer),n&&o.push(`count=${n}`),r||o.push("missing=default"),this.headers.Prefer=o.join(","),Array.isArray(t)){const a=t.reduce((s,l)=>s.concat(Object.keys(l)),[]);if(a.length>0){const s=[...new Set(a)].map(l=>`"${l}"`);this.url.searchParams.set("columns",s.join(","))}}return new jr.default({method:i,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}upsert(t,{onConflict:n,ignoreDuplicates:r=!1,count:i,defaultToNull:o=!0}={}){const a="POST",s=[`resolution=${r?"ignore":"merge"}-duplicates`];if(n!==void 0&&this.url.searchParams.set("on_conflict",n),this.headers.Prefer&&s.push(this.headers.Prefer),i&&s.push(`count=${i}`),o||s.push("missing=default"),this.headers.Prefer=s.join(","),Array.isArray(t)){const l=t.reduce((u,c)=>u.concat(Object.keys(c)),[]);if(l.length>0){const u=[...new Set(l)].map(c=>`"${c}"`);this.url.searchParams.set("columns",u.join(","))}}return new jr.default({method:a,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}update(t,{count:n}={}){const r="PATCH",i=[];return this.headers.Prefer&&i.push(this.headers.Prefer),n&&i.push(`count=${n}`),this.headers.Prefer=i.join(","),new jr.default({method:r,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}delete({count:t}={}){const n="DELETE",r=[];return t&&r.push(`count=${t}`),this.headers.Prefer&&r.unshift(this.headers.Prefer),this.headers.Prefer=r.join(","),new jr.default({method:n,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};aa.default=a0;var ca={},da={};Object.defineProperty(da,"__esModule",{value:!0});da.version=void 0;da.version="0.0.0-automated";Object.defineProperty(ca,"__esModule",{value:!0});ca.DEFAULT_HEADERS=void 0;const s0=da;ca.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${s0.version}`};var Qf=Ve&&Ve.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(mu,"__esModule",{value:!0});const l0=Qf(aa),u0=Qf(wi),c0=ca;let d0=class Yf{constructor(t,{headers:n={},schema:r,fetch:i}={}){this.url=t,this.headers=Object.assign(Object.assign({},c0.DEFAULT_HEADERS),n),this.schemaName=r,this.fetch=i}from(t){const n=new URL(`${this.url}/${t}`);return new l0.default(n,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new Yf(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(t,n={},{head:r=!1,get:i=!1,count:o}={}){let a;const s=new URL(`${this.url}/rpc/${t}`);let l;r||i?(a=r?"HEAD":"GET",Object.entries(n).filter(([c,d])=>d!==void 0).map(([c,d])=>[c,Array.isArray(d)?`{${d.join(",")}}`:`${d}`]).forEach(([c,d])=>{s.searchParams.append(c,d)})):(a="POST",l=n);const u=Object.assign({},this.headers);return o&&(u.Prefer=`count=${o}`),new u0.default({method:a,url:s,headers:u,schema:this.schemaName,body:l,fetch:this.fetch,allowEmpty:!1})}};mu.default=d0;var mr=Ve&&Ve.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Te,"__esModule",{value:!0});Te.PostgrestError=Te.PostgrestBuilder=Te.PostgrestTransformBuilder=Te.PostgrestFilterBuilder=Te.PostgrestQueryBuilder=Te.PostgrestClient=void 0;const Xf=mr(mu);Te.PostgrestClient=Xf.default;const Zf=mr(aa);Te.PostgrestQueryBuilder=Zf.default;const eh=mr(wi);Te.PostgrestFilterBuilder=eh.default;const th=mr(sa);Te.PostgrestTransformBuilder=th.default;const nh=mr(la);Te.PostgrestBuilder=nh.default;const rh=mr(ua);Te.PostgrestError=rh.default;var p0=Te.default={PostgrestClient:Xf.default,PostgrestQueryBuilder:Zf.default,PostgrestFilterBuilder:eh.default,PostgrestTransformBuilder:th.default,PostgrestBuilder:nh.default,PostgrestError:rh.default};const{PostgrestClient:f0,PostgrestQueryBuilder:v_,PostgrestFilterBuilder:g_,PostgrestTransformBuilder:y_,PostgrestBuilder:w_,PostgrestError:x_}=p0;function h0(){if(typeof WebSocket<"u")return WebSocket;if(typeof global.WebSocket<"u")return global.WebSocket;if(typeof window.WebSocket<"u")return window.WebSocket;if(typeof self.WebSocket<"u")return self.WebSocket;throw new Error("`WebSocket` is not supported in this environment")}const m0=h0(),v0="2.11.15",g0=`realtime-js/${v0}`,y0="1.0.0",ih=1e4,w0=1e3;var Mr;(function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"})(Mr||(Mr={}));var ve;(function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"})(ve||(ve={}));var Xe;(function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"})(Xe||(Xe={}));var nl;(function(e){e.websocket="websocket"})(nl||(nl={}));var sn;(function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"})(sn||(sn={}));class x0{constructor(){this.HEADER_LENGTH=1}decode(t,n){return t.constructor===ArrayBuffer?n(this._binaryDecode(t)):n(typeof t=="string"?JSON.parse(t):{})}_binaryDecode(t){const n=new DataView(t),r=new TextDecoder;return this._decodeBroadcast(t,n,r)}_decodeBroadcast(t,n,r){const i=n.getUint8(1),o=n.getUint8(2);let a=this.HEADER_LENGTH+2;const s=r.decode(t.slice(a,a+i));a=a+i;const l=r.decode(t.slice(a,a+o));a=a+o;const u=JSON.parse(r.decode(t.slice(a,t.byteLength)));return{ref:null,topic:s,event:l,payload:u}}}class oh{constructor(t,n){this.callback=t,this.timerCalc=n,this.timer=void 0,this.tries=0,this.callback=t,this.timerCalc=n}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var B;(function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"})(B||(B={}));const Kc=(e,t,n={})=>{var r;const i=(r=n.skipTypes)!==null&&r!==void 0?r:[];return Object.keys(t).reduce((o,a)=>(o[a]=_0(a,e,t,i),o),{})},_0=(e,t,n,r)=>{const i=t.find(s=>s.name===e),o=i==null?void 0:i.type,a=n[e];return o&&!r.includes(o)?ah(o,a):rl(a)},ah=(e,t)=>{if(e.charAt(0)==="_"){const n=e.slice(1,e.length);return b0(t,n)}switch(e){case B.bool:return k0(t);case B.float4:case B.float8:case B.int2:case B.int4:case B.int8:case B.numeric:case B.oid:return S0(t);case B.json:case B.jsonb:return E0(t);case B.timestamp:return j0(t);case B.abstime:case B.date:case B.daterange:case B.int4range:case B.int8range:case B.money:case B.reltime:case B.text:case B.time:case B.timestamptz:case B.timetz:case B.tsrange:case B.tstzrange:return rl(t);default:return rl(t)}},rl=e=>e,k0=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},S0=e=>{if(typeof e=="string"){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},E0=e=>{if(typeof e=="string")try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},b0=(e,t)=>{if(typeof e!="string")return e;const n=e.length-1,r=e[n];if(e[0]==="{"&&r==="}"){let o;const a=e.slice(1,n);try{o=JSON.parse("["+a+"]")}catch{o=a?a.split(","):[]}return o.map(s=>ah(t,s))}return e},j0=e=>typeof e=="string"?e.replace(" ","T"):e,sh=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class Ya{constructor(t,n,r={},i=ih){this.channel=t,this.event=n,this.payload=r,this.timeout=i,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(t){this.timeout=t,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(t){this.payload=Object.assign(Object.assign({},this.payload),t)}receive(t,n){var r;return this._hasReceived(t)&&n((r=this.receivedResp)===null||r===void 0?void 0:r.response),this.recHooks.push({status:t,callback:n}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const t=n=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=n,this._matchReceive(n)};this.channel._on(this.refEvent,{},t),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(t,n){this.refEvent&&this.channel._trigger(this.refEvent,{status:t,response:n})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:t,response:n}){this.recHooks.filter(r=>r.status===t).forEach(r=>r.callback(n))}_hasReceived(t){return this.receivedResp&&this.receivedResp.status===t}}var Gc;(function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"})(Gc||(Gc={}));class Br{constructor(t,n){this.channel=t,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=(n==null?void 0:n.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},i=>{const{onJoin:o,onLeave:a,onSync:s}=this.caller;this.joinRef=this.channel._joinRef(),this.state=Br.syncState(this.state,i,o,a),this.pendingDiffs.forEach(l=>{this.state=Br.syncDiff(this.state,l,o,a)}),this.pendingDiffs=[],s()}),this.channel._on(r.diff,{},i=>{const{onJoin:o,onLeave:a,onSync:s}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(i):(this.state=Br.syncDiff(this.state,i,o,a),s())}),this.onJoin((i,o,a)=>{this.channel._trigger("presence",{event:"join",key:i,currentPresences:o,newPresences:a})}),this.onLeave((i,o,a)=>{this.channel._trigger("presence",{event:"leave",key:i,currentPresences:o,leftPresences:a})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(t,n,r,i){const o=this.cloneDeep(t),a=this.transformState(n),s={},l={};return this.map(o,(u,c)=>{a[u]||(l[u]=c)}),this.map(a,(u,c)=>{const d=o[u];if(d){const p=c.map(_=>_.presence_ref),g=d.map(_=>_.presence_ref),y=c.filter(_=>g.indexOf(_.presence_ref)<0),w=d.filter(_=>p.indexOf(_.presence_ref)<0);y.length>0&&(s[u]=y),w.length>0&&(l[u]=w)}else s[u]=c}),this.syncDiff(o,{joins:s,leaves:l},r,i)}static syncDiff(t,n,r,i){const{joins:o,leaves:a}={joins:this.transformState(n.joins),leaves:this.transformState(n.leaves)};return r||(r=()=>{}),i||(i=()=>{}),this.map(o,(s,l)=>{var u;const c=(u=t[s])!==null&&u!==void 0?u:[];if(t[s]=this.cloneDeep(l),c.length>0){const d=t[s].map(g=>g.presence_ref),p=c.filter(g=>d.indexOf(g.presence_ref)<0);t[s].unshift(...p)}r(s,c,l)}),this.map(a,(s,l)=>{let u=t[s];if(!u)return;const c=l.map(d=>d.presence_ref);u=u.filter(d=>c.indexOf(d.presence_ref)<0),t[s]=u,i(s,u,l),u.length===0&&delete t[s]}),t}static map(t,n){return Object.getOwnPropertyNames(t).map(r=>n(r,t[r]))}static transformState(t){return t=this.cloneDeep(t),Object.getOwnPropertyNames(t).reduce((n,r)=>{const i=t[r];return"metas"in i?n[r]=i.metas.map(o=>(o.presence_ref=o.phx_ref,delete o.phx_ref,delete o.phx_ref_prev,o)):n[r]=i,n},{})}static cloneDeep(t){return JSON.parse(JSON.stringify(t))}onJoin(t){this.caller.onJoin=t}onLeave(t){this.caller.onLeave=t}onSync(t){this.caller.onSync=t}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var Jc;(function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"})(Jc||(Jc={}));var Qc;(function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"})(Qc||(Qc={}));var ht;(function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"})(ht||(ht={}));class vu{constructor(t,n={config:{}},r){this.topic=t,this.params=n,this.socket=r,this.bindings={},this.state=ve.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=t.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},n.config),this.timeout=this.socket.timeout,this.joinPush=new Ya(this,Xe.join,this.params,this.timeout),this.rejoinTimer=new oh(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=ve.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(i=>i.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=ve.closed,this.socket._remove(this)}),this._onError(i=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,i),this.state=ve.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=ve.errored,this.rejoinTimer.scheduleTimeout())}),this._on(Xe.reply,{},(i,o)=>{this._trigger(this._replyEventName(o),i)}),this.presence=new Br(this),this.broadcastEndpointURL=sh(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(t,n=this.timeout){var r,i;if(this.socket.isConnected()||this.socket.connect(),this.state==ve.closed){const{config:{broadcast:o,presence:a,private:s}}=this.params;this._onError(c=>t==null?void 0:t(ht.CHANNEL_ERROR,c)),this._onClose(()=>t==null?void 0:t(ht.CLOSED));const l={},u={broadcast:o,presence:a,postgres_changes:(i=(r=this.bindings.postgres_changes)===null||r===void 0?void 0:r.map(c=>c.filter))!==null&&i!==void 0?i:[],private:s};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:u},l)),this.joinedOnce=!0,this._rejoin(n),this.joinPush.receive("ok",async({postgres_changes:c})=>{var d;if(this.socket.setAuth(),c===void 0){t==null||t(ht.SUBSCRIBED);return}else{const p=this.bindings.postgres_changes,g=(d=p==null?void 0:p.length)!==null&&d!==void 0?d:0,y=[];for(let w=0;w<g;w++){const _=p[w],{filter:{event:m,schema:h,table:f,filter:x}}=_,S=c&&c[w];if(S&&S.event===m&&S.schema===h&&S.table===f&&S.filter===x)y.push(Object.assign(Object.assign({},_),{id:S.id}));else{this.unsubscribe(),this.state=ve.errored,t==null||t(ht.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=y,t&&t(ht.SUBSCRIBED);return}}).receive("error",c=>{this.state=ve.errored,t==null||t(ht.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(c).join(", ")||"error")))}).receive("timeout",()=>{t==null||t(ht.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(t,n={}){return await this.send({type:"presence",event:"track",payload:t},n.timeout||this.timeout)}async untrack(t={}){return await this.send({type:"presence",event:"untrack"},t)}on(t,n,r){return this._on(t,n,r)}async send(t,n={}){var r,i;if(!this._canPush()&&t.type==="broadcast"){const{event:o,payload:a}=t,l={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:o,payload:a,private:this.private}]})};try{const u=await this._fetchWithTimeout(this.broadcastEndpointURL,l,(r=n.timeout)!==null&&r!==void 0?r:this.timeout);return await((i=u.body)===null||i===void 0?void 0:i.cancel()),u.ok?"ok":"error"}catch(u){return u.name==="AbortError"?"timed out":"error"}}else return new Promise(o=>{var a,s,l;const u=this._push(t.type,t,n.timeout||this.timeout);t.type==="broadcast"&&!(!((l=(s=(a=this.params)===null||a===void 0?void 0:a.config)===null||s===void 0?void 0:s.broadcast)===null||l===void 0)&&l.ack)&&o("ok"),u.receive("ok",()=>o("ok")),u.receive("error",()=>o("error")),u.receive("timeout",()=>o("timed out"))})}updateJoinPayload(t){this.joinPush.updatePayload(t)}unsubscribe(t=this.timeout){this.state=ve.leaving;const n=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(Xe.close,"leave",this._joinRef())};this.joinPush.destroy();let r=null;return new Promise(i=>{r=new Ya(this,Xe.leave,{},t),r.receive("ok",()=>{n(),i("ok")}).receive("timeout",()=>{n(),i("timed out")}).receive("error",()=>{i("error")}),r.send(),this._canPush()||r.trigger("ok",{})}).finally(()=>{r==null||r.destroy()})}teardown(){this.pushBuffer.forEach(t=>t.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(t,n,r){const i=new AbortController,o=setTimeout(()=>i.abort(),r),a=await this.socket.fetch(t,Object.assign(Object.assign({},n),{signal:i.signal}));return clearTimeout(o),a}_push(t,n,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${t}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let i=new Ya(this,t,n,r);return this._canPush()?i.send():(i.startTimeout(),this.pushBuffer.push(i)),i}_onMessage(t,n,r){return n}_isMember(t){return this.topic===t}_joinRef(){return this.joinPush.ref}_trigger(t,n,r){var i,o;const a=t.toLocaleLowerCase(),{close:s,error:l,leave:u,join:c}=Xe;if(r&&[s,l,u,c].indexOf(a)>=0&&r!==this._joinRef())return;let p=this._onMessage(a,n,r);if(n&&!p)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(a)?(i=this.bindings.postgres_changes)===null||i===void 0||i.filter(g=>{var y,w,_;return((y=g.filter)===null||y===void 0?void 0:y.event)==="*"||((_=(w=g.filter)===null||w===void 0?void 0:w.event)===null||_===void 0?void 0:_.toLocaleLowerCase())===a}).map(g=>g.callback(p,r)):(o=this.bindings[a])===null||o===void 0||o.filter(g=>{var y,w,_,m,h,f;if(["broadcast","presence","postgres_changes"].includes(a))if("id"in g){const x=g.id,S=(y=g.filter)===null||y===void 0?void 0:y.event;return x&&((w=n.ids)===null||w===void 0?void 0:w.includes(x))&&(S==="*"||(S==null?void 0:S.toLocaleLowerCase())===((_=n.data)===null||_===void 0?void 0:_.type.toLocaleLowerCase()))}else{const x=(h=(m=g==null?void 0:g.filter)===null||m===void 0?void 0:m.event)===null||h===void 0?void 0:h.toLocaleLowerCase();return x==="*"||x===((f=n==null?void 0:n.event)===null||f===void 0?void 0:f.toLocaleLowerCase())}else return g.type.toLocaleLowerCase()===a}).map(g=>{if(typeof p=="object"&&"ids"in p){const y=p.data,{schema:w,table:_,commit_timestamp:m,type:h,errors:f}=y;p=Object.assign(Object.assign({},{schema:w,table:_,commit_timestamp:m,eventType:h,new:{},old:{},errors:f}),this._getPayloadRecords(y))}g.callback(p,r)})}_isClosed(){return this.state===ve.closed}_isJoined(){return this.state===ve.joined}_isJoining(){return this.state===ve.joining}_isLeaving(){return this.state===ve.leaving}_replyEventName(t){return`chan_reply_${t}`}_on(t,n,r){const i=t.toLocaleLowerCase(),o={type:i,filter:n,callback:r};return this.bindings[i]?this.bindings[i].push(o):this.bindings[i]=[o],this}_off(t,n){const r=t.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(i=>{var o;return!(((o=i.type)===null||o===void 0?void 0:o.toLocaleLowerCase())===r&&vu.isEqual(i.filter,n))}),this}static isEqual(t,n){if(Object.keys(t).length!==Object.keys(n).length)return!1;for(const r in t)if(t[r]!==n[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(t){this._on(Xe.close,{},t)}_onError(t){this._on(Xe.error,{},n=>t(n))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(t=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=ve.joining,this.joinPush.resend(t))}_getPayloadRecords(t){const n={new:{},old:{}};return(t.type==="INSERT"||t.type==="UPDATE")&&(n.new=Kc(t.columns,t.record)),(t.type==="UPDATE"||t.type==="DELETE")&&(n.old=Kc(t.columns,t.old_record)),n}}const Yc=()=>{},C0=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class P0{constructor(t,n){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=ih,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=Yc,this.ref=0,this.logger=Yc,this.conn=null,this.sendBuffer=[],this.serializer=new x0,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=o=>{let a;return o?a=o:typeof fetch>"u"?a=(...s)=>yi(()=>Promise.resolve().then(()=>hr),void 0).then(({default:l})=>l(...s)):a=fetch,(...s)=>a(...s)},this.endPoint=`${t}/${nl.websocket}`,this.httpEndpoint=sh(t),n!=null&&n.transport?this.transport=n.transport:this.transport=null,n!=null&&n.params&&(this.params=n.params),n!=null&&n.timeout&&(this.timeout=n.timeout),n!=null&&n.logger&&(this.logger=n.logger),(n!=null&&n.logLevel||n!=null&&n.log_level)&&(this.logLevel=n.logLevel||n.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),n!=null&&n.heartbeatIntervalMs&&(this.heartbeatIntervalMs=n.heartbeatIntervalMs);const i=(r=n==null?void 0:n.params)===null||r===void 0?void 0:r.apikey;if(i&&(this.accessTokenValue=i,this.apiKey=i),this.reconnectAfterMs=n!=null&&n.reconnectAfterMs?n.reconnectAfterMs:o=>[1e3,2e3,5e3,1e4][o-1]||1e4,this.encode=n!=null&&n.encode?n.encode:(o,a)=>a(JSON.stringify(o)),this.decode=n!=null&&n.decode?n.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new oh(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(n==null?void 0:n.fetch),n!=null&&n.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(n==null?void 0:n.worker)||!1,this.workerUrl=n==null?void 0:n.workerUrl}this.accessToken=(n==null?void 0:n.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=m0),!this.transport)throw new Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:y0}))}disconnect(t,n){this.conn&&(this.conn.onclose=function(){},t?this.conn.close(t,n??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(r=>r.teardown()))}getChannels(){return this.channels}async removeChannel(t){const n=await t.unsubscribe();return this.channels.length===0&&this.disconnect(),n}async removeAllChannels(){const t=await Promise.all(this.channels.map(n=>n.unsubscribe()));return this.channels=[],this.disconnect(),t}log(t,n,r){this.logger(t,n,r)}connectionState(){switch(this.conn&&this.conn.readyState){case Mr.connecting:return sn.Connecting;case Mr.open:return sn.Open;case Mr.closing:return sn.Closing;default:return sn.Closed}}isConnected(){return this.connectionState()===sn.Open}channel(t,n={config:{}}){const r=`realtime:${t}`,i=this.getChannels().find(o=>o.topic===r);if(i)return i;{const o=new vu(`realtime:${t}`,n,this);return this.channels.push(o),o}}push(t){const{topic:n,event:r,payload:i,ref:o}=t,a=()=>{this.encode(t,s=>{var l;(l=this.conn)===null||l===void 0||l.send(s)})};this.log("push",`${n} ${r} (${o})`,i),this.isConnected()?a():this.sendBuffer.push(a)}async setAuth(t=null){let n=t||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=n&&(this.accessTokenValue=n,this.channels.forEach(r=>{const i={access_token:n,version:g0};n&&r.updateJoinPayload(i),r.joinedOnce&&r._isJoined()&&r._push(Xe.access_token,{access_token:n})}))}async sendHeartbeat(){var t;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),(t=this.conn)===null||t===void 0||t.close(w0,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(t){this.heartbeatCallback=t}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(t=>t()),this.sendBuffer=[])}_makeRef(){let t=this.ref+1;return t===this.ref?this.ref=0:this.ref=t,this.ref.toString()}_leaveOpenTopic(t){let n=this.channels.find(r=>r.topic===t&&(r._isJoined()||r._isJoining()));n&&(this.log("transport",`leaving duplicate topic "${t}"`),n.unsubscribe())}_remove(t){this.channels=this.channels.filter(n=>n.topic!==t.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=t=>this._onConnError(t),this.conn.onmessage=t=>this._onConnMessage(t),this.conn.onclose=t=>this._onConnClose(t))}_onConnMessage(t){this.decode(t.data,n=>{let{topic:r,event:i,payload:o,ref:a}=n;r==="phoenix"&&i==="phx_reply"&&this.heartbeatCallback(n.payload.status=="ok"?"ok":"error"),a&&a===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${o.status||""} ${r} ${i} ${a&&"("+a+")"||""}`,o),Array.from(this.channels).filter(s=>s._isMember(r)).forEach(s=>s._trigger(i,o,a)),this.stateChangeCallbacks.message.forEach(s=>s(n))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(t=>t())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const t=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(t),this.workerRef.onerror=n=>{this.log("worker","worker error",n.message),this.workerRef.terminate()},this.workerRef.onmessage=n=>{n.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(t){this.log("transport","close",t),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(n=>n(t))}_onConnError(t){this.log("transport",`${t}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(n=>n(t))}_triggerChanError(){this.channels.forEach(t=>t._trigger(Xe.error))}_appendParams(t,n){if(Object.keys(n).length===0)return t;const r=t.match(/\?/)?"&":"?",i=new URLSearchParams(n);return`${t}${r}${i}`}_workerObjectUrl(t){let n;if(t)n=t;else{const r=new Blob([C0],{type:"application/javascript"});n=URL.createObjectURL(r)}return n}}class gu extends Error{constructor(t){super(t),this.__isStorageError=!0,this.name="StorageError"}}function le(e){return typeof e=="object"&&e!==null&&"__isStorageError"in e}class T0 extends gu{constructor(t,n){super(t),this.name="StorageApiError",this.status=n}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class il extends gu{constructor(t,n){super(t),this.name="StorageUnknownError",this.originalError=n}}var O0=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(c){try{u(r.next(c))}catch(d){a(d)}}function l(c){try{u(r.throw(c))}catch(d){a(d)}}function u(c){c.done?o(c.value):i(c.value).then(s,l)}u((r=r.apply(e,t||[])).next())})};const lh=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>yi(()=>Promise.resolve().then(()=>hr),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},R0=()=>O0(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield yi(()=>Promise.resolve().then(()=>hr),void 0)).Response:Response}),ol=e=>{if(Array.isArray(e))return e.map(n=>ol(n));if(typeof e=="function"||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([n,r])=>{const i=n.replace(/([-_][a-z])/gi,o=>o.toUpperCase().replace(/[-_]/g,""));t[i]=ol(r)}),t};var _n=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(c){try{u(r.next(c))}catch(d){a(d)}}function l(c){try{u(r.throw(c))}catch(d){a(d)}}function u(c){c.done?o(c.value):i(c.value).then(s,l)}u((r=r.apply(e,t||[])).next())})};const Xa=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),I0=(e,t,n)=>_n(void 0,void 0,void 0,function*(){const r=yield R0();e instanceof r&&!(n!=null&&n.noResolveJson)?e.json().then(i=>{t(new T0(Xa(i),e.status||500))}).catch(i=>{t(new il(Xa(i),i))}):t(new il(Xa(e),e))}),A0=(e,t,n,r)=>{const i={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?i:(i.headers=Object.assign({"Content-Type":"application/json"},t==null?void 0:t.headers),r&&(i.body=JSON.stringify(r)),Object.assign(Object.assign({},i),n))};function xi(e,t,n,r,i,o){return _n(this,void 0,void 0,function*(){return new Promise((a,s)=>{e(n,A0(t,r,i,o)).then(l=>{if(!l.ok)throw l;return r!=null&&r.noResolveJson?l:l.json()}).then(l=>a(l)).catch(l=>I0(l,s,r))})})}function Lo(e,t,n,r){return _n(this,void 0,void 0,function*(){return xi(e,"GET",t,n,r)})}function Tt(e,t,n,r,i){return _n(this,void 0,void 0,function*(){return xi(e,"POST",t,r,i,n)})}function N0(e,t,n,r,i){return _n(this,void 0,void 0,function*(){return xi(e,"PUT",t,r,i,n)})}function D0(e,t,n,r){return _n(this,void 0,void 0,function*(){return xi(e,"HEAD",t,Object.assign(Object.assign({},n),{noResolveJson:!0}),r)})}function uh(e,t,n,r,i){return _n(this,void 0,void 0,function*(){return xi(e,"DELETE",t,r,i,n)})}var Ce=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(c){try{u(r.next(c))}catch(d){a(d)}}function l(c){try{u(r.throw(c))}catch(d){a(d)}}function u(c){c.done?o(c.value):i(c.value).then(s,l)}u((r=r.apply(e,t||[])).next())})};const L0={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Xc={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class $0{constructor(t,n={},r,i){this.url=t,this.headers=n,this.bucketId=r,this.fetch=lh(i)}uploadOrUpdate(t,n,r,i){return Ce(this,void 0,void 0,function*(){try{let o;const a=Object.assign(Object.assign({},Xc),i);let s=Object.assign(Object.assign({},this.headers),t==="POST"&&{"x-upsert":String(a.upsert)});const l=a.metadata;typeof Blob<"u"&&r instanceof Blob?(o=new FormData,o.append("cacheControl",a.cacheControl),l&&o.append("metadata",this.encodeMetadata(l)),o.append("",r)):typeof FormData<"u"&&r instanceof FormData?(o=r,o.append("cacheControl",a.cacheControl),l&&o.append("metadata",this.encodeMetadata(l))):(o=r,s["cache-control"]=`max-age=${a.cacheControl}`,s["content-type"]=a.contentType,l&&(s["x-metadata"]=this.toBase64(this.encodeMetadata(l)))),i!=null&&i.headers&&(s=Object.assign(Object.assign({},s),i.headers));const u=this._removeEmptyFolders(n),c=this._getFinalPath(u),d=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:t,body:o,headers:s},a!=null&&a.duplex?{duplex:a.duplex}:{})),p=yield d.json();return d.ok?{data:{path:u,id:p.Id,fullPath:p.Key},error:null}:{data:null,error:p}}catch(o){if(le(o))return{data:null,error:o};throw o}})}upload(t,n,r){return Ce(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",t,n,r)})}uploadToSignedUrl(t,n,r,i){return Ce(this,void 0,void 0,function*(){const o=this._removeEmptyFolders(t),a=this._getFinalPath(o),s=new URL(this.url+`/object/upload/sign/${a}`);s.searchParams.set("token",n);try{let l;const u=Object.assign({upsert:Xc.upsert},i),c=Object.assign(Object.assign({},this.headers),{"x-upsert":String(u.upsert)});typeof Blob<"u"&&r instanceof Blob?(l=new FormData,l.append("cacheControl",u.cacheControl),l.append("",r)):typeof FormData<"u"&&r instanceof FormData?(l=r,l.append("cacheControl",u.cacheControl)):(l=r,c["cache-control"]=`max-age=${u.cacheControl}`,c["content-type"]=u.contentType);const d=yield this.fetch(s.toString(),{method:"PUT",body:l,headers:c}),p=yield d.json();return d.ok?{data:{path:o,fullPath:p.Key},error:null}:{data:null,error:p}}catch(l){if(le(l))return{data:null,error:l};throw l}})}createSignedUploadUrl(t,n){return Ce(this,void 0,void 0,function*(){try{let r=this._getFinalPath(t);const i=Object.assign({},this.headers);n!=null&&n.upsert&&(i["x-upsert"]="true");const o=yield Tt(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:i}),a=new URL(this.url+o.url),s=a.searchParams.get("token");if(!s)throw new gu("No token returned by API");return{data:{signedUrl:a.toString(),path:t,token:s},error:null}}catch(r){if(le(r))return{data:null,error:r};throw r}})}update(t,n,r){return Ce(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",t,n,r)})}move(t,n,r){return Ce(this,void 0,void 0,function*(){try{return{data:yield Tt(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(i){if(le(i))return{data:null,error:i};throw i}})}copy(t,n,r){return Ce(this,void 0,void 0,function*(){try{return{data:{path:(yield Tt(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(i){if(le(i))return{data:null,error:i};throw i}})}createSignedUrl(t,n,r){return Ce(this,void 0,void 0,function*(){try{let i=this._getFinalPath(t),o=yield Tt(this.fetch,`${this.url}/object/sign/${i}`,Object.assign({expiresIn:n},r!=null&&r.transform?{transform:r.transform}:{}),{headers:this.headers});const a=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return o={signedUrl:encodeURI(`${this.url}${o.signedURL}${a}`)},{data:o,error:null}}catch(i){if(le(i))return{data:null,error:i};throw i}})}createSignedUrls(t,n,r){return Ce(this,void 0,void 0,function*(){try{const i=yield Tt(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:n,paths:t},{headers:this.headers}),o=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return{data:i.map(a=>Object.assign(Object.assign({},a),{signedUrl:a.signedURL?encodeURI(`${this.url}${a.signedURL}${o}`):null})),error:null}}catch(i){if(le(i))return{data:null,error:i};throw i}})}download(t,n){return Ce(this,void 0,void 0,function*(){const i=typeof(n==null?void 0:n.transform)<"u"?"render/image/authenticated":"object",o=this.transformOptsToQueryString((n==null?void 0:n.transform)||{}),a=o?`?${o}`:"";try{const s=this._getFinalPath(t);return{data:yield(yield Lo(this.fetch,`${this.url}/${i}/${s}${a}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(s){if(le(s))return{data:null,error:s};throw s}})}info(t){return Ce(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{const r=yield Lo(this.fetch,`${this.url}/object/info/${n}`,{headers:this.headers});return{data:ol(r),error:null}}catch(r){if(le(r))return{data:null,error:r};throw r}})}exists(t){return Ce(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{return yield D0(this.fetch,`${this.url}/object/${n}`,{headers:this.headers}),{data:!0,error:null}}catch(r){if(le(r)&&r instanceof il){const i=r.originalError;if([400,404].includes(i==null?void 0:i.status))return{data:!1,error:r}}throw r}})}getPublicUrl(t,n){const r=this._getFinalPath(t),i=[],o=n!=null&&n.download?`download=${n.download===!0?"":n.download}`:"";o!==""&&i.push(o);const s=typeof(n==null?void 0:n.transform)<"u"?"render/image":"object",l=this.transformOptsToQueryString((n==null?void 0:n.transform)||{});l!==""&&i.push(l);let u=i.join("&");return u!==""&&(u=`?${u}`),{data:{publicUrl:encodeURI(`${this.url}/${s}/public/${r}${u}`)}}}remove(t){return Ce(this,void 0,void 0,function*(){try{return{data:yield uh(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:t},{headers:this.headers}),error:null}}catch(n){if(le(n))return{data:null,error:n};throw n}})}list(t,n,r){return Ce(this,void 0,void 0,function*(){try{const i=Object.assign(Object.assign(Object.assign({},L0),n),{prefix:t||""});return{data:yield Tt(this.fetch,`${this.url}/object/list/${this.bucketId}`,i,{headers:this.headers},r),error:null}}catch(i){if(le(i))return{data:null,error:i};throw i}})}encodeMetadata(t){return JSON.stringify(t)}toBase64(t){return typeof Buffer<"u"?Buffer.from(t).toString("base64"):btoa(t)}_getFinalPath(t){return`${this.bucketId}/${t}`}_removeEmptyFolders(t){return t.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(t){const n=[];return t.width&&n.push(`width=${t.width}`),t.height&&n.push(`height=${t.height}`),t.resize&&n.push(`resize=${t.resize}`),t.format&&n.push(`format=${t.format}`),t.quality&&n.push(`quality=${t.quality}`),n.join("&")}}const z0="2.7.1",U0={"X-Client-Info":`storage-js/${z0}`};var Tn=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(c){try{u(r.next(c))}catch(d){a(d)}}function l(c){try{u(r.throw(c))}catch(d){a(d)}}function u(c){c.done?o(c.value):i(c.value).then(s,l)}u((r=r.apply(e,t||[])).next())})};class F0{constructor(t,n={},r){this.url=t,this.headers=Object.assign(Object.assign({},U0),n),this.fetch=lh(r)}listBuckets(){return Tn(this,void 0,void 0,function*(){try{return{data:yield Lo(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(t){if(le(t))return{data:null,error:t};throw t}})}getBucket(t){return Tn(this,void 0,void 0,function*(){try{return{data:yield Lo(this.fetch,`${this.url}/bucket/${t}`,{headers:this.headers}),error:null}}catch(n){if(le(n))return{data:null,error:n};throw n}})}createBucket(t,n={public:!1}){return Tn(this,void 0,void 0,function*(){try{return{data:yield Tt(this.fetch,`${this.url}/bucket`,{id:t,name:t,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(le(r))return{data:null,error:r};throw r}})}updateBucket(t,n){return Tn(this,void 0,void 0,function*(){try{return{data:yield N0(this.fetch,`${this.url}/bucket/${t}`,{id:t,name:t,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(le(r))return{data:null,error:r};throw r}})}emptyBucket(t){return Tn(this,void 0,void 0,function*(){try{return{data:yield Tt(this.fetch,`${this.url}/bucket/${t}/empty`,{},{headers:this.headers}),error:null}}catch(n){if(le(n))return{data:null,error:n};throw n}})}deleteBucket(t){return Tn(this,void 0,void 0,function*(){try{return{data:yield uh(this.fetch,`${this.url}/bucket/${t}`,{},{headers:this.headers}),error:null}}catch(n){if(le(n))return{data:null,error:n};throw n}})}}class M0 extends F0{constructor(t,n={},r){super(t,n,r)}from(t){return new $0(this.url,this.headers,t,this.fetch)}}const B0="2.50.2";let Rr="";typeof Deno<"u"?Rr="deno":typeof document<"u"?Rr="web":typeof navigator<"u"&&navigator.product==="ReactNative"?Rr="react-native":Rr="node";const W0={"X-Client-Info":`supabase-js-${Rr}/${B0}`},q0={headers:W0},V0={schema:"public"},H0={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},K0={};var G0=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(c){try{u(r.next(c))}catch(d){a(d)}}function l(c){try{u(r.throw(c))}catch(d){a(d)}}function u(c){c.done?o(c.value):i(c.value).then(s,l)}u((r=r.apply(e,t||[])).next())})};const J0=e=>{let t;return e?t=e:typeof fetch>"u"?t=Kf:t=fetch,(...n)=>t(...n)},Q0=()=>typeof Headers>"u"?Gf:Headers,Y0=(e,t,n)=>{const r=J0(n),i=Q0();return(o,a)=>G0(void 0,void 0,void 0,function*(){var s;const l=(s=yield t())!==null&&s!==void 0?s:e;let u=new i(a==null?void 0:a.headers);return u.has("apikey")||u.set("apikey",e),u.has("Authorization")||u.set("Authorization",`Bearer ${l}`),r(o,Object.assign(Object.assign({},a),{headers:u}))})};var X0=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(c){try{u(r.next(c))}catch(d){a(d)}}function l(c){try{u(r.throw(c))}catch(d){a(d)}}function u(c){c.done?o(c.value):i(c.value).then(s,l)}u((r=r.apply(e,t||[])).next())})};function Z0(e){return e.endsWith("/")?e:e+"/"}function ew(e,t){var n,r;const{db:i,auth:o,realtime:a,global:s}=e,{db:l,auth:u,realtime:c,global:d}=t,p={db:Object.assign(Object.assign({},l),i),auth:Object.assign(Object.assign({},u),o),realtime:Object.assign(Object.assign({},c),a),global:Object.assign(Object.assign(Object.assign({},d),s),{headers:Object.assign(Object.assign({},(n=d==null?void 0:d.headers)!==null&&n!==void 0?n:{}),(r=s==null?void 0:s.headers)!==null&&r!==void 0?r:{})}),accessToken:()=>X0(this,void 0,void 0,function*(){return""})};return e.accessToken?p.accessToken=e.accessToken:delete p.accessToken,p}const ch="2.70.0",An=30*1e3,al=3,Za=al*An,tw="http://localhost:9999",nw="supabase.auth.token",rw={"X-Client-Info":`gotrue-js/${ch}`},sl="X-Supabase-Api-Version",dh={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},iw=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,ow=6e5;class yu extends Error{constructor(t,n,r){super(t),this.__isAuthError=!0,this.name="AuthError",this.status=n,this.code=r}}function A(e){return typeof e=="object"&&e!==null&&"__isAuthError"in e}class aw extends yu{constructor(t,n,r){super(t,n,r),this.name="AuthApiError",this.status=n,this.code=r}}function sw(e){return A(e)&&e.name==="AuthApiError"}class ph extends yu{constructor(t,n){super(t),this.name="AuthUnknownError",this.originalError=n}}class Zt extends yu{constructor(t,n,r,i){super(t,r,i),this.name=n,this.status=r}}class jt extends Zt{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function lw(e){return A(e)&&e.name==="AuthSessionMissingError"}class Wi extends Zt{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class qi extends Zt{constructor(t){super(t,"AuthInvalidCredentialsError",400,void 0)}}class Vi extends Zt{constructor(t,n=null){super(t,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function uw(e){return A(e)&&e.name==="AuthImplicitGrantRedirectError"}class Zc extends Zt{constructor(t,n=null){super(t,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class ll extends Zt{constructor(t,n){super(t,"AuthRetryableFetchError",n,void 0)}}function es(e){return A(e)&&e.name==="AuthRetryableFetchError"}class ed extends Zt{constructor(t,n,r){super(t,"AuthWeakPasswordError",n,"weak_password"),this.reasons=r}}class Wr extends Zt{constructor(t){super(t,"AuthInvalidJwtError",400,"invalid_jwt")}}const $o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),td=` 	
\r=`.split(""),cw=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<td.length;t+=1)e[td[t].charCodeAt(0)]=-2;for(let t=0;t<$o.length;t+=1)e[$o[t].charCodeAt(0)]=t;return e})();function nd(e,t,n){if(e!==null)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;n($o[r]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;n($o[r]),t.queuedBits-=6}}function fh(e,t,n){const r=cw[e];if(r>-1)for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)n(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else{if(r===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}}function rd(e){const t=[],n=a=>{t.push(String.fromCodePoint(a))},r={utf8seq:0,codepoint:0},i={queue:0,queuedBits:0},o=a=>{fw(a,r,n)};for(let a=0;a<e.length;a+=1)fh(e.charCodeAt(a),i,o);return t.join("")}function dw(e,t){if(e<=127){t(e);return}else if(e<=2047){t(192|e>>6),t(128|e&63);return}else if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|e&63);return}else if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|e&63);return}throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}function pw(e,t){for(let n=0;n<e.length;n+=1){let r=e.charCodeAt(n);if(r>55295&&r<=56319){const i=(r-55296)*1024&65535;r=(e.charCodeAt(n+1)-56320&65535|i)+65536,n+=1}dw(r,t)}}function fw(e,t,n){if(t.utf8seq===0){if(e<=127){n(e);return}for(let r=1;r<6;r+=1)if(!(e>>7-r&1)){t.utf8seq=r;break}if(t.utf8seq===2)t.codepoint=e&31;else if(t.utf8seq===3)t.codepoint=e&15;else if(t.utf8seq===4)t.codepoint=e&7;else throw new Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|e&63,t.utf8seq-=1,t.utf8seq===0&&n(t.codepoint)}}function hw(e){const t=[],n={queue:0,queuedBits:0},r=i=>{t.push(i)};for(let i=0;i<e.length;i+=1)fh(e.charCodeAt(i),n,r);return new Uint8Array(t)}function mw(e){const t=[];return pw(e,n=>t.push(n)),new Uint8Array(t)}function vw(e){const t=[],n={queue:0,queuedBits:0},r=i=>{t.push(i)};return e.forEach(i=>nd(i,n,r)),nd(null,n,r),t.join("")}function gw(e){return Math.round(Date.now()/1e3)+e}function yw(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=Math.random()*16|0;return(e=="x"?t:t&3|8).toString(16)})}const Je=()=>typeof window<"u"&&typeof document<"u",nn={tested:!1,writable:!1},qr=()=>{if(!Je())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if(nn.tested)return nn.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),nn.tested=!0,nn.writable=!0}catch{nn.tested=!0,nn.writable=!1}return nn.writable};function ww(e){const t={},n=new URL(e);if(n.hash&&n.hash[0]==="#")try{new URLSearchParams(n.hash.substring(1)).forEach((i,o)=>{t[o]=i})}catch{}return n.searchParams.forEach((r,i)=>{t[i]=r}),t}const hh=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>yi(()=>Promise.resolve().then(()=>hr),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},xw=e=>typeof e=="object"&&e!==null&&"status"in e&&"ok"in e&&"json"in e&&typeof e.json=="function",mh=async(e,t,n)=>{await e.setItem(t,JSON.stringify(n))},Hi=async(e,t)=>{const n=await e.getItem(t);if(!n)return null;try{return JSON.parse(n)}catch{return n}},Ki=async(e,t)=>{await e.removeItem(t)};class pa{constructor(){this.promise=new pa.promiseConstructor((t,n)=>{this.resolve=t,this.reject=n})}}pa.promiseConstructor=Promise;function ts(e){const t=e.split(".");if(t.length!==3)throw new Wr("Invalid JWT structure");for(let r=0;r<t.length;r++)if(!iw.test(t[r]))throw new Wr("JWT not in base64url format");return{header:JSON.parse(rd(t[0])),payload:JSON.parse(rd(t[1])),signature:hw(t[2]),raw:{header:t[0],payload:t[1]}}}async function _w(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function kw(e,t){return new Promise((r,i)=>{(async()=>{for(let o=0;o<1/0;o++)try{const a=await e(o);if(!t(o,null,a)){r(a);return}}catch(a){if(!t(o,a)){i(a);return}}})()})}function Sw(e){return("0"+e.toString(16)).substr(-2)}function Ew(){const t=new Uint32Array(56);if(typeof crypto>"u"){const n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",r=n.length;let i="";for(let o=0;o<56;o++)i+=n.charAt(Math.floor(Math.random()*r));return i}return crypto.getRandomValues(t),Array.from(t,Sw).join("")}async function bw(e){const n=new TextEncoder().encode(e),r=await crypto.subtle.digest("SHA-256",n),i=new Uint8Array(r);return Array.from(i).map(o=>String.fromCharCode(o)).join("")}async function jw(e){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const n=await bw(e);return btoa(n).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function On(e,t,n=!1){const r=Ew();let i=r;n&&(i+="/PASSWORD_RECOVERY"),await mh(e,`${t}-code-verifier`,i);const o=await jw(r);return[o,r===o?"plain":"s256"]}const Cw=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function Pw(e){const t=e.headers.get(sl);if(!t||!t.match(Cw))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch{return null}}function Tw(e){if(!e)throw new Error("Missing exp claim");const t=Math.floor(Date.now()/1e3);if(e<=t)throw new Error("JWT has expired")}function Ow(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}const Rw=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function Rn(e){if(!Rw.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var Iw=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n};const an=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Aw=[502,503,504];async function id(e){var t;if(!xw(e))throw new ll(an(e),0);if(Aw.includes(e.status))throw new ll(an(e),e.status);let n;try{n=await e.json()}catch(o){throw new ph(an(o),o)}let r;const i=Pw(e);if(i&&i.getTime()>=dh["2024-01-01"].timestamp&&typeof n=="object"&&n&&typeof n.code=="string"?r=n.code:typeof n=="object"&&n&&typeof n.error_code=="string"&&(r=n.error_code),r){if(r==="weak_password")throw new ed(an(n),e.status,((t=n.weak_password)===null||t===void 0?void 0:t.reasons)||[]);if(r==="session_not_found")throw new jt}else if(typeof n=="object"&&n&&typeof n.weak_password=="object"&&n.weak_password&&Array.isArray(n.weak_password.reasons)&&n.weak_password.reasons.length&&n.weak_password.reasons.reduce((o,a)=>o&&typeof a=="string",!0))throw new ed(an(n),e.status,n.weak_password.reasons);throw new aw(an(n),e.status||500,r)}const Nw=(e,t,n,r)=>{const i={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?i:(i.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},t==null?void 0:t.headers),i.body=JSON.stringify(r),Object.assign(Object.assign({},i),n))};async function L(e,t,n,r){var i;const o=Object.assign({},r==null?void 0:r.headers);o[sl]||(o[sl]=dh["2024-01-01"].name),r!=null&&r.jwt&&(o.Authorization=`Bearer ${r.jwt}`);const a=(i=r==null?void 0:r.query)!==null&&i!==void 0?i:{};r!=null&&r.redirectTo&&(a.redirect_to=r.redirectTo);const s=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",l=await Dw(e,t,n+s,{headers:o,noResolveJson:r==null?void 0:r.noResolveJson},{},r==null?void 0:r.body);return r!=null&&r.xform?r==null?void 0:r.xform(l):{data:Object.assign({},l),error:null}}async function Dw(e,t,n,r,i,o){const a=Nw(t,r,i,o);let s;try{s=await e(n,Object.assign({},a))}catch(l){throw console.error(l),new ll(an(l),0)}if(s.ok||await id(s),r!=null&&r.noResolveJson)return s;try{return await s.json()}catch(l){await id(l)}}function pt(e){var t;let n=null;Uw(e)&&(n=Object.assign({},e),e.expires_at||(n.expires_at=gw(e.expires_in)));const r=(t=e.user)!==null&&t!==void 0?t:e;return{data:{session:n,user:r},error:null}}function od(e){const t=pt(e);return!t.error&&e.weak_password&&typeof e.weak_password=="object"&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&typeof e.weak_password.message=="string"&&e.weak_password.reasons.reduce((n,r)=>n&&typeof r=="string",!0)&&(t.data.weak_password=e.weak_password),t}function It(e){var t;return{data:{user:(t=e.user)!==null&&t!==void 0?t:e},error:null}}function Lw(e){return{data:e,error:null}}function $w(e){const{action_link:t,email_otp:n,hashed_token:r,redirect_to:i,verification_type:o}=e,a=Iw(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),s={action_link:t,email_otp:n,hashed_token:r,redirect_to:i,verification_type:o},l=Object.assign({},a);return{data:{properties:s,user:l},error:null}}function zw(e){return e}function Uw(e){return e.access_token&&e.refresh_token&&e.expires_in}const ns=["global","local","others"];var Fw=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n};class Mw{constructor({url:t="",headers:n={},fetch:r}){this.url=t,this.headers=n,this.fetch=hh(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(t,n=ns[0]){if(ns.indexOf(n)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${ns.join(", ")}`);try{return await L(this.fetch,"POST",`${this.url}/logout?scope=${n}`,{headers:this.headers,jwt:t,noResolveJson:!0}),{data:null,error:null}}catch(r){if(A(r))return{data:null,error:r};throw r}}async inviteUserByEmail(t,n={}){try{return await L(this.fetch,"POST",`${this.url}/invite`,{body:{email:t,data:n.data},headers:this.headers,redirectTo:n.redirectTo,xform:It})}catch(r){if(A(r))return{data:{user:null},error:r};throw r}}async generateLink(t){try{const{options:n}=t,r=Fw(t,["options"]),i=Object.assign(Object.assign({},r),n);return"newEmail"in r&&(i.new_email=r==null?void 0:r.newEmail,delete i.newEmail),await L(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:i,headers:this.headers,xform:$w,redirectTo:n==null?void 0:n.redirectTo})}catch(n){if(A(n))return{data:{properties:null,user:null},error:n};throw n}}async createUser(t){try{return await L(this.fetch,"POST",`${this.url}/admin/users`,{body:t,headers:this.headers,xform:It})}catch(n){if(A(n))return{data:{user:null},error:n};throw n}}async listUsers(t){var n,r,i,o,a,s,l;try{const u={nextPage:null,lastPage:0,total:0},c=await L(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(r=(n=t==null?void 0:t.page)===null||n===void 0?void 0:n.toString())!==null&&r!==void 0?r:"",per_page:(o=(i=t==null?void 0:t.perPage)===null||i===void 0?void 0:i.toString())!==null&&o!==void 0?o:""},xform:zw});if(c.error)throw c.error;const d=await c.json(),p=(a=c.headers.get("x-total-count"))!==null&&a!==void 0?a:0,g=(l=(s=c.headers.get("link"))===null||s===void 0?void 0:s.split(","))!==null&&l!==void 0?l:[];return g.length>0&&(g.forEach(y=>{const w=parseInt(y.split(";")[0].split("=")[1].substring(0,1)),_=JSON.parse(y.split(";")[1].split("=")[1]);u[`${_}Page`]=w}),u.total=parseInt(p)),{data:Object.assign(Object.assign({},d),u),error:null}}catch(u){if(A(u))return{data:{users:[]},error:u};throw u}}async getUserById(t){Rn(t);try{return await L(this.fetch,"GET",`${this.url}/admin/users/${t}`,{headers:this.headers,xform:It})}catch(n){if(A(n))return{data:{user:null},error:n};throw n}}async updateUserById(t,n){Rn(t);try{return await L(this.fetch,"PUT",`${this.url}/admin/users/${t}`,{body:n,headers:this.headers,xform:It})}catch(r){if(A(r))return{data:{user:null},error:r};throw r}}async deleteUser(t,n=!1){Rn(t);try{return await L(this.fetch,"DELETE",`${this.url}/admin/users/${t}`,{headers:this.headers,body:{should_soft_delete:n},xform:It})}catch(r){if(A(r))return{data:{user:null},error:r};throw r}}async _listFactors(t){Rn(t.userId);try{const{data:n,error:r}=await L(this.fetch,"GET",`${this.url}/admin/users/${t.userId}/factors`,{headers:this.headers,xform:i=>({data:{factors:i},error:null})});return{data:n,error:r}}catch(n){if(A(n))return{data:null,error:n};throw n}}async _deleteFactor(t){Rn(t.userId),Rn(t.id);try{return{data:await L(this.fetch,"DELETE",`${this.url}/admin/users/${t.userId}/factors/${t.id}`,{headers:this.headers}),error:null}}catch(n){if(A(n))return{data:null,error:n};throw n}}}const Bw={getItem:e=>qr()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{qr()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{qr()&&globalThis.localStorage.removeItem(e)}};function ad(e={}){return{getItem:t=>e[t]||null,setItem:(t,n)=>{e[t]=n},removeItem:t=>{delete e[t]}}}function Ww(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}const In={debug:!!(globalThis&&qr()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class vh extends Error{constructor(t){super(t),this.isAcquireTimeout=!0}}class qw extends vh{}async function Vw(e,t,n){In.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const r=new globalThis.AbortController;return t>0&&setTimeout(()=>{r.abort(),In.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,t===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},async i=>{if(i){In.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,i.name);try{return await n()}finally{In.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,i.name)}}else{if(t===0)throw In.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new qw(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(In.debug)try{const o=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(o,null,"  "))}catch(o){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",o)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await n()}}))}Ww();const Hw={url:tw,storageKey:nw,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:rw,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function sd(e,t,n){return await n()}class ci{constructor(t){var n,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=ci.nextInstanceID,ci.nextInstanceID+=1,this.instanceID>0&&Je()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const i=Object.assign(Object.assign({},Hw),t);if(this.logDebugMessages=!!i.debug,typeof i.debug=="function"&&(this.logger=i.debug),this.persistSession=i.persistSession,this.storageKey=i.storageKey,this.autoRefreshToken=i.autoRefreshToken,this.admin=new Mw({url:i.url,headers:i.headers,fetch:i.fetch}),this.url=i.url,this.headers=i.headers,this.fetch=hh(i.fetch),this.lock=i.lock||sd,this.detectSessionInUrl=i.detectSessionInUrl,this.flowType=i.flowType,this.hasCustomAuthorizationHeader=i.hasCustomAuthorizationHeader,i.lock?this.lock=i.lock:Je()&&(!((n=globalThis==null?void 0:globalThis.navigator)===null||n===void 0)&&n.locks)?this.lock=Vw:this.lock=sd,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?i.storage?this.storage=i.storage:qr()?this.storage=Bw:(this.memoryStorage={},this.storage=ad(this.memoryStorage)):(this.memoryStorage={},this.storage=ad(this.memoryStorage)),Je()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(o){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",o)}(r=this.broadcastChannel)===null||r===void 0||r.addEventListener("message",async o=>{this._debug("received broadcast notification from other tab or client",o),await this._notifyAllSubscribers(o.data.event,o.data.session,!1)})}this.initialize()}_debug(...t){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${ch}) ${new Date().toISOString()}`,...t),this}async initialize(){return this.initializePromise?await this.initializePromise:(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))(),await this.initializePromise)}async _initialize(){var t;try{const n=ww(window.location.href);let r="none";if(this._isImplicitGrantCallback(n)?r="implicit":await this._isPKCECallback(n)&&(r="pkce"),Je()&&this.detectSessionInUrl&&r!=="none"){const{data:i,error:o}=await this._getSessionFromURL(n,r);if(o){if(this._debug("#_initialize()","error detecting session from URL",o),uw(o)){const l=(t=o.details)===null||t===void 0?void 0:t.code;if(l==="identity_already_exists"||l==="identity_not_found"||l==="single_identity_not_deletable")return{error:o}}return await this._removeSession(),{error:o}}const{session:a,redirectType:s}=i;return this._debug("#_initialize()","detected session in URL",a,"redirect type",s),await this._saveSession(a),setTimeout(async()=>{s==="recovery"?await this._notifyAllSubscribers("PASSWORD_RECOVERY",a):await this._notifyAllSubscribers("SIGNED_IN",a)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(n){return A(n)?{error:n}:{error:new ph("Unexpected error during initialization",n)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(t){var n,r,i;try{const o=await L(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(r=(n=t==null?void 0:t.options)===null||n===void 0?void 0:n.data)!==null&&r!==void 0?r:{},gotrue_meta_security:{captcha_token:(i=t==null?void 0:t.options)===null||i===void 0?void 0:i.captchaToken}},xform:pt}),{data:a,error:s}=o;if(s||!a)return{data:{user:null,session:null},error:s};const l=a.session,u=a.user;return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(o){if(A(o))return{data:{user:null,session:null},error:o};throw o}}async signUp(t){var n,r,i;try{let o;if("email"in t){const{email:c,password:d,options:p}=t;let g=null,y=null;this.flowType==="pkce"&&([g,y]=await On(this.storage,this.storageKey)),o=await L(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:p==null?void 0:p.emailRedirectTo,body:{email:c,password:d,data:(n=p==null?void 0:p.data)!==null&&n!==void 0?n:{},gotrue_meta_security:{captcha_token:p==null?void 0:p.captchaToken},code_challenge:g,code_challenge_method:y},xform:pt})}else if("phone"in t){const{phone:c,password:d,options:p}=t;o=await L(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:c,password:d,data:(r=p==null?void 0:p.data)!==null&&r!==void 0?r:{},channel:(i=p==null?void 0:p.channel)!==null&&i!==void 0?i:"sms",gotrue_meta_security:{captcha_token:p==null?void 0:p.captchaToken}},xform:pt})}else throw new qi("You must provide either an email or phone number and a password");const{data:a,error:s}=o;if(s||!a)return{data:{user:null,session:null},error:s};const l=a.session,u=a.user;return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(o){if(A(o))return{data:{user:null,session:null},error:o};throw o}}async signInWithPassword(t){try{let n;if("email"in t){const{email:o,password:a,options:s}=t;n=await L(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:o,password:a,gotrue_meta_security:{captcha_token:s==null?void 0:s.captchaToken}},xform:od})}else if("phone"in t){const{phone:o,password:a,options:s}=t;n=await L(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:o,password:a,gotrue_meta_security:{captcha_token:s==null?void 0:s.captchaToken}},xform:od})}else throw new qi("You must provide either an email or phone number and a password");const{data:r,error:i}=n;return i?{data:{user:null,session:null},error:i}:!r||!r.session||!r.user?{data:{user:null,session:null},error:new Wi}:(r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:i})}catch(n){if(A(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOAuth(t){var n,r,i,o;return await this._handleProviderSignIn(t.provider,{redirectTo:(n=t.options)===null||n===void 0?void 0:n.redirectTo,scopes:(r=t.options)===null||r===void 0?void 0:r.scopes,queryParams:(i=t.options)===null||i===void 0?void 0:i.queryParams,skipBrowserRedirect:(o=t.options)===null||o===void 0?void 0:o.skipBrowserRedirect})}async exchangeCodeForSession(t){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(t))}async signInWithWeb3(t){const{chain:n}=t;if(n==="solana")return await this.signInWithSolana(t);throw new Error(`@supabase/auth-js: Unsupported chain "${n}"`)}async signInWithSolana(t){var n,r,i,o,a,s,l,u,c,d,p,g;let y,w;if("message"in t)y=t.message,w=t.signature;else{const{chain:_,wallet:m,statement:h,options:f}=t;let x;if(Je())if(typeof m=="object")x=m;else{const E=window;if("solana"in E&&typeof E.solana=="object"&&("signIn"in E.solana&&typeof E.solana.signIn=="function"||"signMessage"in E.solana&&typeof E.solana.signMessage=="function"))x=E.solana;else throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if(typeof m!="object"||!(f!=null&&f.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");x=m}const S=new URL((n=f==null?void 0:f.url)!==null&&n!==void 0?n:window.location.href);if("signIn"in x&&x.signIn){const E=await x.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},f==null?void 0:f.signInWithSolana),{version:"1",domain:S.host,uri:S.href}),h?{statement:h}:null));let j;if(Array.isArray(E)&&E[0]&&typeof E[0]=="object")j=E[0];else if(E&&typeof E=="object"&&"signedMessage"in E&&"signature"in E)j=E;else throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in j&&"signature"in j&&(typeof j.signedMessage=="string"||j.signedMessage instanceof Uint8Array)&&j.signature instanceof Uint8Array)y=typeof j.signedMessage=="string"?j.signedMessage:new TextDecoder().decode(j.signedMessage),w=j.signature;else throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in x)||typeof x.signMessage!="function"||!("publicKey"in x)||typeof x!="object"||!x.publicKey||!("toBase58"in x.publicKey)||typeof x.publicKey.toBase58!="function")throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");y=[`${S.host} wants you to sign in with your Solana account:`,x.publicKey.toBase58(),...h?["",h,""]:[""],"Version: 1",`URI: ${S.href}`,`Issued At: ${(i=(r=f==null?void 0:f.signInWithSolana)===null||r===void 0?void 0:r.issuedAt)!==null&&i!==void 0?i:new Date().toISOString()}`,...!((o=f==null?void 0:f.signInWithSolana)===null||o===void 0)&&o.notBefore?[`Not Before: ${f.signInWithSolana.notBefore}`]:[],...!((a=f==null?void 0:f.signInWithSolana)===null||a===void 0)&&a.expirationTime?[`Expiration Time: ${f.signInWithSolana.expirationTime}`]:[],...!((s=f==null?void 0:f.signInWithSolana)===null||s===void 0)&&s.chainId?[`Chain ID: ${f.signInWithSolana.chainId}`]:[],...!((l=f==null?void 0:f.signInWithSolana)===null||l===void 0)&&l.nonce?[`Nonce: ${f.signInWithSolana.nonce}`]:[],...!((u=f==null?void 0:f.signInWithSolana)===null||u===void 0)&&u.requestId?[`Request ID: ${f.signInWithSolana.requestId}`]:[],...!((d=(c=f==null?void 0:f.signInWithSolana)===null||c===void 0?void 0:c.resources)===null||d===void 0)&&d.length?["Resources",...f.signInWithSolana.resources.map(j=>`- ${j}`)]:[]].join(`
`);const E=await x.signMessage(new TextEncoder().encode(y),"utf8");if(!E||!(E instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");w=E}}try{const{data:_,error:m}=await L(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:y,signature:vw(w)},!((p=t.options)===null||p===void 0)&&p.captchaToken?{gotrue_meta_security:{captcha_token:(g=t.options)===null||g===void 0?void 0:g.captchaToken}}:null),xform:pt});if(m)throw m;return!_||!_.session||!_.user?{data:{user:null,session:null},error:new Wi}:(_.session&&(await this._saveSession(_.session),await this._notifyAllSubscribers("SIGNED_IN",_.session)),{data:Object.assign({},_),error:m})}catch(_){if(A(_))return{data:{user:null,session:null},error:_};throw _}}async _exchangeCodeForSession(t){const n=await Hi(this.storage,`${this.storageKey}-code-verifier`),[r,i]=(n??"").split("/");try{const{data:o,error:a}=await L(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:t,code_verifier:r},xform:pt});if(await Ki(this.storage,`${this.storageKey}-code-verifier`),a)throw a;return!o||!o.session||!o.user?{data:{user:null,session:null,redirectType:null},error:new Wi}:(o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",o.session)),{data:Object.assign(Object.assign({},o),{redirectType:i??null}),error:a})}catch(o){if(A(o))return{data:{user:null,session:null,redirectType:null},error:o};throw o}}async signInWithIdToken(t){try{const{options:n,provider:r,token:i,access_token:o,nonce:a}=t,s=await L(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:i,access_token:o,nonce:a,gotrue_meta_security:{captcha_token:n==null?void 0:n.captchaToken}},xform:pt}),{data:l,error:u}=s;return u?{data:{user:null,session:null},error:u}:!l||!l.session||!l.user?{data:{user:null,session:null},error:new Wi}:(l.session&&(await this._saveSession(l.session),await this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:l,error:u})}catch(n){if(A(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOtp(t){var n,r,i,o,a;try{if("email"in t){const{email:s,options:l}=t;let u=null,c=null;this.flowType==="pkce"&&([u,c]=await On(this.storage,this.storageKey));const{error:d}=await L(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:s,data:(n=l==null?void 0:l.data)!==null&&n!==void 0?n:{},create_user:(r=l==null?void 0:l.shouldCreateUser)!==null&&r!==void 0?r:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},code_challenge:u,code_challenge_method:c},redirectTo:l==null?void 0:l.emailRedirectTo});return{data:{user:null,session:null},error:d}}if("phone"in t){const{phone:s,options:l}=t,{data:u,error:c}=await L(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:s,data:(i=l==null?void 0:l.data)!==null&&i!==void 0?i:{},create_user:(o=l==null?void 0:l.shouldCreateUser)!==null&&o!==void 0?o:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},channel:(a=l==null?void 0:l.channel)!==null&&a!==void 0?a:"sms"}});return{data:{user:null,session:null,messageId:u==null?void 0:u.message_id},error:c}}throw new qi("You must provide either an email or phone number.")}catch(s){if(A(s))return{data:{user:null,session:null},error:s};throw s}}async verifyOtp(t){var n,r;try{let i,o;"options"in t&&(i=(n=t.options)===null||n===void 0?void 0:n.redirectTo,o=(r=t.options)===null||r===void 0?void 0:r.captchaToken);const{data:a,error:s}=await L(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},t),{gotrue_meta_security:{captcha_token:o}}),redirectTo:i,xform:pt});if(s)throw s;if(!a)throw new Error("An error occurred on token verification.");const l=a.session,u=a.user;return l!=null&&l.access_token&&(await this._saveSession(l),await this._notifyAllSubscribers(t.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(i){if(A(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithSSO(t){var n,r,i;try{let o=null,a=null;return this.flowType==="pkce"&&([o,a]=await On(this.storage,this.storageKey)),await L(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in t?{provider_id:t.providerId}:null),"domain"in t?{domain:t.domain}:null),{redirect_to:(r=(n=t.options)===null||n===void 0?void 0:n.redirectTo)!==null&&r!==void 0?r:void 0}),!((i=t==null?void 0:t.options)===null||i===void 0)&&i.captchaToken?{gotrue_meta_security:{captcha_token:t.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:o,code_challenge_method:a}),headers:this.headers,xform:Lw})}catch(o){if(A(o))return{data:null,error:o};throw o}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async t=>{const{data:{session:n},error:r}=t;if(r)throw r;if(!n)throw new jt;const{error:i}=await L(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:n.access_token});return{data:{user:null,session:null},error:i}})}catch(t){if(A(t))return{data:{user:null,session:null},error:t};throw t}}async resend(t){try{const n=`${this.url}/resend`;if("email"in t){const{email:r,type:i,options:o}=t,{error:a}=await L(this.fetch,"POST",n,{headers:this.headers,body:{email:r,type:i,gotrue_meta_security:{captcha_token:o==null?void 0:o.captchaToken}},redirectTo:o==null?void 0:o.emailRedirectTo});return{data:{user:null,session:null},error:a}}else if("phone"in t){const{phone:r,type:i,options:o}=t,{data:a,error:s}=await L(this.fetch,"POST",n,{headers:this.headers,body:{phone:r,type:i,gotrue_meta_security:{captcha_token:o==null?void 0:o.captchaToken}}});return{data:{user:null,session:null,messageId:a==null?void 0:a.message_id},error:s}}throw new qi("You must provide either an email or phone number and a type")}catch(n){if(A(n))return{data:{user:null,session:null},error:n};throw n}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async n=>n))}async _acquireLock(t,n){this._debug("#_acquireLock","begin",t);try{if(this.lockAcquired){const r=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),i=(async()=>(await r,await n()))();return this.pendingInLock.push((async()=>{try{await i}catch{}})()),i}return await this.lock(`lock:${this.storageKey}`,t,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const r=n();for(this.pendingInLock.push((async()=>{try{await r}catch{}})()),await r;this.pendingInLock.length;){const i=[...this.pendingInLock];await Promise.all(i),this.pendingInLock.splice(0,i.length)}return await r}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(t){this._debug("#_useSession","begin");try{const n=await this.__loadSession();return await t(n)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let t=null;const n=await Hi(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",n),n!==null&&(this._isValidSession(n)?t=n:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!t)return{data:{session:null},error:null};const r=t.expires_at?t.expires_at*1e3-Date.now()<Za:!1;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",t.expires_at),!r){if(this.storage.isServer){let a=this.suppressGetSessionWarning;t=new Proxy(t,{get:(l,u,c)=>(!a&&u==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),a=!0,this.suppressGetSessionWarning=!0),Reflect.get(l,u,c))})}return{data:{session:t},error:null}}const{session:i,error:o}=await this._callRefreshToken(t.refresh_token);return o?{data:{session:null},error:o}:{data:{session:i},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(t){return t?await this._getUser(t):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(t){try{return t?await L(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:t,xform:It}):await this._useSession(async n=>{var r,i,o;const{data:a,error:s}=n;if(s)throw s;return!(!((r=a.session)===null||r===void 0)&&r.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new jt}:await L(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(o=(i=a.session)===null||i===void 0?void 0:i.access_token)!==null&&o!==void 0?o:void 0,xform:It})})}catch(n){if(A(n))return lw(n)&&(await this._removeSession(),await Ki(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:n};throw n}}async updateUser(t,n={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(t,n))}async _updateUser(t,n={}){try{return await this._useSession(async r=>{const{data:i,error:o}=r;if(o)throw o;if(!i.session)throw new jt;const a=i.session;let s=null,l=null;this.flowType==="pkce"&&t.email!=null&&([s,l]=await On(this.storage,this.storageKey));const{data:u,error:c}=await L(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:n==null?void 0:n.emailRedirectTo,body:Object.assign(Object.assign({},t),{code_challenge:s,code_challenge_method:l}),jwt:a.access_token,xform:It});if(c)throw c;return a.user=u.user,await this._saveSession(a),await this._notifyAllSubscribers("USER_UPDATED",a),{data:{user:a.user},error:null}})}catch(r){if(A(r))return{data:{user:null},error:r};throw r}}async setSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(t))}async _setSession(t){try{if(!t.access_token||!t.refresh_token)throw new jt;const n=Date.now()/1e3;let r=n,i=!0,o=null;const{payload:a}=ts(t.access_token);if(a.exp&&(r=a.exp,i=r<=n),i){const{session:s,error:l}=await this._callRefreshToken(t.refresh_token);if(l)return{data:{user:null,session:null},error:l};if(!s)return{data:{user:null,session:null},error:null};o=s}else{const{data:s,error:l}=await this._getUser(t.access_token);if(l)throw l;o={access_token:t.access_token,refresh_token:t.refresh_token,user:s.user,token_type:"bearer",expires_in:r-n,expires_at:r},await this._saveSession(o),await this._notifyAllSubscribers("SIGNED_IN",o)}return{data:{user:o.user,session:o},error:null}}catch(n){if(A(n))return{data:{session:null,user:null},error:n};throw n}}async refreshSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(t))}async _refreshSession(t){try{return await this._useSession(async n=>{var r;if(!t){const{data:a,error:s}=n;if(s)throw s;t=(r=a.session)!==null&&r!==void 0?r:void 0}if(!(t!=null&&t.refresh_token))throw new jt;const{session:i,error:o}=await this._callRefreshToken(t.refresh_token);return o?{data:{user:null,session:null},error:o}:i?{data:{user:i.user,session:i},error:null}:{data:{user:null,session:null},error:null}})}catch(n){if(A(n))return{data:{user:null,session:null},error:n};throw n}}async _getSessionFromURL(t,n){try{if(!Je())throw new Vi("No browser detected.");if(t.error||t.error_description||t.error_code)throw new Vi(t.error_description||"Error in URL with unspecified error_description",{error:t.error||"unspecified_error",code:t.error_code||"unspecified_code"});switch(n){case"implicit":if(this.flowType==="pkce")throw new Zc("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new Vi("Not a valid implicit grant flow url.");break;default:}if(n==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!t.code)throw new Zc("No code detected.");const{data:h,error:f}=await this._exchangeCodeForSession(t.code);if(f)throw f;const x=new URL(window.location.href);return x.searchParams.delete("code"),window.history.replaceState(window.history.state,"",x.toString()),{data:{session:h.session,redirectType:null},error:null}}const{provider_token:r,provider_refresh_token:i,access_token:o,refresh_token:a,expires_in:s,expires_at:l,token_type:u}=t;if(!o||!s||!a||!u)throw new Vi("No session defined in URL");const c=Math.round(Date.now()/1e3),d=parseInt(s);let p=c+d;l&&(p=parseInt(l));const g=p-c;g*1e3<=An&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${g}s, should have been closer to ${d}s`);const y=p-d;c-y>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",y,p,c):c-y<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",y,p,c);const{data:w,error:_}=await this._getUser(o);if(_)throw _;const m={provider_token:r,provider_refresh_token:i,access_token:o,expires_in:d,expires_at:p,refresh_token:a,token_type:u,user:w.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:m,redirectType:t.type},error:null}}catch(r){if(A(r))return{data:{session:null,redirectType:null},error:r};throw r}}_isImplicitGrantCallback(t){return!!(t.access_token||t.error_description)}async _isPKCECallback(t){const n=await Hi(this.storage,`${this.storageKey}-code-verifier`);return!!(t.code&&n)}async signOut(t={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(t))}async _signOut({scope:t}={scope:"global"}){return await this._useSession(async n=>{var r;const{data:i,error:o}=n;if(o)return{error:o};const a=(r=i.session)===null||r===void 0?void 0:r.access_token;if(a){const{error:s}=await this.admin.signOut(a,t);if(s&&!(sw(s)&&(s.status===404||s.status===401||s.status===403)))return{error:s}}return t!=="others"&&(await this._removeSession(),await Ki(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(t){const n=yw(),r={id:n,callback:t,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",n),this.stateChangeEmitters.delete(n)}};return this._debug("#onAuthStateChange()","registered callback with id",n),this.stateChangeEmitters.set(n,r),(async()=>(await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(n)})))(),{data:{subscription:r}}}async _emitInitialSession(t){return await this._useSession(async n=>{var r,i;try{const{data:{session:o},error:a}=n;if(a)throw a;await((r=this.stateChangeEmitters.get(t))===null||r===void 0?void 0:r.callback("INITIAL_SESSION",o)),this._debug("INITIAL_SESSION","callback id",t,"session",o)}catch(o){await((i=this.stateChangeEmitters.get(t))===null||i===void 0?void 0:i.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",t,"error",o),console.error(o)}})}async resetPasswordForEmail(t,n={}){let r=null,i=null;this.flowType==="pkce"&&([r,i]=await On(this.storage,this.storageKey,!0));try{return await L(this.fetch,"POST",`${this.url}/recover`,{body:{email:t,code_challenge:r,code_challenge_method:i,gotrue_meta_security:{captcha_token:n.captchaToken}},headers:this.headers,redirectTo:n.redirectTo})}catch(o){if(A(o))return{data:null,error:o};throw o}}async getUserIdentities(){var t;try{const{data:n,error:r}=await this.getUser();if(r)throw r;return{data:{identities:(t=n.user.identities)!==null&&t!==void 0?t:[]},error:null}}catch(n){if(A(n))return{data:null,error:n};throw n}}async linkIdentity(t){var n;try{const{data:r,error:i}=await this._useSession(async o=>{var a,s,l,u,c;const{data:d,error:p}=o;if(p)throw p;const g=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,t.provider,{redirectTo:(a=t.options)===null||a===void 0?void 0:a.redirectTo,scopes:(s=t.options)===null||s===void 0?void 0:s.scopes,queryParams:(l=t.options)===null||l===void 0?void 0:l.queryParams,skipBrowserRedirect:!0});return await L(this.fetch,"GET",g,{headers:this.headers,jwt:(c=(u=d.session)===null||u===void 0?void 0:u.access_token)!==null&&c!==void 0?c:void 0})});if(i)throw i;return Je()&&!(!((n=t.options)===null||n===void 0)&&n.skipBrowserRedirect)&&window.location.assign(r==null?void 0:r.url),{data:{provider:t.provider,url:r==null?void 0:r.url},error:null}}catch(r){if(A(r))return{data:{provider:t.provider,url:null},error:r};throw r}}async unlinkIdentity(t){try{return await this._useSession(async n=>{var r,i;const{data:o,error:a}=n;if(a)throw a;return await L(this.fetch,"DELETE",`${this.url}/user/identities/${t.identity_id}`,{headers:this.headers,jwt:(i=(r=o.session)===null||r===void 0?void 0:r.access_token)!==null&&i!==void 0?i:void 0})})}catch(n){if(A(n))return{data:null,error:n};throw n}}async _refreshAccessToken(t){const n=`#_refreshAccessToken(${t.substring(0,5)}...)`;this._debug(n,"begin");try{const r=Date.now();return await kw(async i=>(i>0&&await _w(200*Math.pow(2,i-1)),this._debug(n,"refreshing attempt",i),await L(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:t},headers:this.headers,xform:pt})),(i,o)=>{const a=200*Math.pow(2,i);return o&&es(o)&&Date.now()+a-r<An})}catch(r){if(this._debug(n,"error",r),A(r))return{data:{session:null,user:null},error:r};throw r}finally{this._debug(n,"end")}}_isValidSession(t){return typeof t=="object"&&t!==null&&"access_token"in t&&"refresh_token"in t&&"expires_at"in t}async _handleProviderSignIn(t,n){const r=await this._getUrlForProvider(`${this.url}/authorize`,t,{redirectTo:n.redirectTo,scopes:n.scopes,queryParams:n.queryParams});return this._debug("#_handleProviderSignIn()","provider",t,"options",n,"url",r),Je()&&!n.skipBrowserRedirect&&window.location.assign(r),{data:{provider:t,url:r},error:null}}async _recoverAndRefresh(){var t;const n="#_recoverAndRefresh()";this._debug(n,"begin");try{const r=await Hi(this.storage,this.storageKey);if(this._debug(n,"session from storage",r),!this._isValidSession(r)){this._debug(n,"session is not valid"),r!==null&&await this._removeSession();return}const i=((t=r.expires_at)!==null&&t!==void 0?t:1/0)*1e3-Date.now()<Za;if(this._debug(n,`session has${i?"":" not"} expired with margin of ${Za}s`),i){if(this.autoRefreshToken&&r.refresh_token){const{error:o}=await this._callRefreshToken(r.refresh_token);o&&(console.error(o),es(o)||(this._debug(n,"refresh failed with a non-retryable error, removing the session",o),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(r){this._debug(n,"error",r),console.error(r);return}finally{this._debug(n,"end")}}async _callRefreshToken(t){var n,r;if(!t)throw new jt;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const i=`#_callRefreshToken(${t.substring(0,5)}...)`;this._debug(i,"begin");try{this.refreshingDeferred=new pa;const{data:o,error:a}=await this._refreshAccessToken(t);if(a)throw a;if(!o.session)throw new jt;await this._saveSession(o.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",o.session);const s={session:o.session,error:null};return this.refreshingDeferred.resolve(s),s}catch(o){if(this._debug(i,"error",o),A(o)){const a={session:null,error:o};return es(o)||await this._removeSession(),(n=this.refreshingDeferred)===null||n===void 0||n.resolve(a),a}throw(r=this.refreshingDeferred)===null||r===void 0||r.reject(o),o}finally{this.refreshingDeferred=null,this._debug(i,"end")}}async _notifyAllSubscribers(t,n,r=!0){const i=`#_notifyAllSubscribers(${t})`;this._debug(i,"begin",n,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:t,session:n});const o=[],a=Array.from(this.stateChangeEmitters.values()).map(async s=>{try{await s.callback(t,n)}catch(l){o.push(l)}});if(await Promise.all(a),o.length>0){for(let s=0;s<o.length;s+=1)console.error(o[s]);throw o[0]}}finally{this._debug(i,"end")}}async _saveSession(t){this._debug("#_saveSession()",t),this.suppressGetSessionWarning=!0,await mh(this.storage,this.storageKey,t)}async _removeSession(){this._debug("#_removeSession()"),await Ki(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const t=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{t&&Je()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",t)}catch(n){console.error("removing visibilitychange callback failed",n)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const t=setInterval(()=>this._autoRefreshTokenTick(),An);this.autoRefreshTicker=t,t&&typeof t=="object"&&typeof t.unref=="function"?t.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(t),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const t=this.autoRefreshTicker;this.autoRefreshTicker=null,t&&clearInterval(t)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async n=>{const{data:{session:r}}=n;if(!r||!r.refresh_token||!r.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const i=Math.floor((r.expires_at*1e3-t)/An);this._debug("#_autoRefreshTokenTick()",`access token expires in ${i} ticks, a tick lasts ${An}ms, refresh threshold is ${al} ticks`),i<=al&&await this._callRefreshToken(r.refresh_token)})}catch(n){console.error("Auto refresh tick failed with error. This is likely a transient error.",n)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(t){if(t.isAcquireTimeout||t instanceof vh)this._debug("auto refresh token tick lock not available");else throw t}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!Je()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(t){console.error("_handleVisibilityChange",t)}}async _onVisibilityChanged(t){const n=`#_onVisibilityChanged(${t})`;this._debug(n,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),t||(await this.initializePromise,await this._acquireLock(-1,async()=>{if(document.visibilityState!=="visible"){this._debug(n,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(t,n,r){const i=[`provider=${encodeURIComponent(n)}`];if(r!=null&&r.redirectTo&&i.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),r!=null&&r.scopes&&i.push(`scopes=${encodeURIComponent(r.scopes)}`),this.flowType==="pkce"){const[o,a]=await On(this.storage,this.storageKey),s=new URLSearchParams({code_challenge:`${encodeURIComponent(o)}`,code_challenge_method:`${encodeURIComponent(a)}`});i.push(s.toString())}if(r!=null&&r.queryParams){const o=new URLSearchParams(r.queryParams);i.push(o.toString())}return r!=null&&r.skipBrowserRedirect&&i.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${t}?${i.join("&")}`}async _unenroll(t){try{return await this._useSession(async n=>{var r;const{data:i,error:o}=n;return o?{data:null,error:o}:await L(this.fetch,"DELETE",`${this.url}/factors/${t.factorId}`,{headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(A(n))return{data:null,error:n};throw n}}async _enroll(t){try{return await this._useSession(async n=>{var r,i;const{data:o,error:a}=n;if(a)return{data:null,error:a};const s=Object.assign({friendly_name:t.friendlyName,factor_type:t.factorType},t.factorType==="phone"?{phone:t.phone}:{issuer:t.issuer}),{data:l,error:u}=await L(this.fetch,"POST",`${this.url}/factors`,{body:s,headers:this.headers,jwt:(r=o==null?void 0:o.session)===null||r===void 0?void 0:r.access_token});return u?{data:null,error:u}:(t.factorType==="totp"&&(!((i=l==null?void 0:l.totp)===null||i===void 0)&&i.qr_code)&&(l.totp.qr_code=`data:image/svg+xml;utf-8,${l.totp.qr_code}`),{data:l,error:null})})}catch(n){if(A(n))return{data:null,error:n};throw n}}async _verify(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:i,error:o}=n;if(o)return{data:null,error:o};const{data:a,error:s}=await L(this.fetch,"POST",`${this.url}/factors/${t.factorId}/verify`,{body:{code:t.code,challenge_id:t.challengeId},headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token});return s?{data:null,error:s}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+a.expires_in},a)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",a),{data:a,error:s})})}catch(n){if(A(n))return{data:null,error:n};throw n}})}async _challenge(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:i,error:o}=n;return o?{data:null,error:o}:await L(this.fetch,"POST",`${this.url}/factors/${t.factorId}/challenge`,{body:{channel:t.channel},headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(A(n))return{data:null,error:n};throw n}})}async _challengeAndVerify(t){const{data:n,error:r}=await this._challenge({factorId:t.factorId});return r?{data:null,error:r}:await this._verify({factorId:t.factorId,challengeId:n.id,code:t.code})}async _listFactors(){const{data:{user:t},error:n}=await this.getUser();if(n)return{data:null,error:n};const r=(t==null?void 0:t.factors)||[],i=r.filter(a=>a.factor_type==="totp"&&a.status==="verified"),o=r.filter(a=>a.factor_type==="phone"&&a.status==="verified");return{data:{all:r,totp:i,phone:o},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async t=>{var n,r;const{data:{session:i},error:o}=t;if(o)return{data:null,error:o};if(!i)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:a}=ts(i.access_token);let s=null;a.aal&&(s=a.aal);let l=s;((r=(n=i.user.factors)===null||n===void 0?void 0:n.filter(d=>d.status==="verified"))!==null&&r!==void 0?r:[]).length>0&&(l="aal2");const c=a.amr||[];return{data:{currentLevel:s,nextLevel:l,currentAuthenticationMethods:c},error:null}}))}async fetchJwk(t,n={keys:[]}){let r=n.keys.find(a=>a.kid===t);if(r||(r=this.jwks.keys.find(a=>a.kid===t),r&&this.jwks_cached_at+ow>Date.now()))return r;const{data:i,error:o}=await L(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(o)throw o;if(!i.keys||i.keys.length===0)throw new Wr("JWKS is empty");if(this.jwks=i,this.jwks_cached_at=Date.now(),r=i.keys.find(a=>a.kid===t),!r)throw new Wr("No matching signing key found in JWKS");return r}async getClaims(t,n={keys:[]}){try{let r=t;if(!r){const{data:g,error:y}=await this.getSession();if(y||!g.session)return{data:null,error:y};r=g.session.access_token}const{header:i,payload:o,signature:a,raw:{header:s,payload:l}}=ts(r);if(Tw(o.exp),!i.kid||i.alg==="HS256"||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){const{error:g}=await this.getUser(r);if(g)throw g;return{data:{claims:o,header:i,signature:a},error:null}}const u=Ow(i.alg),c=await this.fetchJwk(i.kid,n),d=await crypto.subtle.importKey("jwk",c,u,!0,["verify"]);if(!await crypto.subtle.verify(u,d,a,mw(`${s}.${l}`)))throw new Wr("Invalid JWT signature");return{data:{claims:o,header:i,signature:a},error:null}}catch(r){if(A(r))return{data:null,error:r};throw r}}}ci.nextInstanceID=0;const Kw=ci;class Gw extends Kw{constructor(t){super(t)}}var Jw=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(c){try{u(r.next(c))}catch(d){a(d)}}function l(c){try{u(r.throw(c))}catch(d){a(d)}}function u(c){c.done?o(c.value):i(c.value).then(s,l)}u((r=r.apply(e,t||[])).next())})};class Qw{constructor(t,n,r){var i,o,a;if(this.supabaseUrl=t,this.supabaseKey=n,!t)throw new Error("supabaseUrl is required.");if(!n)throw new Error("supabaseKey is required.");const s=Z0(t),l=new URL(s);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const u=`sb-${l.hostname.split(".")[0]}-auth-token`,c={db:V0,realtime:K0,auth:Object.assign(Object.assign({},H0),{storageKey:u}),global:q0},d=ew(r??{},c);this.storageKey=(i=d.auth.storageKey)!==null&&i!==void 0?i:"",this.headers=(o=d.global.headers)!==null&&o!==void 0?o:{},d.accessToken?(this.accessToken=d.accessToken,this.auth=new Proxy({},{get:(p,g)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(g)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((a=d.auth)!==null&&a!==void 0?a:{},this.headers,d.global.fetch),this.fetch=Y0(n,this._getAccessToken.bind(this),d.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},d.realtime)),this.rest=new f0(new URL("rest/v1",l).href,{headers:this.headers,schema:d.db.schema,fetch:this.fetch}),d.accessToken||this._listenForAuthEvents()}get functions(){return new Wy(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new M0(this.storageUrl.href,this.headers,this.fetch)}from(t){return this.rest.from(t)}schema(t){return this.rest.schema(t)}rpc(t,n={},r={}){return this.rest.rpc(t,n,r)}channel(t,n={config:{}}){return this.realtime.channel(t,n)}getChannels(){return this.realtime.getChannels()}removeChannel(t){return this.realtime.removeChannel(t)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var t,n;return Jw(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:r}=yield this.auth.getSession();return(n=(t=r.session)===null||t===void 0?void 0:t.access_token)!==null&&n!==void 0?n:null})}_initSupabaseAuthClient({autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:i,storageKey:o,flowType:a,lock:s,debug:l},u,c){const d={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new Gw({url:this.authUrl.href,headers:Object.assign(Object.assign({},d),u),storageKey:o,autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:i,flowType:a,lock:s,debug:l,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(t){return new P0(this.realtimeUrl.href,Object.assign(Object.assign({},t),{params:Object.assign({apikey:this.supabaseKey},t==null?void 0:t.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((n,r)=>{this._handleTokenChanged(n,"CLIENT",r==null?void 0:r.access_token)})}_handleTokenChanged(t,n,r){(t==="TOKEN_REFRESHED"||t==="SIGNED_IN")&&this.changedAccessToken!==r?this.changedAccessToken=r:t==="SIGNED_OUT"&&(this.realtime.setAuth(),n=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const Yw=(e,t,n)=>new Qw(e,t,n),Xw="https://jpvbtrzvbpyzgtpvltss.supabase.co",Zw="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpwdmJ0cnp2YnB5emd0cHZsdHNzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5MTM1MzAsImV4cCI6MjA2NjQ4OTUzMH0.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI";Yw(Xw,Zw);class ex{async signUp(t,n,r){try{return await(await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,password:n,name:r})})).json()}catch{return{success:!1,error:"Network error during signup"}}}async signIn(t,n){try{const i=await(await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,password:n})})).json();return i.success&&i.token&&localStorage.setItem("auth_token",i.token),i}catch{return{success:!1,error:"Network error during login"}}}async signOut(){try{const t=localStorage.getItem("auth_token");t&&await fetch("/api/auth/logout",{method:"POST",headers:{Authorization:`Bearer ${t}`}})}finally{localStorage.removeItem("auth_token")}}async getCurrentUser(){try{const t=localStorage.getItem("auth_token");if(!t)return null;const n=await fetch("/api/auth/user",{headers:{Authorization:`Bearer ${t}`}});if(!n.ok)return n.status===401&&localStorage.removeItem("auth_token"),null;const r=await n.json();return r.success?r.data:null}catch{return null}}getToken(){return localStorage.getItem("auth_token")}isAuthenticated(){return!!this.getToken()}}const Gi=new ex,fa=fu((e,t)=>({user:null,isLoading:!1,isAuthenticated:!1,login:async(n,r)=>{e({isLoading:!0});try{const i=await Gi.signIn(n,r);return i.success&&i.user?(e({user:i.user,isAuthenticated:!0,isLoading:!1}),{success:!0}):(e({isLoading:!1}),{success:!1,error:i.error||"Login failed"})}catch{return e({isLoading:!1}),{success:!1,error:"Network error"}}},signup:async(n,r,i)=>{e({isLoading:!0});try{const o=await Gi.signUp(n,r,i);return o.success&&o.user?(e({user:o.user,isAuthenticated:!0,isLoading:!1}),{success:!0}):(e({isLoading:!1}),{success:!1,error:o.error||"Signup failed"})}catch{return e({isLoading:!1}),{success:!1,error:"Network error"}}},logout:async()=>{e({isLoading:!0});try{await Gi.signOut()}finally{e({user:null,isAuthenticated:!1,isLoading:!1})}},checkAuth:async()=>{e({isLoading:!0});try{const n=await Gi.getCurrentUser();e({user:n,isAuthenticated:!!n,isLoading:!1})}catch{e({user:null,isAuthenticated:!1,isLoading:!1})}},updateUser:n=>{const{user:r}=t();r&&e({user:{...r,...n}})}})),lt=({children:e,onClick:t,variant:n="primary",size:r="md",isLoading:i=!1,disabled:o=!1,type:a="button",className:s="",...l})=>{const u="font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",c={primary:"bg-primary-500 hover:bg-primary-600 text-white focus:ring-primary-500",secondary:"border-2 border-primary-500 text-primary-500 hover:bg-primary-50 focus:ring-primary-500",danger:"bg-red-500 hover:bg-red-600 text-white focus:ring-red-500"},d={sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"},p=`${u} ${c[n]} ${d[r]} ${s}`;return v.jsx("button",{type:a,onClick:t,disabled:o||i,className:p,...l,children:i?v.jsxs("div",{className:"flex items-center",children:[v.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[v.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),v.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Loading..."]}):e})},Lt=({label:e,placeholder:t,value:n,onChange:r,type:i="text",error:o,required:a=!1,disabled:s=!1,className:l="",...u})=>{const c=`w-full px-3 py-2 border-2 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed ${o?"border-red-500":"border-gray-600 focus:border-primary-500"} ${l}`;return v.jsxs("div",{className:"w-full",children:[e&&v.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:[e,a&&v.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),v.jsx("input",{type:i,value:n,onChange:d=>r(d.target.value),placeholder:t,disabled:s,className:c,...u}),o&&v.jsx("p",{className:"mt-1 text-sm text-red-500",children:o})]})},tx=()=>{const[e,t]=k.useState(""),[n,r]=k.useState(""),[i,o]=k.useState({}),{login:a,isLoading:s}=fa(),l=gi(),u=()=>{const d={};return e?/\S+@\S+\.\S+/.test(e)||(d.email="Email is invalid"):d.email="Email is required",n||(d.password="Password is required"),o(d),Object.keys(d).length===0},c=async d=>{if(d.preventDefault(),!u())return;const p=await a(e,n);p.success?l("/dashboard"):o({general:p.error})};return v.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:v.jsxs("div",{className:"max-w-md w-full space-y-8 p-8",children:[v.jsxs("div",{children:[v.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Sign in to ChewyAI"}),v.jsxs("p",{className:"mt-2 text-center text-sm text-gray-400",children:["Or"," ",v.jsx(Bf,{to:"/signup",className:"font-medium text-primary-500 hover:text-primary-400",children:"create a new account"})]})]}),v.jsxs("form",{className:"mt-8 space-y-6",onSubmit:c,children:[i.general&&v.jsx("div",{className:"bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded",children:i.general}),v.jsxs("div",{className:"space-y-4",children:[v.jsx(Lt,{label:"Email address",type:"email",value:e,onChange:t,error:i.email,placeholder:"Enter your email",required:!0}),v.jsx(Lt,{label:"Password",type:"password",value:n,onChange:r,error:i.password,placeholder:"Enter your password",required:!0})]}),v.jsx(lt,{type:"submit",isLoading:s,className:"w-full",size:"lg",children:"Sign in"})]})]})})},nx=()=>{const[e,t]=k.useState({name:"",email:"",password:"",confirmPassword:""}),[n,r]=k.useState({}),{signup:i,isLoading:o}=fa(),a=gi(),s=()=>{const c={};return e.email?/\S+@\S+\.\S+/.test(e.email)||(c.email="Email is invalid"):c.email="Email is required",e.password?e.password.length<6&&(c.password="Password must be at least 6 characters"):c.password="Password is required",e.password!==e.confirmPassword&&(c.confirmPassword="Passwords do not match"),r(c),Object.keys(c).length===0},l=async c=>{if(c.preventDefault(),!s())return;const d=await i(e.email,e.password,e.name||void 0);d.success?a("/dashboard"):r({general:d.error||"Signup failed"})},u=(c,d)=>t(p=>({...p,[c]:d}));return v.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:v.jsxs("div",{className:"max-w-md w-full space-y-8 p-8",children:[v.jsxs("div",{children:[v.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Create your account"}),v.jsxs("p",{className:"mt-2 text-center text-sm text-gray-400",children:["Or"," ",v.jsx(Bf,{to:"/login",className:"font-medium text-primary-500 hover:text-primary-400",children:"sign in to your existing account"})]})]}),v.jsxs("form",{className:"mt-8 space-y-6",onSubmit:l,children:[n.general&&v.jsx("div",{className:"bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded",children:n.general}),v.jsxs("div",{className:"space-y-4",children:[v.jsx(Lt,{label:"Full Name (Optional)",value:e.name,onChange:c=>u("name",c),placeholder:"Enter your full name"}),v.jsx(Lt,{label:"Email address",type:"email",value:e.email,onChange:c=>u("email",c),error:n.email,placeholder:"Enter your email",required:!0}),v.jsx(Lt,{label:"Password",type:"password",value:e.password,onChange:c=>u("password",c),error:n.password,placeholder:"Create a password",required:!0}),v.jsx(Lt,{label:"Confirm Password",type:"password",value:e.confirmPassword,onChange:c=>u("confirmPassword",c),error:n.confirmPassword,placeholder:"Confirm your password",required:!0})]}),v.jsx(lt,{type:"submit",isLoading:o,className:"w-full",size:"lg",children:"Create Account"})]})]})})},Ji=({children:e})=>{const{isAuthenticated:t,isLoading:n,checkAuth:r}=fa(),i=fr();return k.useEffect(()=>{r()},[r]),n?v.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:v.jsx("div",{className:"text-white",children:"Loading..."})}):t?v.jsx(v.Fragment,{children:e}):v.jsx(Mf,{to:"/login",state:{from:i},replace:!0})},rx=()=>v.jsx("div",{className:"min-h-screen bg-background-primary text-white flex items-center justify-center",children:v.jsx("h1",{className:"text-3xl font-bold",children:"Welcome to ChewyAI Dashboard"})});var gh={exports:{}},ix="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",ox=ix,ax=ox;function yh(){}function wh(){}wh.resetWarningCache=yh;var sx=function(){function e(r,i,o,a,s,l){if(l!==ax){var u=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}e.isRequired=e;function t(){return e}var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:wh,resetWarningCache:yh};return n.PropTypes=n,n};gh.exports=sx();var lx=gh.exports;const M=hl(lx);function kn(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(c){try{u(r.next(c))}catch(d){a(d)}}function l(c){try{u(r.throw(c))}catch(d){a(d)}}function u(c){c.done?o(c.value):i(c.value).then(s,l)}u((r=r.apply(e,t||[])).next())})}const ux=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function sr(e,t,n){const r=cx(e),{webkitRelativePath:i}=e,o=typeof t=="string"?t:typeof i=="string"&&i.length>0?i:`./${e.name}`;return typeof r.path!="string"&&ld(r,"path",o),n!==void 0&&Object.defineProperty(r,"handle",{value:n,writable:!1,configurable:!1,enumerable:!0}),ld(r,"relativePath",o),r}function cx(e){const{name:t}=e;if(t&&t.lastIndexOf(".")!==-1&&!e.type){const r=t.split(".").pop().toLowerCase(),i=ux.get(r);i&&Object.defineProperty(e,"type",{value:i,writable:!1,configurable:!1,enumerable:!0})}return e}function ld(e,t,n){Object.defineProperty(e,t,{value:n,writable:!1,configurable:!1,enumerable:!0})}const dx=[".DS_Store","Thumbs.db"];function px(e){return kn(this,void 0,void 0,function*(){return zo(e)&&fx(e.dataTransfer)?gx(e.dataTransfer,e.type):hx(e)?mx(e):Array.isArray(e)&&e.every(t=>"getFile"in t&&typeof t.getFile=="function")?vx(e):[]})}function fx(e){return zo(e)}function hx(e){return zo(e)&&zo(e.target)}function zo(e){return typeof e=="object"&&e!==null}function mx(e){return ul(e.target.files).map(t=>sr(t))}function vx(e){return kn(this,void 0,void 0,function*(){return(yield Promise.all(e.map(n=>n.getFile()))).map(n=>sr(n))})}function gx(e,t){return kn(this,void 0,void 0,function*(){if(e.items){const n=ul(e.items).filter(i=>i.kind==="file");if(t!=="drop")return n;const r=yield Promise.all(n.map(yx));return ud(xh(r))}return ud(ul(e.files).map(n=>sr(n)))})}function ud(e){return e.filter(t=>dx.indexOf(t.name)===-1)}function ul(e){if(e===null)return[];const t=[];for(let n=0;n<e.length;n++){const r=e[n];t.push(r)}return t}function yx(e){if(typeof e.webkitGetAsEntry!="function")return cd(e);const t=e.webkitGetAsEntry();return t&&t.isDirectory?_h(t):cd(e,t)}function xh(e){return e.reduce((t,n)=>[...t,...Array.isArray(n)?xh(n):[n]],[])}function cd(e,t){return kn(this,void 0,void 0,function*(){var n;if(globalThis.isSecureContext&&typeof e.getAsFileSystemHandle=="function"){const o=yield e.getAsFileSystemHandle();if(o===null)throw new Error(`${e} is not a File`);if(o!==void 0){const a=yield o.getFile();return a.handle=o,sr(a)}}const r=e.getAsFile();if(!r)throw new Error(`${e} is not a File`);return sr(r,(n=t==null?void 0:t.fullPath)!==null&&n!==void 0?n:void 0)})}function wx(e){return kn(this,void 0,void 0,function*(){return e.isDirectory?_h(e):xx(e)})}function _h(e){const t=e.createReader();return new Promise((n,r)=>{const i=[];function o(){t.readEntries(a=>kn(this,void 0,void 0,function*(){if(a.length){const s=Promise.all(a.map(wx));i.push(s),o()}else try{const s=yield Promise.all(i);n(s)}catch(s){r(s)}}),a=>{r(a)})}o()})}function xx(e){return kn(this,void 0,void 0,function*(){return new Promise((t,n)=>{e.file(r=>{const i=sr(r,e.fullPath);t(i)},r=>{n(r)})})})}var rs=function(e,t){if(e&&t){var n=Array.isArray(t)?t:t.split(",");if(n.length===0)return!0;var r=e.name||"",i=(e.type||"").toLowerCase(),o=i.replace(/\/.*$/,"");return n.some(function(a){var s=a.trim().toLowerCase();return s.charAt(0)==="."?r.toLowerCase().endsWith(s):s.endsWith("/*")?o===s.replace(/\/.*$/,""):i===s})}return!0};function dd(e){return Sx(e)||kx(e)||Sh(e)||_x()}function _x(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function kx(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Sx(e){if(Array.isArray(e))return cl(e)}function pd(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function fd(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?pd(Object(n),!0).forEach(function(r){kh(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):pd(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function kh(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function di(e,t){return jx(e)||bx(e,t)||Sh(e,t)||Ex()}function Ex(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Sh(e,t){if(e){if(typeof e=="string")return cl(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return cl(e,t)}}function cl(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function bx(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var r=[],i=!0,o=!1,a,s;try{for(n=n.call(e);!(i=(a=n.next()).done)&&(r.push(a.value),!(t&&r.length===t));i=!0);}catch(l){o=!0,s=l}finally{try{!i&&n.return!=null&&n.return()}finally{if(o)throw s}}return r}}function jx(e){if(Array.isArray(e))return e}var Cx=typeof rs=="function"?rs:rs.default,Px="file-invalid-type",Tx="file-too-large",Ox="file-too-small",Rx="too-many-files",Ix=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",n=t.split(","),r=n.length>1?"one of ".concat(n.join(", ")):n[0];return{code:Px,message:"File type must be ".concat(r)}},hd=function(t){return{code:Tx,message:"File is larger than ".concat(t," ").concat(t===1?"byte":"bytes")}},md=function(t){return{code:Ox,message:"File is smaller than ".concat(t," ").concat(t===1?"byte":"bytes")}},Ax={code:Rx,message:"Too many files"};function Eh(e,t){var n=e.type==="application/x-moz-file"||Cx(e,t);return[n,n?null:Ix(t)]}function bh(e,t,n){if(ln(e.size))if(ln(t)&&ln(n)){if(e.size>n)return[!1,hd(n)];if(e.size<t)return[!1,md(t)]}else{if(ln(t)&&e.size<t)return[!1,md(t)];if(ln(n)&&e.size>n)return[!1,hd(n)]}return[!0,null]}function ln(e){return e!=null}function Nx(e){var t=e.files,n=e.accept,r=e.minSize,i=e.maxSize,o=e.multiple,a=e.maxFiles,s=e.validator;return!o&&t.length>1||o&&a>=1&&t.length>a?!1:t.every(function(l){var u=Eh(l,n),c=di(u,1),d=c[0],p=bh(l,r,i),g=di(p,1),y=g[0],w=s?s(l):null;return d&&y&&!w})}function Uo(e){return typeof e.isPropagationStopped=="function"?e.isPropagationStopped():typeof e.cancelBubble<"u"?e.cancelBubble:!1}function Qi(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(t){return t==="Files"||t==="application/x-moz-file"}):!!e.target&&!!e.target.files}function vd(e){e.preventDefault()}function Dx(e){return e.indexOf("MSIE")!==-1||e.indexOf("Trident/")!==-1}function Lx(e){return e.indexOf("Edge/")!==-1}function $x(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.navigator.userAgent;return Dx(e)||Lx(e)}function ot(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(r){for(var i=arguments.length,o=new Array(i>1?i-1:0),a=1;a<i;a++)o[a-1]=arguments[a];return t.some(function(s){return!Uo(r)&&s&&s.apply(void 0,[r].concat(o)),Uo(r)})}}function zx(){return"showOpenFilePicker"in window}function Ux(e){if(ln(e)){var t=Object.entries(e).filter(function(n){var r=di(n,2),i=r[0],o=r[1],a=!0;return jh(i)||(console.warn('Skipped "'.concat(i,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),a=!1),(!Array.isArray(o)||!o.every(Ch))&&(console.warn('Skipped "'.concat(i,'" because an invalid file extension was provided.')),a=!1),a}).reduce(function(n,r){var i=di(r,2),o=i[0],a=i[1];return fd(fd({},n),{},kh({},o,a))},{});return[{description:"Files",accept:t}]}return e}function Fx(e){if(ln(e))return Object.entries(e).reduce(function(t,n){var r=di(n,2),i=r[0],o=r[1];return[].concat(dd(t),[i],dd(o))},[]).filter(function(t){return jh(t)||Ch(t)}).join(",")}function Mx(e){return e instanceof DOMException&&(e.name==="AbortError"||e.code===e.ABORT_ERR)}function Bx(e){return e instanceof DOMException&&(e.name==="SecurityError"||e.code===e.SECURITY_ERR)}function jh(e){return e==="audio/*"||e==="video/*"||e==="image/*"||e==="text/*"||e==="application/*"||/\w+\/[-+.\w]+/g.test(e)}function Ch(e){return/^.*\.[\w]+$/.test(e)}var Wx=["children"],qx=["open"],Vx=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],Hx=["refKey","onChange","onClick"];function Kx(e){return Qx(e)||Jx(e)||Ph(e)||Gx()}function Gx(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Jx(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Qx(e){if(Array.isArray(e))return dl(e)}function is(e,t){return Zx(e)||Xx(e,t)||Ph(e,t)||Yx()}function Yx(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ph(e,t){if(e){if(typeof e=="string")return dl(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return dl(e,t)}}function dl(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Xx(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var r=[],i=!0,o=!1,a,s;try{for(n=n.call(e);!(i=(a=n.next()).done)&&(r.push(a.value),!(t&&r.length===t));i=!0);}catch(l){o=!0,s=l}finally{try{!i&&n.return!=null&&n.return()}finally{if(o)throw s}}return r}}function Zx(e){if(Array.isArray(e))return e}function gd(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function J(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?gd(Object(n),!0).forEach(function(r){pl(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):gd(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function pl(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Fo(e,t){if(e==null)return{};var n=e_(e,t),r,i;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)r=o[i],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function e_(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,o;for(o=0;o<r.length;o++)i=r[o],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}var wu=k.forwardRef(function(e,t){var n=e.children,r=Fo(e,Wx),i=Oh(r),o=i.open,a=Fo(i,qx);return k.useImperativeHandle(t,function(){return{open:o}},[o]),Bo.createElement(k.Fragment,null,n(J(J({},a),{},{open:o})))});wu.displayName="Dropzone";var Th={disabled:!1,getFilesFromEvent:px,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};wu.defaultProps=Th;wu.propTypes={children:M.func,accept:M.objectOf(M.arrayOf(M.string)),multiple:M.bool,preventDropOnDocument:M.bool,noClick:M.bool,noKeyboard:M.bool,noDrag:M.bool,noDragEventsBubbling:M.bool,minSize:M.number,maxSize:M.number,maxFiles:M.number,disabled:M.bool,getFilesFromEvent:M.func,onFileDialogCancel:M.func,onFileDialogOpen:M.func,useFsAccessApi:M.bool,autoFocus:M.bool,onDragEnter:M.func,onDragLeave:M.func,onDragOver:M.func,onDrop:M.func,onDropAccepted:M.func,onDropRejected:M.func,onError:M.func,validator:M.func};var fl={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function Oh(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=J(J({},Th),e),n=t.accept,r=t.disabled,i=t.getFilesFromEvent,o=t.maxSize,a=t.minSize,s=t.multiple,l=t.maxFiles,u=t.onDragEnter,c=t.onDragLeave,d=t.onDragOver,p=t.onDrop,g=t.onDropAccepted,y=t.onDropRejected,w=t.onFileDialogCancel,_=t.onFileDialogOpen,m=t.useFsAccessApi,h=t.autoFocus,f=t.preventDropOnDocument,x=t.noClick,S=t.noKeyboard,E=t.noDrag,j=t.noDragEventsBubbling,O=t.onError,F=t.validator,N=k.useMemo(function(){return Fx(n)},[n]),Ee=k.useMemo(function(){return Ux(n)},[n]),dt=k.useMemo(function(){return typeof _=="function"?_:yd},[_]),rt=k.useMemo(function(){return typeof w=="function"?w:yd},[w]),ae=k.useRef(null),be=k.useRef(null),vr=k.useReducer(t_,fl),Sn=is(vr,2),P=Sn[0],R=Sn[1],D=P.isFocused,H=P.isFileDialogActive,G=k.useRef(typeof window<"u"&&window.isSecureContext&&m&&zx()),en=function(){!G.current&&H&&setTimeout(function(){if(be.current){var I=be.current.files;I.length||(R({type:"closeDialog"}),rt())}},300)};k.useEffect(function(){return window.addEventListener("focus",en,!1),function(){window.removeEventListener("focus",en,!1)}},[be,H,rt,G]);var je=k.useRef([]),En=function(I){ae.current&&ae.current.contains(I.target)||(I.preventDefault(),je.current=[])};k.useEffect(function(){return f&&(document.addEventListener("dragover",vd,!1),document.addEventListener("drop",En,!1)),function(){f&&(document.removeEventListener("dragover",vd),document.removeEventListener("drop",En))}},[ae,f]),k.useEffect(function(){return!r&&h&&ae.current&&ae.current.focus(),function(){}},[ae,h,r]);var we=k.useCallback(function(C){O?O(C):console.error(C)},[O]),tn=k.useCallback(function(C){C.preventDefault(),C.persist(),Ei(C),je.current=[].concat(Kx(je.current),[C.target]),Qi(C)&&Promise.resolve(i(C)).then(function(I){if(!(Uo(C)&&!j)){var ne=I.length,se=ne>0&&Nx({files:I,accept:N,minSize:a,maxSize:o,multiple:s,maxFiles:l,validator:F}),Ne=ne>0&&!se;R({isDragAccept:se,isDragReject:Ne,isDragActive:!0,type:"setDraggedFiles"}),u&&u(C)}}).catch(function(I){return we(I)})},[i,u,we,j,N,a,o,s,l,F]),xu=k.useCallback(function(C){C.preventDefault(),C.persist(),Ei(C);var I=Qi(C);if(I&&C.dataTransfer)try{C.dataTransfer.dropEffect="copy"}catch{}return I&&d&&d(C),!1},[d,j]),_u=k.useCallback(function(C){C.preventDefault(),C.persist(),Ei(C);var I=je.current.filter(function(se){return ae.current&&ae.current.contains(se)}),ne=I.indexOf(C.target);ne!==-1&&I.splice(ne,1),je.current=I,!(I.length>0)&&(R({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),Qi(C)&&c&&c(C))},[ae,c,j]),_i=k.useCallback(function(C,I){var ne=[],se=[];C.forEach(function(Ne){var gr=Eh(Ne,N),Cn=is(gr,2),va=Cn[0],ga=Cn[1],ya=bh(Ne,a,o),bi=is(ya,2),wa=bi[0],xa=bi[1],_a=F?F(Ne):null;if(va&&wa&&!_a)ne.push(Ne);else{var ka=[ga,xa];_a&&(ka=ka.concat(_a)),se.push({file:Ne,errors:ka.filter(function(Dh){return Dh})})}}),(!s&&ne.length>1||s&&l>=1&&ne.length>l)&&(ne.forEach(function(Ne){se.push({file:Ne,errors:[Ax]})}),ne.splice(0)),R({acceptedFiles:ne,fileRejections:se,isDragReject:se.length>0,type:"setFiles"}),p&&p(ne,se,I),se.length>0&&y&&y(se,I),ne.length>0&&g&&g(ne,I)},[R,s,N,a,o,l,p,g,y,F]),ki=k.useCallback(function(C){C.preventDefault(),C.persist(),Ei(C),je.current=[],Qi(C)&&Promise.resolve(i(C)).then(function(I){Uo(C)&&!j||_i(I,C)}).catch(function(I){return we(I)}),R({type:"reset"})},[i,_i,we,j]),bn=k.useCallback(function(){if(G.current){R({type:"openDialog"}),dt();var C={multiple:s,types:Ee};window.showOpenFilePicker(C).then(function(I){return i(I)}).then(function(I){_i(I,null),R({type:"closeDialog"})}).catch(function(I){Mx(I)?(rt(I),R({type:"closeDialog"})):Bx(I)?(G.current=!1,be.current?(be.current.value=null,be.current.click()):we(new Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):we(I)});return}be.current&&(R({type:"openDialog"}),dt(),be.current.value=null,be.current.click())},[R,dt,rt,m,_i,we,Ee,s]),ku=k.useCallback(function(C){!ae.current||!ae.current.isEqualNode(C.target)||(C.key===" "||C.key==="Enter"||C.keyCode===32||C.keyCode===13)&&(C.preventDefault(),bn())},[ae,bn]),Su=k.useCallback(function(){R({type:"focus"})},[]),Eu=k.useCallback(function(){R({type:"blur"})},[]),bu=k.useCallback(function(){x||($x()?setTimeout(bn,0):bn())},[x,bn]),jn=function(I){return r?null:I},ma=function(I){return S?null:jn(I)},Si=function(I){return E?null:jn(I)},Ei=function(I){j&&I.stopPropagation()},Ih=k.useMemo(function(){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},I=C.refKey,ne=I===void 0?"ref":I,se=C.role,Ne=C.onKeyDown,gr=C.onFocus,Cn=C.onBlur,va=C.onClick,ga=C.onDragEnter,ya=C.onDragOver,bi=C.onDragLeave,wa=C.onDrop,xa=Fo(C,Vx);return J(J(pl({onKeyDown:ma(ot(Ne,ku)),onFocus:ma(ot(gr,Su)),onBlur:ma(ot(Cn,Eu)),onClick:jn(ot(va,bu)),onDragEnter:Si(ot(ga,tn)),onDragOver:Si(ot(ya,xu)),onDragLeave:Si(ot(bi,_u)),onDrop:Si(ot(wa,ki)),role:typeof se=="string"&&se!==""?se:"presentation"},ne,ae),!r&&!S?{tabIndex:0}:{}),xa)}},[ae,ku,Su,Eu,bu,tn,xu,_u,ki,S,E,r]),Ah=k.useCallback(function(C){C.stopPropagation()},[]),Nh=k.useMemo(function(){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},I=C.refKey,ne=I===void 0?"ref":I,se=C.onChange,Ne=C.onClick,gr=Fo(C,Hx),Cn=pl({accept:N,multiple:s,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:jn(ot(se,ki)),onClick:jn(ot(Ne,Ah)),tabIndex:-1},ne,be);return J(J({},Cn),gr)}},[be,n,s,ki,r]);return J(J({},P),{},{isFocused:D&&!r,getRootProps:Ih,getInputProps:Nh,rootRef:ae,inputRef:be,open:jn(bn)})}function t_(e,t){switch(t.type){case"focus":return J(J({},e),{},{isFocused:!0});case"blur":return J(J({},e),{},{isFocused:!1});case"openDialog":return J(J({},fl),{},{isFileDialogActive:!0});case"closeDialog":return J(J({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return J(J({},e),{},{isDragActive:t.isDragActive,isDragAccept:t.isDragAccept,isDragReject:t.isDragReject});case"setFiles":return J(J({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections,isDragReject:t.isDragReject});case"reset":return J({},fl);default:return e}}function yd(){}const ha=fu(e=>({documents:[],selectedDocuments:new Set,isLoading:!1,uploadProgress:{},fetchDocuments:async()=>{e({isLoading:!0});try{const t=localStorage.getItem("auth_token"),n=await fetch("/api/documents",{headers:{Authorization:`Bearer ${t}`}});if(!n.ok)throw new Error("Failed to fetch documents");const r=await n.json();if(r.success)e({documents:r.data,isLoading:!1});else throw new Error(r.error)}catch(t){throw console.error("Fetch documents error:",t),e({isLoading:!1}),t}},uploadDocument:async t=>{const n=new FormData;n.append("document",t);try{const r=localStorage.getItem("auth_token"),i=await fetch("/api/documents/upload",{method:"POST",headers:{Authorization:`Bearer ${r}`},body:n});if(!i.ok){const a=await i.json();throw new Error(a.error||"Upload failed")}const o=await i.json();if(o.success)return e(a=>({documents:[o.data,...a.documents],uploadProgress:{...a.uploadProgress,[t.name]:100}})),o.data;throw new Error(o.error)}catch(r){throw console.error("Upload document error:",r),r}},deleteDocument:async t=>{try{const n=localStorage.getItem("auth_token"),r=await fetch(`/api/documents/${t}`,{method:"DELETE",headers:{Authorization:`Bearer ${n}`}});if(!r.ok){const i=await r.json();throw new Error(i.error||"Delete failed")}e(i=>({documents:i.documents.filter(o=>o.id!==t),selectedDocuments:new Set([...i.selectedDocuments].filter(o=>o!==t))}))}catch(n){throw console.error("Delete document error:",n),n}},searchDocuments:async t=>{try{const n=localStorage.getItem("auth_token"),r=await fetch(`/api/documents/search?q=${encodeURIComponent(t)}`,{headers:{Authorization:`Bearer ${n}`}});if(!r.ok)throw new Error("Search failed");const i=await r.json();return i.success?i.data:[]}catch(n){return console.error("Search documents error:",n),[]}},getDocument:async t=>{try{const n=localStorage.getItem("auth_token"),r=await fetch(`/api/documents/${t}`,{headers:{Authorization:`Bearer ${n}`}});if(!r.ok)return null;const i=await r.json();return i.success?i.data:null}catch(n){return console.error("Get document error:",n),null}},toggleDocumentSelection:t=>{e(n=>{const r=new Set(n.selectedDocuments);return r.has(t)?r.delete(t):r.add(t),{selectedDocuments:r}})},clearSelection:()=>{e({selectedDocuments:new Set})},selectAll:()=>{e(t=>({selectedDocuments:new Set(t.documents.map(n=>n.id))}))},setUploadProgress:(t,n)=>{e(r=>({uploadProgress:{...r.uploadProgress,[t]:n}}))}})),n_=()=>{const[e,t]=k.useState(!1),[n,r]=k.useState([]),{uploadDocument:i,setUploadProgress:o}=ha(),a=k.useCallback(async c=>{t(!0),r([]);const d=[];for(const p of c)try{if(p.size>10*1024*1024){d.push(`${p.name}: File size exceeds 10MB limit`);continue}if(!["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","text/plain","application/vnd.openxmlformats-officedocument.presentationml.presentation"].includes(p.type)){d.push(`${p.name}: Unsupported file type. Please upload PDF, DOCX, TXT, or PPTX files.`);continue}o(p.name,0),await i(p),o(p.name,100)}catch(g){d.push(`${p.name}: ${g instanceof Error?g.message:"Unknown error"}`)}r(d),t(!1)},[i,o]),{getRootProps:s,getInputProps:l,isDragActive:u}=Oh({onDrop:a,accept:{"application/pdf":[".pdf"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":[".docx"],"text/plain":[".txt"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":[".pptx"]},multiple:!0,disabled:e});return v.jsxs("div",{className:"space-y-4",children:[v.jsxs("div",{...s(),className:`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
          ${u?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-primary-500 hover:bg-primary-500/5"}
          ${e?"opacity-50 cursor-not-allowed":""}
        `,children:[v.jsx("input",{...l()}),v.jsxs("div",{className:"space-y-2",children:[v.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:v.jsx("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})}),u?v.jsx("p",{className:"text-primary-400",children:"Drop the files here..."}):v.jsxs("div",{children:[v.jsxs("p",{className:"text-gray-300",children:["Drag & drop files here, or"," ",v.jsx("span",{className:"text-primary-500 font-medium",children:"browse"})]}),v.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Supports PDF, DOCX, TXT, PPTX (max 10MB each)"})]})]})]}),e&&v.jsxs("div",{className:"bg-background-secondary rounded-lg p-4",children:[v.jsx("p",{className:"text-sm text-gray-300 mb-2",children:"Uploading files..."}),v.jsx("div",{className:"space-y-2"})]}),n.length>0&&v.jsxs("div",{className:"bg-red-900/20 border border-red-700 rounded-lg p-4",children:[v.jsx("h4",{className:"text-red-400 font-medium mb-2",children:"Upload Errors:"}),v.jsx("ul",{className:"text-sm text-red-300 space-y-1",children:n.map((c,d)=>v.jsxs("li",{children:["• ",c]},d))})]})]})},r_=({document:e})=>{const{selectedDocuments:t,toggleDocumentSelection:n,deleteDocument:r}=ha(),[i,o]=k.useState(!1),a=t.has(e.id),s=async()=>{if(window.confirm(`Are you sure you want to delete "${e.filename}"? This action cannot be undone.`)){o(!0);try{await r(e.id)}catch(p){console.error("Delete error:",p),alert("Failed to delete document. Please try again.")}finally{o(!1)}}},l=d=>{if(d===0)return"0 Bytes";const p=1024,g=["Bytes","KB","MB","GB"],y=Math.floor(Math.log(d)/Math.log(p));return parseFloat((d/Math.pow(p,y)).toFixed(2))+" "+g[y]},u=d=>new Date(d).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),c=d=>({pdf:"📄",docx:"📝",txt:"📃",pptx:"📊"})[d]||"📄";return v.jsxs("div",{className:`
        bg-background-secondary rounded-lg p-4 border-2 transition-all cursor-pointer
        ${a?"border-primary-500 bg-primary-500/10":"border-gray-700 hover:border-gray-600"}
      `,onClick:()=>n(e.id),children:[v.jsxs("div",{className:"flex items-start justify-between mb-3",children:[v.jsxs("div",{className:"flex items-center space-x-2 flex-1 min-w-0",children:[v.jsx("span",{className:"text-2xl",children:c(e.file_type)}),v.jsxs("div",{className:"min-w-0 flex-1",children:[v.jsx("h3",{className:"text-white font-medium truncate",title:e.filename,children:e.filename}),v.jsxs("p",{className:"text-sm text-gray-400",children:[e.file_type.toUpperCase()," • ",l(e.file_size)]})]})]}),v.jsx("div",{className:`
            w-5 h-5 rounded border-2 flex items-center justify-center
            ${a?"bg-primary-500 border-primary-500":"border-gray-500"}
          `,children:a&&v.jsx("svg",{className:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:v.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]}),!e.is_processed&&v.jsx("div",{className:"mb-3",children:v.jsxs("div",{className:"flex items-center space-x-2 text-yellow-400",children:[v.jsx("div",{className:"w-2 h-2 bg-yellow-400 rounded-full animate-pulse"}),v.jsx("span",{className:"text-sm",children:"Processing..."})]})}),e.processing_error&&v.jsx("div",{className:"mb-3",children:v.jsxs("div",{className:"text-red-400 text-sm",children:["⚠️ Processing failed: ",e.processing_error]})}),v.jsxs("div",{className:"text-xs text-gray-500 mb-3",children:["Uploaded ",u(e.uploaded_at)]}),v.jsx("div",{className:"flex justify-end space-x-2",onClick:d=>d.stopPropagation(),children:v.jsx(lt,{onClick:s,variant:"danger",size:"sm",isLoading:i,children:"Delete"})})]})},i_=()=>{const{documents:e,selectedDocuments:t,isLoading:n,fetchDocuments:r,searchDocuments:i,clearSelection:o,selectAll:a,deleteDocument:s}=ha(),[l,u]=k.useState(""),[c,d]=k.useState(null),[p,g]=k.useState(!1);k.useEffect(()=>{r()},[r]);const y=async()=>{if(l.trim().length<2){d(null);return}g(!0);try{const f=await i(l.trim());d(f)}catch(f){console.error("Search error:",f)}finally{g(!1)}},w=()=>{u(""),d(null)},_=async()=>{if(!(t.size===0||!window.confirm(`Are you sure you want to delete ${t.size} document(s)? This action cannot be undone.`)))try{const x=Array.from(t).map(S=>s(S));await Promise.all(x),o()}catch(x){console.error("Bulk delete error:",x),alert("Some documents could not be deleted. Please try again.")}},m=c||e,h=t.size>0;return n&&e.length===0?v.jsx("div",{className:"flex items-center justify-center py-12",children:v.jsx("div",{className:"text-gray-400",children:"Loading documents..."})}):v.jsxs("div",{className:"space-y-6",children:[v.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[v.jsx("div",{className:"flex-1",children:v.jsxs("div",{className:"flex gap-2",children:[v.jsx(Lt,{placeholder:"Search documents...",value:l,onChange:u}),v.jsx(lt,{onClick:y,isLoading:p,disabled:l.trim().length<2,children:"Search"}),c&&v.jsx(lt,{onClick:w,variant:"secondary",children:"Clear"})]})}),h&&v.jsxs("div",{className:"flex gap-2",children:[v.jsx(lt,{onClick:a,variant:"secondary",size:"sm",children:"Select All"}),v.jsxs(lt,{onClick:o,variant:"secondary",size:"sm",children:["Clear (",t.size,")"]}),v.jsx(lt,{onClick:_,variant:"danger",size:"sm",children:"Delete Selected"})]})]}),c&&v.jsxs("div",{className:"text-sm text-gray-400",children:["Found ",c.length,' document(s) matching "',l,'"']}),m.length===0?v.jsxs("div",{className:"text-center py-12",children:[v.jsx("div",{className:"text-gray-400 mb-4",children:c?"No documents found matching your search.":"No documents uploaded yet."}),!c&&v.jsx("p",{className:"text-sm text-gray-500",children:"Upload your first document to get started with AI-powered study materials."})]}):v.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:m.map(f=>v.jsx(r_,{document:f},f.id))})]})},o_=()=>v.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:v.jsxs("div",{className:"space-y-8",children:[v.jsxs("div",{children:[v.jsx("h1",{className:"text-3xl font-bold text-white",children:"Documents"}),v.jsx("p",{className:"mt-2 text-gray-400",children:"Upload and manage your study documents. Supported formats: PDF, DOCX, TXT, PPTX"})]}),v.jsxs("div",{className:"bg-background-secondary rounded-lg p-6",children:[v.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Upload Documents"}),v.jsx(n_,{})]}),v.jsxs("div",{children:[v.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Your Documents"}),v.jsx(i_,{})]})]})}),a_=fu(e=>({isGenerating:!1,generationProgress:"",lastGenerated:null,generateFlashcards:async t=>{e({isGenerating:!0,generationProgress:"Preparing documents..."});try{const n=localStorage.getItem("auth_token");e({generationProgress:"Generating flashcards with AI..."});const r=await fetch("/api/ai/generate-flashcards",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${n}`},body:JSON.stringify(t)});if(!r.ok){const o=await r.json();throw new Error(o.error||"Generation failed")}const i=await r.json();if(i.success)return e({lastGenerated:{studySet:i.data.studySet,content:i.data.flashcards,type:"flashcards"},isGenerating:!1,generationProgress:""}),{studySet:i.data.studySet,flashcards:i.data.flashcards,creditsRemaining:i.data.creditsRemaining};throw new Error(i.error)}catch(n){throw e({isGenerating:!1,generationProgress:""}),n}},generateQuiz:async t=>{e({isGenerating:!0,generationProgress:"Preparing documents..."});try{const n=localStorage.getItem("auth_token");e({generationProgress:"Generating quiz questions with AI..."});const r=await fetch("/api/ai/generate-quiz",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${n}`},body:JSON.stringify(t)});if(!r.ok){const o=await r.json();throw new Error(o.error||"Generation failed")}const i=await r.json();if(i.success)return e({lastGenerated:{studySet:i.data.studySet,content:i.data.questions,type:"quiz"},isGenerating:!1,generationProgress:""}),{studySet:i.data.studySet,questions:i.data.questions,creditsRemaining:i.data.creditsRemaining};throw new Error(i.error)}catch(n){throw e({isGenerating:!1,generationProgress:""}),n}},clearLastGenerated:()=>{e({lastGenerated:null})}})),s_=({selectedDocuments:e,onSelectionChange:t,maxSelection:n=5})=>{const{documents:r,fetchDocuments:i,isLoading:o}=ha(),[a,s]=k.useState("");k.useEffect(()=>{r.length===0&&i()},[r.length,i]);const l=r.filter(d=>d.is_processed&&d.filename.toLowerCase().includes(a.toLowerCase())),u=d=>{e.includes(d)?t(e.filter(g=>g!==d)):e.length<n&&t([...e,d])},c=()=>r.filter(d=>e.includes(d.id));return o?v.jsx("div",{className:"flex items-center justify-center py-8",children:v.jsx("div",{className:"text-gray-400",children:"Loading documents..."})}):r.length===0?v.jsxs("div",{className:"text-center py-8",children:[v.jsx("div",{className:"text-gray-400 mb-4",children:"No documents found"}),v.jsx("p",{className:"text-sm text-gray-500",children:"Upload some documents first to generate study materials."})]}):v.jsxs("div",{className:"space-y-4",children:[v.jsx("div",{children:v.jsx("input",{type:"text",placeholder:"Search documents...",value:a,onChange:d=>s(d.target.value),className:"w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"})}),e.length>0&&v.jsxs("div",{className:"bg-primary-500/10 border border-primary-500/30 rounded-lg p-3",children:[v.jsxs("div",{className:"text-sm text-primary-400 mb-2",children:["Selected ",e.length," of ",n," documents:"]}),v.jsx("div",{className:"space-y-1",children:c().map(d=>v.jsxs("div",{className:"text-sm text-gray-300 flex items-center justify-between",children:[v.jsx("span",{className:"truncate",children:d.filename}),v.jsx("button",{onClick:()=>u(d.id),className:"text-red-400 hover:text-red-300 ml-2",children:"✕"})]},d.id))})]}),v.jsx("div",{className:"max-h-64 overflow-y-auto space-y-2",children:l.map(d=>{const p=e.includes(d.id),g=!p&&e.length<n;return v.jsx("div",{className:`
                p-3 rounded-lg border cursor-pointer transition-all
                ${p?"bg-primary-500/20 border-primary-500":g?"bg-background-secondary border-gray-600 hover:border-gray-500":"bg-gray-800 border-gray-700 opacity-50 cursor-not-allowed"}
              `,onClick:()=>g||p?u(d.id):null,children:v.jsxs("div",{className:"flex items-center justify-between",children:[v.jsx("div",{className:"flex-1 min-w-0",children:v.jsxs("div",{className:"flex items-center space-x-2",children:[v.jsx("span",{className:"text-lg",children:d.file_type==="pdf"?"📄":d.file_type==="docx"?"📝":d.file_type==="txt"?"📃":"📊"}),v.jsxs("div",{className:"min-w-0 flex-1",children:[v.jsx("p",{className:"text-white font-medium truncate",children:d.filename}),v.jsxs("p",{className:"text-sm text-gray-400",children:[d.file_type.toUpperCase()," • ",Math.round(d.file_size/1024)," KB"]})]})]})}),v.jsx("div",{className:`
                  w-5 h-5 rounded border-2 flex items-center justify-center
                  ${p?"bg-primary-500 border-primary-500":"border-gray-500"}
                `,children:p&&v.jsx("svg",{className:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:v.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]})},d.id)})}),l.length===0&&a&&v.jsx("div",{className:"text-center py-4 text-gray-400",children:"No documents match your search."})]})},Rh=({type:e})=>{const[t,n]=k.useState([]),[r,i]=k.useState(""),[o,a]=k.useState(10),[s,l]=k.useState(""),[u,c]=k.useState({}),{generateFlashcards:d,generateQuiz:p,isGenerating:g,generationProgress:y}=a_(),{user:w,updateUser:_}=fa(),m=gi(),h=()=>{const E={};return t.length===0&&(E.documents="Please select at least one document"),r.trim()||(E.name="Study set name is required"),(o<1||o>50)&&(E.count="Item count must be between 1 and 50"),c(E),Object.keys(E).length===0},f=async E=>{if(E.preventDefault(),!!h()){if(!w||w.credits_remaining<1){alert("Insufficient credits. Please purchase more credits to continue.");return}try{const j={documentIds:t,name:r.trim(),count:o,customPrompt:s.trim()||void 0};let O;e==="flashcards"?O=await d(j):O=await p(j),_({credits_remaining:O.creditsRemaining}),m(`/study-sets/${O.studySet.id}`)}catch(j){console.error("Generation error:",j),alert(j.message||"Failed to generate study materials. Please try again.")}}},x=1,S=w&&w.credits_remaining>=x;return v.jsx("div",{className:"max-w-2xl mx-auto",children:v.jsxs("form",{onSubmit:f,className:"space-y-6",children:[v.jsxs("div",{className:"text-center",children:[v.jsxs("h2",{className:"text-2xl font-bold text-white mb-2",children:["Generate ",e==="flashcards"?"Flashcards":"Quiz Questions"]}),v.jsx("p",{className:"text-gray-400",children:"Create AI-powered study materials from your documents"})]}),v.jsx("div",{className:"bg-background-secondary rounded-lg p-4",children:v.jsxs("div",{className:"flex items-center justify-between",children:[v.jsxs("div",{children:[v.jsxs("p",{className:"text-sm text-gray-300",children:["Cost: ",v.jsxs("span",{className:"font-medium text-primary-400",children:[x," credit"]})]}),v.jsxs("p",{className:"text-sm text-gray-400",children:["Your balance: ",(w==null?void 0:w.credits_remaining)||0," credits"]})]}),!S&&v.jsx(lt,{onClick:()=>m("/credits"),variant:"secondary",size:"sm",children:"Buy Credits"})]})}),v.jsxs("div",{children:[v.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Documents *"}),v.jsx("div",{className:"bg-background-secondary rounded-lg p-4",children:v.jsx(s_,{selectedDocuments:t,onSelectionChange:n,maxSelection:5})}),u.documents&&v.jsx("p",{className:"mt-1 text-sm text-red-500",children:u.documents})]}),v.jsx(Lt,{label:"Study Set Name",value:r,onChange:i,placeholder:`My ${e==="flashcards"?"Flashcards":"Quiz"}`,error:u.name,required:!0}),v.jsxs("div",{children:[v.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:["Number of ",e==="flashcards"?"Flashcards":"Questions"]}),v.jsx("input",{type:"number",min:"1",max:"50",value:o,onChange:E=>a(parseInt(E.target.value)||10),className:"w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary-500"}),u.count&&v.jsx("p",{className:"mt-1 text-sm text-red-500",children:u.count})]}),v.jsxs("div",{children:[v.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Custom Instructions (Optional)"}),v.jsx("textarea",{value:s,onChange:E=>l(E.target.value),placeholder:"Add specific instructions for the AI (e.g., focus on key concepts, include examples, etc.)",rows:3,className:"w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),g&&v.jsx("div",{className:"bg-primary-500/10 border border-primary-500/30 rounded-lg p-4",children:v.jsxs("div",{className:"flex items-center space-x-3",children:[v.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-primary-500"}),v.jsx("span",{className:"text-primary-400",children:y})]})}),v.jsx(lt,{type:"submit",isLoading:g,disabled:!S,className:"w-full",size:"lg",children:g?"Generating...":`Generate ${e==="flashcards"?"Flashcards":"Quiz"} (${x} credit)`}),!S&&v.jsx("p",{className:"text-center text-sm text-red-400",children:"Insufficient credits. Please purchase more credits to continue."})]})})},l_=()=>v.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:v.jsx(Rh,{type:"flashcards"})}),u_=()=>v.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:v.jsx(Rh,{type:"quiz"})});function c_(){return v.jsx(sy,{children:v.jsxs(ey,{children:[v.jsx(bt,{path:"/",element:v.jsx(Mf,{to:"/login"})}),v.jsx(bt,{path:"/login",element:v.jsx(tx,{})}),v.jsx(bt,{path:"/signup",element:v.jsx(nx,{})}),v.jsx(bt,{path:"/dashboard",element:v.jsx(Ji,{children:v.jsx(rx,{})})}),v.jsx(bt,{path:"/documents",element:v.jsx(Ji,{children:v.jsx(o_,{})})}),v.jsx(bt,{path:"/generate/flashcards",element:v.jsx(Ji,{children:v.jsx(l_,{})})}),v.jsx(bt,{path:"/generate/quiz",element:v.jsx(Ji,{children:v.jsx(u_,{})})})]})})}os.createRoot(document.getElementById("root")).render(v.jsx(Bo.StrictMode,{children:v.jsx(c_,{})}));
