function Lh(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in e)){const s=Object.getOwnPropertyDescriptor(r,i);s&&Object.defineProperty(e,i,s.get?s:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const a of s.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();var Qe=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function vl(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Uh(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var i=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,i.get?i:{enumerable:!0,get:function(){return e[r]}})}),n}var wd={exports:{}},Ms={},_d={exports:{}},L={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mi=Symbol.for("react.element"),Fh=Symbol.for("react.portal"),Mh=Symbol.for("react.fragment"),Bh=Symbol.for("react.strict_mode"),Wh=Symbol.for("react.profiler"),qh=Symbol.for("react.provider"),Hh=Symbol.for("react.context"),Vh=Symbol.for("react.forward_ref"),Kh=Symbol.for("react.suspense"),Qh=Symbol.for("react.memo"),Gh=Symbol.for("react.lazy"),Cc=Symbol.iterator;function Jh(e){return e===null||typeof e!="object"?null:(e=Cc&&e[Cc]||e["@@iterator"],typeof e=="function"?e:null)}var kd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Sd=Object.assign,bd={};function ur(e,t,n){this.props=e,this.context=t,this.refs=bd,this.updater=n||kd}ur.prototype.isReactComponent={};ur.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};ur.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function jd(){}jd.prototype=ur.prototype;function gl(e,t,n){this.props=e,this.context=t,this.refs=bd,this.updater=n||kd}var yl=gl.prototype=new jd;yl.constructor=gl;Sd(yl,ur.prototype);yl.isPureReactComponent=!0;var Pc=Array.isArray,Ed=Object.prototype.hasOwnProperty,xl={current:null},Cd={key:!0,ref:!0,__self:!0,__source:!0};function Pd(e,t,n){var r,i={},s=null,a=null;if(t!=null)for(r in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(s=""+t.key),t)Ed.call(t,r)&&!Cd.hasOwnProperty(r)&&(i[r]=t[r]);var o=arguments.length-2;if(o===1)i.children=n;else if(1<o){for(var l=Array(o),c=0;c<o;c++)l[c]=arguments[c+2];i.children=l}if(e&&e.defaultProps)for(r in o=e.defaultProps,o)i[r]===void 0&&(i[r]=o[r]);return{$$typeof:mi,type:e,key:s,ref:a,props:i,_owner:xl.current}}function Yh(e,t){return{$$typeof:mi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function wl(e){return typeof e=="object"&&e!==null&&e.$$typeof===mi}function Xh(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Tc=/\/+/g;function ba(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Xh(""+e.key):t.toString(36)}function Xi(e,t,n,r,i){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var a=!1;if(e===null)a=!0;else switch(s){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case mi:case Fh:a=!0}}if(a)return a=e,i=i(a),e=r===""?"."+ba(a,0):r,Pc(i)?(n="",e!=null&&(n=e.replace(Tc,"$&/")+"/"),Xi(i,t,n,"",function(c){return c})):i!=null&&(wl(i)&&(i=Yh(i,n+(!i.key||a&&a.key===i.key?"":(""+i.key).replace(Tc,"$&/")+"/")+e)),t.push(i)),1;if(a=0,r=r===""?".":r+":",Pc(e))for(var o=0;o<e.length;o++){s=e[o];var l=r+ba(s,o);a+=Xi(s,t,n,l,i)}else if(l=Jh(e),typeof l=="function")for(e=l.call(e),o=0;!(s=e.next()).done;)s=s.value,l=r+ba(s,o++),a+=Xi(s,t,n,l,i);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function Pi(e,t,n){if(e==null)return e;var r=[],i=0;return Xi(e,r,"","",function(s){return t.call(n,s,i++)}),r}function Zh(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ee={current:null},Zi={transition:null},em={ReactCurrentDispatcher:Ee,ReactCurrentBatchConfig:Zi,ReactCurrentOwner:xl};function Td(){throw Error("act(...) is not supported in production builds of React.")}L.Children={map:Pi,forEach:function(e,t,n){Pi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Pi(e,function(){t++}),t},toArray:function(e){return Pi(e,function(t){return t})||[]},only:function(e){if(!wl(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};L.Component=ur;L.Fragment=Mh;L.Profiler=Wh;L.PureComponent=gl;L.StrictMode=Bh;L.Suspense=Kh;L.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=em;L.act=Td;L.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Sd({},e.props),i=e.key,s=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,a=xl.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var o=e.type.defaultProps;for(l in t)Ed.call(t,l)&&!Cd.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&o!==void 0?o[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){o=Array(l);for(var c=0;c<l;c++)o[c]=arguments[c+2];r.children=o}return{$$typeof:mi,type:e.type,key:i,ref:s,props:r,_owner:a}};L.createContext=function(e){return e={$$typeof:Hh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:qh,_context:e},e.Consumer=e};L.createElement=Pd;L.createFactory=function(e){var t=Pd.bind(null,e);return t.type=e,t};L.createRef=function(){return{current:null}};L.forwardRef=function(e){return{$$typeof:Vh,render:e}};L.isValidElement=wl;L.lazy=function(e){return{$$typeof:Gh,_payload:{_status:-1,_result:e},_init:Zh}};L.memo=function(e,t){return{$$typeof:Qh,type:e,compare:t===void 0?null:t}};L.startTransition=function(e){var t=Zi.transition;Zi.transition={};try{e()}finally{Zi.transition=t}};L.unstable_act=Td;L.useCallback=function(e,t){return Ee.current.useCallback(e,t)};L.useContext=function(e){return Ee.current.useContext(e)};L.useDebugValue=function(){};L.useDeferredValue=function(e){return Ee.current.useDeferredValue(e)};L.useEffect=function(e,t){return Ee.current.useEffect(e,t)};L.useId=function(){return Ee.current.useId()};L.useImperativeHandle=function(e,t,n){return Ee.current.useImperativeHandle(e,t,n)};L.useInsertionEffect=function(e,t){return Ee.current.useInsertionEffect(e,t)};L.useLayoutEffect=function(e,t){return Ee.current.useLayoutEffect(e,t)};L.useMemo=function(e,t){return Ee.current.useMemo(e,t)};L.useReducer=function(e,t,n){return Ee.current.useReducer(e,t,n)};L.useRef=function(e){return Ee.current.useRef(e)};L.useState=function(e){return Ee.current.useState(e)};L.useSyncExternalStore=function(e,t,n){return Ee.current.useSyncExternalStore(e,t,n)};L.useTransition=function(){return Ee.current.useTransition()};L.version="18.3.1";_d.exports=L;var _=_d.exports;const Bs=vl(_),tm=Lh({__proto__:null,default:Bs},[_]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var nm=_,rm=Symbol.for("react.element"),im=Symbol.for("react.fragment"),sm=Object.prototype.hasOwnProperty,am=nm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,om={key:!0,ref:!0,__self:!0,__source:!0};function Od(e,t,n){var r,i={},s=null,a=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(a=t.ref);for(r in t)sm.call(t,r)&&!om.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:rm,type:e,key:s,ref:a,props:i,_owner:am.current}}Ms.Fragment=im;Ms.jsx=Od;Ms.jsxs=Od;wd.exports=Ms;var p=wd.exports,ao={},Nd={exports:{}},Be={},Id={exports:{}},Rd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(T,R){var $=T.length;T.push(R);e:for(;0<$;){var K=$-1>>>1,G=T[K];if(0<i(G,R))T[K]=R,T[$]=G,$=K;else break e}}function n(T){return T.length===0?null:T[0]}function r(T){if(T.length===0)return null;var R=T[0],$=T.pop();if($!==R){T[0]=$;e:for(var K=0,G=T.length,tn=G>>>1;K<tn;){var Te=2*(K+1)-1,jn=T[Te],Se=Te+1,nn=T[Se];if(0>i(jn,$))Se<G&&0>i(nn,jn)?(T[K]=nn,T[Se]=$,K=Se):(T[K]=jn,T[Te]=$,K=Te);else if(Se<G&&0>i(nn,$))T[K]=nn,T[Se]=$,K=Se;else break e}}return R}function i(T,R){var $=T.sortIndex-R.sortIndex;return $!==0?$:T.id-R.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var a=Date,o=a.now();e.unstable_now=function(){return a.now()-o}}var l=[],c=[],u=1,d=null,f=3,g=!1,y=!1,x=!1,k=typeof setTimeout=="function"?setTimeout:null,v=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function h(T){for(var R=n(c);R!==null;){if(R.callback===null)r(c);else if(R.startTime<=T)r(c),R.sortIndex=R.expirationTime,t(l,R);else break;R=n(c)}}function w(T){if(x=!1,h(T),!y)if(n(l)!==null)y=!0,yr(S);else{var R=n(c);R!==null&&bn(w,R.startTime-T)}}function S(T,R){y=!1,x&&(x=!1,v(C),C=-1),g=!0;var $=f;try{for(h(R),d=n(l);d!==null&&(!(d.expirationTime>R)||T&&!M());){var K=d.callback;if(typeof K=="function"){d.callback=null,f=d.priorityLevel;var G=K(d.expirationTime<=R);R=e.unstable_now(),typeof G=="function"?d.callback=G:d===n(l)&&r(l),h(R)}else r(l);d=n(l)}if(d!==null)var tn=!0;else{var Te=n(c);Te!==null&&bn(w,Te.startTime-R),tn=!1}return tn}finally{d=null,f=$,g=!1}}var b=!1,E=null,C=-1,N=5,I=-1;function M(){return!(e.unstable_now()-I<N)}function le(){if(E!==null){var T=e.unstable_now();I=T;var R=!0;try{R=E(!0,T)}finally{R?he():(b=!1,E=null)}}else b=!1}var he;if(typeof m=="function")he=function(){m(le)};else if(typeof MessageChannel<"u"){var ce=new MessageChannel,Pe=ce.port2;ce.port1.onmessage=le,he=function(){Pe.postMessage(null)}}else he=function(){k(le,0)};function yr(T){E=T,b||(b=!0,he())}function bn(T,R){C=k(function(){T(e.unstable_now())},R)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(T){T.callback=null},e.unstable_continueExecution=function(){y||g||(y=!0,yr(S))},e.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):N=0<T?Math.floor(1e3/T):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(T){switch(f){case 1:case 2:case 3:var R=3;break;default:R=f}var $=f;f=R;try{return T()}finally{f=$}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(T,R){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var $=f;f=T;try{return R()}finally{f=$}},e.unstable_scheduleCallback=function(T,R,$){var K=e.unstable_now();switch(typeof $=="object"&&$!==null?($=$.delay,$=typeof $=="number"&&0<$?K+$:K):$=K,T){case 1:var G=-1;break;case 2:G=250;break;case 5:G=**********;break;case 4:G=1e4;break;default:G=5e3}return G=$+G,T={id:u++,callback:R,priorityLevel:T,startTime:$,expirationTime:G,sortIndex:-1},$>K?(T.sortIndex=$,t(c,T),n(l)===null&&T===n(c)&&(x?(v(C),C=-1):x=!0,bn(w,$-K))):(T.sortIndex=G,t(l,T),y||g||(y=!0,yr(S))),T},e.unstable_shouldYield=M,e.unstable_wrapCallback=function(T){var R=f;return function(){var $=f;f=R;try{return T.apply(this,arguments)}finally{f=$}}}})(Rd);Id.exports=Rd;var lm=Id.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cm=_,Me=lm;function j(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Ad=new Set,Kr={};function wn(e,t){er(e,t),er(e+"Capture",t)}function er(e,t){for(Kr[e]=t,e=0;e<t.length;e++)Ad.add(t[e])}var wt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),oo=Object.prototype.hasOwnProperty,um=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Oc={},Nc={};function dm(e){return oo.call(Nc,e)?!0:oo.call(Oc,e)?!1:um.test(e)?Nc[e]=!0:(Oc[e]=!0,!1)}function pm(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function fm(e,t,n,r){if(t===null||typeof t>"u"||pm(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ce(e,t,n,r,i,s,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=a}var ge={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ge[e]=new Ce(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ge[t]=new Ce(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ge[e]=new Ce(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ge[e]=new Ce(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ge[e]=new Ce(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ge[e]=new Ce(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ge[e]=new Ce(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ge[e]=new Ce(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ge[e]=new Ce(e,5,!1,e.toLowerCase(),null,!1,!1)});var _l=/[\-:]([a-z])/g;function kl(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(_l,kl);ge[t]=new Ce(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(_l,kl);ge[t]=new Ce(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(_l,kl);ge[t]=new Ce(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ge[e]=new Ce(e,1,!1,e.toLowerCase(),null,!1,!1)});ge.xlinkHref=new Ce("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ge[e]=new Ce(e,1,!1,e.toLowerCase(),null,!0,!0)});function Sl(e,t,n,r){var i=ge.hasOwnProperty(t)?ge[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(fm(t,n,i,r)&&(n=null),r||i===null?dm(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var bt=cm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ti=Symbol.for("react.element"),$n=Symbol.for("react.portal"),zn=Symbol.for("react.fragment"),bl=Symbol.for("react.strict_mode"),lo=Symbol.for("react.profiler"),Dd=Symbol.for("react.provider"),$d=Symbol.for("react.context"),jl=Symbol.for("react.forward_ref"),co=Symbol.for("react.suspense"),uo=Symbol.for("react.suspense_list"),El=Symbol.for("react.memo"),Tt=Symbol.for("react.lazy"),zd=Symbol.for("react.offscreen"),Ic=Symbol.iterator;function wr(e){return e===null||typeof e!="object"?null:(e=Ic&&e[Ic]||e["@@iterator"],typeof e=="function"?e:null)}var Z=Object.assign,ja;function Tr(e){if(ja===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);ja=t&&t[1]||""}return`
`+ja+e}var Ea=!1;function Ca(e,t){if(!e||Ea)return"";Ea=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var i=c.stack.split(`
`),s=r.stack.split(`
`),a=i.length-1,o=s.length-1;1<=a&&0<=o&&i[a]!==s[o];)o--;for(;1<=a&&0<=o;a--,o--)if(i[a]!==s[o]){if(a!==1||o!==1)do if(a--,o--,0>o||i[a]!==s[o]){var l=`
`+i[a].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=a&&0<=o);break}}}finally{Ea=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Tr(e):""}function hm(e){switch(e.tag){case 5:return Tr(e.type);case 16:return Tr("Lazy");case 13:return Tr("Suspense");case 19:return Tr("SuspenseList");case 0:case 2:case 15:return e=Ca(e.type,!1),e;case 11:return e=Ca(e.type.render,!1),e;case 1:return e=Ca(e.type,!0),e;default:return""}}function po(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case zn:return"Fragment";case $n:return"Portal";case lo:return"Profiler";case bl:return"StrictMode";case co:return"Suspense";case uo:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case $d:return(e.displayName||"Context")+".Consumer";case Dd:return(e._context.displayName||"Context")+".Provider";case jl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case El:return t=e.displayName||null,t!==null?t:po(e.type)||"Memo";case Tt:t=e._payload,e=e._init;try{return po(e(t))}catch{}}return null}function mm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return po(t);case 8:return t===bl?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Qt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Ld(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function vm(e){var t=Ld(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(a){r=""+a,s.call(this,a)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Oi(e){e._valueTracker||(e._valueTracker=vm(e))}function Ud(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Ld(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function us(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function fo(e,t){var n=t.checked;return Z({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Rc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Qt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Fd(e,t){t=t.checked,t!=null&&Sl(e,"checked",t,!1)}function ho(e,t){Fd(e,t);var n=Qt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?mo(e,t.type,n):t.hasOwnProperty("defaultValue")&&mo(e,t.type,Qt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ac(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function mo(e,t,n){(t!=="number"||us(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Or=Array.isArray;function Qn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Qt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function vo(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(j(91));return Z({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Dc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(j(92));if(Or(n)){if(1<n.length)throw Error(j(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Qt(n)}}function Md(e,t){var n=Qt(t.value),r=Qt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function $c(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Bd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function go(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Bd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ni,Wd=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ni=Ni||document.createElement("div"),Ni.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ni.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Qr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Ar={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},gm=["Webkit","ms","Moz","O"];Object.keys(Ar).forEach(function(e){gm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Ar[t]=Ar[e]})});function qd(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Ar.hasOwnProperty(e)&&Ar[e]?(""+t).trim():t+"px"}function Hd(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=qd(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var ym=Z({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function yo(e,t){if(t){if(ym[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(j(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(j(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(j(61))}if(t.style!=null&&typeof t.style!="object")throw Error(j(62))}}function xo(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var wo=null;function Cl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var _o=null,Gn=null,Jn=null;function zc(e){if(e=yi(e)){if(typeof _o!="function")throw Error(j(280));var t=e.stateNode;t&&(t=Ks(t),_o(e.stateNode,e.type,t))}}function Vd(e){Gn?Jn?Jn.push(e):Jn=[e]:Gn=e}function Kd(){if(Gn){var e=Gn,t=Jn;if(Jn=Gn=null,zc(e),t)for(e=0;e<t.length;e++)zc(t[e])}}function Qd(e,t){return e(t)}function Gd(){}var Pa=!1;function Jd(e,t,n){if(Pa)return e(t,n);Pa=!0;try{return Qd(e,t,n)}finally{Pa=!1,(Gn!==null||Jn!==null)&&(Gd(),Kd())}}function Gr(e,t){var n=e.stateNode;if(n===null)return null;var r=Ks(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(j(231,t,typeof n));return n}var ko=!1;if(wt)try{var _r={};Object.defineProperty(_r,"passive",{get:function(){ko=!0}}),window.addEventListener("test",_r,_r),window.removeEventListener("test",_r,_r)}catch{ko=!1}function xm(e,t,n,r,i,s,a,o,l){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(u){this.onError(u)}}var Dr=!1,ds=null,ps=!1,So=null,wm={onError:function(e){Dr=!0,ds=e}};function _m(e,t,n,r,i,s,a,o,l){Dr=!1,ds=null,xm.apply(wm,arguments)}function km(e,t,n,r,i,s,a,o,l){if(_m.apply(this,arguments),Dr){if(Dr){var c=ds;Dr=!1,ds=null}else throw Error(j(198));ps||(ps=!0,So=c)}}function _n(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Yd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Lc(e){if(_n(e)!==e)throw Error(j(188))}function Sm(e){var t=e.alternate;if(!t){if(t=_n(e),t===null)throw Error(j(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return Lc(i),e;if(s===r)return Lc(i),t;s=s.sibling}throw Error(j(188))}if(n.return!==r.return)n=i,r=s;else{for(var a=!1,o=i.child;o;){if(o===n){a=!0,n=i,r=s;break}if(o===r){a=!0,r=i,n=s;break}o=o.sibling}if(!a){for(o=s.child;o;){if(o===n){a=!0,n=s,r=i;break}if(o===r){a=!0,r=s,n=i;break}o=o.sibling}if(!a)throw Error(j(189))}}if(n.alternate!==r)throw Error(j(190))}if(n.tag!==3)throw Error(j(188));return n.stateNode.current===n?e:t}function Xd(e){return e=Sm(e),e!==null?Zd(e):null}function Zd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Zd(e);if(t!==null)return t;e=e.sibling}return null}var ep=Me.unstable_scheduleCallback,Uc=Me.unstable_cancelCallback,bm=Me.unstable_shouldYield,jm=Me.unstable_requestPaint,ne=Me.unstable_now,Em=Me.unstable_getCurrentPriorityLevel,Pl=Me.unstable_ImmediatePriority,tp=Me.unstable_UserBlockingPriority,fs=Me.unstable_NormalPriority,Cm=Me.unstable_LowPriority,np=Me.unstable_IdlePriority,Ws=null,dt=null;function Pm(e){if(dt&&typeof dt.onCommitFiberRoot=="function")try{dt.onCommitFiberRoot(Ws,e,void 0,(e.current.flags&128)===128)}catch{}}var rt=Math.clz32?Math.clz32:Nm,Tm=Math.log,Om=Math.LN2;function Nm(e){return e>>>=0,e===0?32:31-(Tm(e)/Om|0)|0}var Ii=64,Ri=4194304;function Nr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function hs(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,s=e.pingedLanes,a=n&268435455;if(a!==0){var o=a&~i;o!==0?r=Nr(o):(s&=a,s!==0&&(r=Nr(s)))}else a=n&~i,a!==0?r=Nr(a):s!==0&&(r=Nr(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,s=t&-t,i>=s||i===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-rt(t),i=1<<n,r|=e[n],t&=~i;return r}function Im(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Rm(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,s=e.pendingLanes;0<s;){var a=31-rt(s),o=1<<a,l=i[a];l===-1?(!(o&n)||o&r)&&(i[a]=Im(o,t)):l<=t&&(e.expiredLanes|=o),s&=~o}}function bo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function rp(){var e=Ii;return Ii<<=1,!(Ii&4194240)&&(Ii=64),e}function Ta(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-rt(t),e[t]=n}function Am(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-rt(n),s=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~s}}function Tl(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-rt(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var F=0;function ip(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var sp,Ol,ap,op,lp,jo=!1,Ai=[],Ut=null,Ft=null,Mt=null,Jr=new Map,Yr=new Map,It=[],Dm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Fc(e,t){switch(e){case"focusin":case"focusout":Ut=null;break;case"dragenter":case"dragleave":Ft=null;break;case"mouseover":case"mouseout":Mt=null;break;case"pointerover":case"pointerout":Jr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Yr.delete(t.pointerId)}}function kr(e,t,n,r,i,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},t!==null&&(t=yi(t),t!==null&&Ol(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function $m(e,t,n,r,i){switch(t){case"focusin":return Ut=kr(Ut,e,t,n,r,i),!0;case"dragenter":return Ft=kr(Ft,e,t,n,r,i),!0;case"mouseover":return Mt=kr(Mt,e,t,n,r,i),!0;case"pointerover":var s=i.pointerId;return Jr.set(s,kr(Jr.get(s)||null,e,t,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,Yr.set(s,kr(Yr.get(s)||null,e,t,n,r,i)),!0}return!1}function cp(e){var t=un(e.target);if(t!==null){var n=_n(t);if(n!==null){if(t=n.tag,t===13){if(t=Yd(n),t!==null){e.blockedOn=t,lp(e.priority,function(){ap(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function es(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Eo(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);wo=r,n.target.dispatchEvent(r),wo=null}else return t=yi(n),t!==null&&Ol(t),e.blockedOn=n,!1;t.shift()}return!0}function Mc(e,t,n){es(e)&&n.delete(t)}function zm(){jo=!1,Ut!==null&&es(Ut)&&(Ut=null),Ft!==null&&es(Ft)&&(Ft=null),Mt!==null&&es(Mt)&&(Mt=null),Jr.forEach(Mc),Yr.forEach(Mc)}function Sr(e,t){e.blockedOn===t&&(e.blockedOn=null,jo||(jo=!0,Me.unstable_scheduleCallback(Me.unstable_NormalPriority,zm)))}function Xr(e){function t(i){return Sr(i,e)}if(0<Ai.length){Sr(Ai[0],e);for(var n=1;n<Ai.length;n++){var r=Ai[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Ut!==null&&Sr(Ut,e),Ft!==null&&Sr(Ft,e),Mt!==null&&Sr(Mt,e),Jr.forEach(t),Yr.forEach(t),n=0;n<It.length;n++)r=It[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<It.length&&(n=It[0],n.blockedOn===null);)cp(n),n.blockedOn===null&&It.shift()}var Yn=bt.ReactCurrentBatchConfig,ms=!0;function Lm(e,t,n,r){var i=F,s=Yn.transition;Yn.transition=null;try{F=1,Nl(e,t,n,r)}finally{F=i,Yn.transition=s}}function Um(e,t,n,r){var i=F,s=Yn.transition;Yn.transition=null;try{F=4,Nl(e,t,n,r)}finally{F=i,Yn.transition=s}}function Nl(e,t,n,r){if(ms){var i=Eo(e,t,n,r);if(i===null)Ua(e,t,r,vs,n),Fc(e,r);else if($m(i,e,t,n,r))r.stopPropagation();else if(Fc(e,r),t&4&&-1<Dm.indexOf(e)){for(;i!==null;){var s=yi(i);if(s!==null&&sp(s),s=Eo(e,t,n,r),s===null&&Ua(e,t,r,vs,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else Ua(e,t,r,null,n)}}var vs=null;function Eo(e,t,n,r){if(vs=null,e=Cl(r),e=un(e),e!==null)if(t=_n(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Yd(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return vs=e,null}function up(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Em()){case Pl:return 1;case tp:return 4;case fs:case Cm:return 16;case np:return 536870912;default:return 16}default:return 16}}var Dt=null,Il=null,ts=null;function dp(){if(ts)return ts;var e,t=Il,n=t.length,r,i="value"in Dt?Dt.value:Dt.textContent,s=i.length;for(e=0;e<n&&t[e]===i[e];e++);var a=n-e;for(r=1;r<=a&&t[n-r]===i[s-r];r++);return ts=i.slice(e,1<r?1-r:void 0)}function ns(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Di(){return!0}function Bc(){return!1}function We(e){function t(n,r,i,s,a){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=a,this.currentTarget=null;for(var o in e)e.hasOwnProperty(o)&&(n=e[o],this[o]=n?n(s):s[o]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Di:Bc,this.isPropagationStopped=Bc,this}return Z(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Di)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Di)},persist:function(){},isPersistent:Di}),t}var dr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Rl=We(dr),gi=Z({},dr,{view:0,detail:0}),Fm=We(gi),Oa,Na,br,qs=Z({},gi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Al,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==br&&(br&&e.type==="mousemove"?(Oa=e.screenX-br.screenX,Na=e.screenY-br.screenY):Na=Oa=0,br=e),Oa)},movementY:function(e){return"movementY"in e?e.movementY:Na}}),Wc=We(qs),Mm=Z({},qs,{dataTransfer:0}),Bm=We(Mm),Wm=Z({},gi,{relatedTarget:0}),Ia=We(Wm),qm=Z({},dr,{animationName:0,elapsedTime:0,pseudoElement:0}),Hm=We(qm),Vm=Z({},dr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Km=We(Vm),Qm=Z({},dr,{data:0}),qc=We(Qm),Gm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Jm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ym={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Xm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Ym[e])?!!t[e]:!1}function Al(){return Xm}var Zm=Z({},gi,{key:function(e){if(e.key){var t=Gm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ns(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Jm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Al,charCode:function(e){return e.type==="keypress"?ns(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ns(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),ev=We(Zm),tv=Z({},qs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Hc=We(tv),nv=Z({},gi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Al}),rv=We(nv),iv=Z({},dr,{propertyName:0,elapsedTime:0,pseudoElement:0}),sv=We(iv),av=Z({},qs,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ov=We(av),lv=[9,13,27,32],Dl=wt&&"CompositionEvent"in window,$r=null;wt&&"documentMode"in document&&($r=document.documentMode);var cv=wt&&"TextEvent"in window&&!$r,pp=wt&&(!Dl||$r&&8<$r&&11>=$r),Vc=String.fromCharCode(32),Kc=!1;function fp(e,t){switch(e){case"keyup":return lv.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function hp(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Ln=!1;function uv(e,t){switch(e){case"compositionend":return hp(t);case"keypress":return t.which!==32?null:(Kc=!0,Vc);case"textInput":return e=t.data,e===Vc&&Kc?null:e;default:return null}}function dv(e,t){if(Ln)return e==="compositionend"||!Dl&&fp(e,t)?(e=dp(),ts=Il=Dt=null,Ln=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return pp&&t.locale!=="ko"?null:t.data;default:return null}}var pv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Qc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!pv[e.type]:t==="textarea"}function mp(e,t,n,r){Vd(r),t=gs(t,"onChange"),0<t.length&&(n=new Rl("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var zr=null,Zr=null;function fv(e){Ep(e,0)}function Hs(e){var t=Mn(e);if(Ud(t))return e}function hv(e,t){if(e==="change")return t}var vp=!1;if(wt){var Ra;if(wt){var Aa="oninput"in document;if(!Aa){var Gc=document.createElement("div");Gc.setAttribute("oninput","return;"),Aa=typeof Gc.oninput=="function"}Ra=Aa}else Ra=!1;vp=Ra&&(!document.documentMode||9<document.documentMode)}function Jc(){zr&&(zr.detachEvent("onpropertychange",gp),Zr=zr=null)}function gp(e){if(e.propertyName==="value"&&Hs(Zr)){var t=[];mp(t,Zr,e,Cl(e)),Jd(fv,t)}}function mv(e,t,n){e==="focusin"?(Jc(),zr=t,Zr=n,zr.attachEvent("onpropertychange",gp)):e==="focusout"&&Jc()}function vv(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Hs(Zr)}function gv(e,t){if(e==="click")return Hs(t)}function yv(e,t){if(e==="input"||e==="change")return Hs(t)}function xv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var st=typeof Object.is=="function"?Object.is:xv;function ei(e,t){if(st(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!oo.call(t,i)||!st(e[i],t[i]))return!1}return!0}function Yc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Xc(e,t){var n=Yc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Yc(n)}}function yp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?yp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function xp(){for(var e=window,t=us();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=us(e.document)}return t}function $l(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function wv(e){var t=xp(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&yp(n.ownerDocument.documentElement,n)){if(r!==null&&$l(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!e.extend&&s>r&&(i=r,r=s,s=i),i=Xc(n,s);var a=Xc(n,r);i&&a&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var _v=wt&&"documentMode"in document&&11>=document.documentMode,Un=null,Co=null,Lr=null,Po=!1;function Zc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Po||Un==null||Un!==us(r)||(r=Un,"selectionStart"in r&&$l(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Lr&&ei(Lr,r)||(Lr=r,r=gs(Co,"onSelect"),0<r.length&&(t=new Rl("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Un)))}function $i(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Fn={animationend:$i("Animation","AnimationEnd"),animationiteration:$i("Animation","AnimationIteration"),animationstart:$i("Animation","AnimationStart"),transitionend:$i("Transition","TransitionEnd")},Da={},wp={};wt&&(wp=document.createElement("div").style,"AnimationEvent"in window||(delete Fn.animationend.animation,delete Fn.animationiteration.animation,delete Fn.animationstart.animation),"TransitionEvent"in window||delete Fn.transitionend.transition);function Vs(e){if(Da[e])return Da[e];if(!Fn[e])return e;var t=Fn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in wp)return Da[e]=t[n];return e}var _p=Vs("animationend"),kp=Vs("animationiteration"),Sp=Vs("animationstart"),bp=Vs("transitionend"),jp=new Map,eu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Jt(e,t){jp.set(e,t),wn(t,[e])}for(var $a=0;$a<eu.length;$a++){var za=eu[$a],kv=za.toLowerCase(),Sv=za[0].toUpperCase()+za.slice(1);Jt(kv,"on"+Sv)}Jt(_p,"onAnimationEnd");Jt(kp,"onAnimationIteration");Jt(Sp,"onAnimationStart");Jt("dblclick","onDoubleClick");Jt("focusin","onFocus");Jt("focusout","onBlur");Jt(bp,"onTransitionEnd");er("onMouseEnter",["mouseout","mouseover"]);er("onMouseLeave",["mouseout","mouseover"]);er("onPointerEnter",["pointerout","pointerover"]);er("onPointerLeave",["pointerout","pointerover"]);wn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));wn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));wn("onBeforeInput",["compositionend","keypress","textInput","paste"]);wn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));wn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));wn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ir="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),bv=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ir));function tu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,km(r,t,void 0,e),e.currentTarget=null}function Ep(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var a=r.length-1;0<=a;a--){var o=r[a],l=o.instance,c=o.currentTarget;if(o=o.listener,l!==s&&i.isPropagationStopped())break e;tu(i,o,c),s=l}else for(a=0;a<r.length;a++){if(o=r[a],l=o.instance,c=o.currentTarget,o=o.listener,l!==s&&i.isPropagationStopped())break e;tu(i,o,c),s=l}}}if(ps)throw e=So,ps=!1,So=null,e}function H(e,t){var n=t[Ro];n===void 0&&(n=t[Ro]=new Set);var r=e+"__bubble";n.has(r)||(Cp(t,e,2,!1),n.add(r))}function La(e,t,n){var r=0;t&&(r|=4),Cp(n,e,r,t)}var zi="_reactListening"+Math.random().toString(36).slice(2);function ti(e){if(!e[zi]){e[zi]=!0,Ad.forEach(function(n){n!=="selectionchange"&&(bv.has(n)||La(n,!1,e),La(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[zi]||(t[zi]=!0,La("selectionchange",!1,t))}}function Cp(e,t,n,r){switch(up(t)){case 1:var i=Lm;break;case 4:i=Um;break;default:i=Nl}n=i.bind(null,t,n,e),i=void 0,!ko||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Ua(e,t,n,r,i){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var o=r.stateNode.containerInfo;if(o===i||o.nodeType===8&&o.parentNode===i)break;if(a===4)for(a=r.return;a!==null;){var l=a.tag;if((l===3||l===4)&&(l=a.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;a=a.return}for(;o!==null;){if(a=un(o),a===null)return;if(l=a.tag,l===5||l===6){r=s=a;continue e}o=o.parentNode}}r=r.return}Jd(function(){var c=s,u=Cl(n),d=[];e:{var f=jp.get(e);if(f!==void 0){var g=Rl,y=e;switch(e){case"keypress":if(ns(n)===0)break e;case"keydown":case"keyup":g=ev;break;case"focusin":y="focus",g=Ia;break;case"focusout":y="blur",g=Ia;break;case"beforeblur":case"afterblur":g=Ia;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=Wc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=Bm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=rv;break;case _p:case kp:case Sp:g=Hm;break;case bp:g=sv;break;case"scroll":g=Fm;break;case"wheel":g=ov;break;case"copy":case"cut":case"paste":g=Km;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=Hc}var x=(t&4)!==0,k=!x&&e==="scroll",v=x?f!==null?f+"Capture":null:f;x=[];for(var m=c,h;m!==null;){h=m;var w=h.stateNode;if(h.tag===5&&w!==null&&(h=w,v!==null&&(w=Gr(m,v),w!=null&&x.push(ni(m,w,h)))),k)break;m=m.return}0<x.length&&(f=new g(f,y,null,n,u),d.push({event:f,listeners:x}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",f&&n!==wo&&(y=n.relatedTarget||n.fromElement)&&(un(y)||y[_t]))break e;if((g||f)&&(f=u.window===u?u:(f=u.ownerDocument)?f.defaultView||f.parentWindow:window,g?(y=n.relatedTarget||n.toElement,g=c,y=y?un(y):null,y!==null&&(k=_n(y),y!==k||y.tag!==5&&y.tag!==6)&&(y=null)):(g=null,y=c),g!==y)){if(x=Wc,w="onMouseLeave",v="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(x=Hc,w="onPointerLeave",v="onPointerEnter",m="pointer"),k=g==null?f:Mn(g),h=y==null?f:Mn(y),f=new x(w,m+"leave",g,n,u),f.target=k,f.relatedTarget=h,w=null,un(u)===c&&(x=new x(v,m+"enter",y,n,u),x.target=h,x.relatedTarget=k,w=x),k=w,g&&y)t:{for(x=g,v=y,m=0,h=x;h;h=Tn(h))m++;for(h=0,w=v;w;w=Tn(w))h++;for(;0<m-h;)x=Tn(x),m--;for(;0<h-m;)v=Tn(v),h--;for(;m--;){if(x===v||v!==null&&x===v.alternate)break t;x=Tn(x),v=Tn(v)}x=null}else x=null;g!==null&&nu(d,f,g,x,!1),y!==null&&k!==null&&nu(d,k,y,x,!0)}}e:{if(f=c?Mn(c):window,g=f.nodeName&&f.nodeName.toLowerCase(),g==="select"||g==="input"&&f.type==="file")var S=hv;else if(Qc(f))if(vp)S=yv;else{S=vv;var b=mv}else(g=f.nodeName)&&g.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(S=gv);if(S&&(S=S(e,c))){mp(d,S,n,u);break e}b&&b(e,f,c),e==="focusout"&&(b=f._wrapperState)&&b.controlled&&f.type==="number"&&mo(f,"number",f.value)}switch(b=c?Mn(c):window,e){case"focusin":(Qc(b)||b.contentEditable==="true")&&(Un=b,Co=c,Lr=null);break;case"focusout":Lr=Co=Un=null;break;case"mousedown":Po=!0;break;case"contextmenu":case"mouseup":case"dragend":Po=!1,Zc(d,n,u);break;case"selectionchange":if(_v)break;case"keydown":case"keyup":Zc(d,n,u)}var E;if(Dl)e:{switch(e){case"compositionstart":var C="onCompositionStart";break e;case"compositionend":C="onCompositionEnd";break e;case"compositionupdate":C="onCompositionUpdate";break e}C=void 0}else Ln?fp(e,n)&&(C="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(C="onCompositionStart");C&&(pp&&n.locale!=="ko"&&(Ln||C!=="onCompositionStart"?C==="onCompositionEnd"&&Ln&&(E=dp()):(Dt=u,Il="value"in Dt?Dt.value:Dt.textContent,Ln=!0)),b=gs(c,C),0<b.length&&(C=new qc(C,e,null,n,u),d.push({event:C,listeners:b}),E?C.data=E:(E=hp(n),E!==null&&(C.data=E)))),(E=cv?uv(e,n):dv(e,n))&&(c=gs(c,"onBeforeInput"),0<c.length&&(u=new qc("onBeforeInput","beforeinput",null,n,u),d.push({event:u,listeners:c}),u.data=E))}Ep(d,t)})}function ni(e,t,n){return{instance:e,listener:t,currentTarget:n}}function gs(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=Gr(e,n),s!=null&&r.unshift(ni(e,s,i)),s=Gr(e,t),s!=null&&r.push(ni(e,s,i))),e=e.return}return r}function Tn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function nu(e,t,n,r,i){for(var s=t._reactName,a=[];n!==null&&n!==r;){var o=n,l=o.alternate,c=o.stateNode;if(l!==null&&l===r)break;o.tag===5&&c!==null&&(o=c,i?(l=Gr(n,s),l!=null&&a.unshift(ni(n,l,o))):i||(l=Gr(n,s),l!=null&&a.push(ni(n,l,o)))),n=n.return}a.length!==0&&e.push({event:t,listeners:a})}var jv=/\r\n?/g,Ev=/\u0000|\uFFFD/g;function ru(e){return(typeof e=="string"?e:""+e).replace(jv,`
`).replace(Ev,"")}function Li(e,t,n){if(t=ru(t),ru(e)!==t&&n)throw Error(j(425))}function ys(){}var To=null,Oo=null;function No(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Io=typeof setTimeout=="function"?setTimeout:void 0,Cv=typeof clearTimeout=="function"?clearTimeout:void 0,iu=typeof Promise=="function"?Promise:void 0,Pv=typeof queueMicrotask=="function"?queueMicrotask:typeof iu<"u"?function(e){return iu.resolve(null).then(e).catch(Tv)}:Io;function Tv(e){setTimeout(function(){throw e})}function Fa(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Xr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Xr(t)}function Bt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function su(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var pr=Math.random().toString(36).slice(2),ut="__reactFiber$"+pr,ri="__reactProps$"+pr,_t="__reactContainer$"+pr,Ro="__reactEvents$"+pr,Ov="__reactListeners$"+pr,Nv="__reactHandles$"+pr;function un(e){var t=e[ut];if(t)return t;for(var n=e.parentNode;n;){if(t=n[_t]||n[ut]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=su(e);e!==null;){if(n=e[ut])return n;e=su(e)}return t}e=n,n=e.parentNode}return null}function yi(e){return e=e[ut]||e[_t],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Mn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(j(33))}function Ks(e){return e[ri]||null}var Ao=[],Bn=-1;function Yt(e){return{current:e}}function V(e){0>Bn||(e.current=Ao[Bn],Ao[Bn]=null,Bn--)}function q(e,t){Bn++,Ao[Bn]=e.current,e.current=t}var Gt={},ke=Yt(Gt),Ae=Yt(!1),mn=Gt;function tr(e,t){var n=e.type.contextTypes;if(!n)return Gt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function De(e){return e=e.childContextTypes,e!=null}function xs(){V(Ae),V(ke)}function au(e,t,n){if(ke.current!==Gt)throw Error(j(168));q(ke,t),q(Ae,n)}function Pp(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(j(108,mm(e)||"Unknown",i));return Z({},n,r)}function ws(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Gt,mn=ke.current,q(ke,e),q(Ae,Ae.current),!0}function ou(e,t,n){var r=e.stateNode;if(!r)throw Error(j(169));n?(e=Pp(e,t,mn),r.__reactInternalMemoizedMergedChildContext=e,V(Ae),V(ke),q(ke,e)):V(Ae),q(Ae,n)}var vt=null,Qs=!1,Ma=!1;function Tp(e){vt===null?vt=[e]:vt.push(e)}function Iv(e){Qs=!0,Tp(e)}function Xt(){if(!Ma&&vt!==null){Ma=!0;var e=0,t=F;try{var n=vt;for(F=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}vt=null,Qs=!1}catch(i){throw vt!==null&&(vt=vt.slice(e+1)),ep(Pl,Xt),i}finally{F=t,Ma=!1}}return null}var Wn=[],qn=0,_s=null,ks=0,qe=[],He=0,vn=null,gt=1,yt="";function sn(e,t){Wn[qn++]=ks,Wn[qn++]=_s,_s=e,ks=t}function Op(e,t,n){qe[He++]=gt,qe[He++]=yt,qe[He++]=vn,vn=e;var r=gt;e=yt;var i=32-rt(r)-1;r&=~(1<<i),n+=1;var s=32-rt(t)+i;if(30<s){var a=i-i%5;s=(r&(1<<a)-1).toString(32),r>>=a,i-=a,gt=1<<32-rt(t)+i|n<<i|r,yt=s+e}else gt=1<<s|n<<i|r,yt=e}function zl(e){e.return!==null&&(sn(e,1),Op(e,1,0))}function Ll(e){for(;e===_s;)_s=Wn[--qn],Wn[qn]=null,ks=Wn[--qn],Wn[qn]=null;for(;e===vn;)vn=qe[--He],qe[He]=null,yt=qe[--He],qe[He]=null,gt=qe[--He],qe[He]=null}var Fe=null,Ue=null,Q=!1,nt=null;function Np(e,t){var n=Ve(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function lu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Fe=e,Ue=Bt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Fe=e,Ue=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=vn!==null?{id:gt,overflow:yt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ve(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Fe=e,Ue=null,!0):!1;default:return!1}}function Do(e){return(e.mode&1)!==0&&(e.flags&128)===0}function $o(e){if(Q){var t=Ue;if(t){var n=t;if(!lu(e,t)){if(Do(e))throw Error(j(418));t=Bt(n.nextSibling);var r=Fe;t&&lu(e,t)?Np(r,n):(e.flags=e.flags&-4097|2,Q=!1,Fe=e)}}else{if(Do(e))throw Error(j(418));e.flags=e.flags&-4097|2,Q=!1,Fe=e}}}function cu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Fe=e}function Ui(e){if(e!==Fe)return!1;if(!Q)return cu(e),Q=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!No(e.type,e.memoizedProps)),t&&(t=Ue)){if(Do(e))throw Ip(),Error(j(418));for(;t;)Np(e,t),t=Bt(t.nextSibling)}if(cu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(j(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ue=Bt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ue=null}}else Ue=Fe?Bt(e.stateNode.nextSibling):null;return!0}function Ip(){for(var e=Ue;e;)e=Bt(e.nextSibling)}function nr(){Ue=Fe=null,Q=!1}function Ul(e){nt===null?nt=[e]:nt.push(e)}var Rv=bt.ReactCurrentBatchConfig;function jr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(j(309));var r=n.stateNode}if(!r)throw Error(j(147,e));var i=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(a){var o=i.refs;a===null?delete o[s]:o[s]=a},t._stringRef=s,t)}if(typeof e!="string")throw Error(j(284));if(!n._owner)throw Error(j(290,e))}return e}function Fi(e,t){throw e=Object.prototype.toString.call(t),Error(j(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function uu(e){var t=e._init;return t(e._payload)}function Rp(e){function t(v,m){if(e){var h=v.deletions;h===null?(v.deletions=[m],v.flags|=16):h.push(m)}}function n(v,m){if(!e)return null;for(;m!==null;)t(v,m),m=m.sibling;return null}function r(v,m){for(v=new Map;m!==null;)m.key!==null?v.set(m.key,m):v.set(m.index,m),m=m.sibling;return v}function i(v,m){return v=Vt(v,m),v.index=0,v.sibling=null,v}function s(v,m,h){return v.index=h,e?(h=v.alternate,h!==null?(h=h.index,h<m?(v.flags|=2,m):h):(v.flags|=2,m)):(v.flags|=1048576,m)}function a(v){return e&&v.alternate===null&&(v.flags|=2),v}function o(v,m,h,w){return m===null||m.tag!==6?(m=Qa(h,v.mode,w),m.return=v,m):(m=i(m,h),m.return=v,m)}function l(v,m,h,w){var S=h.type;return S===zn?u(v,m,h.props.children,w,h.key):m!==null&&(m.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Tt&&uu(S)===m.type)?(w=i(m,h.props),w.ref=jr(v,m,h),w.return=v,w):(w=cs(h.type,h.key,h.props,null,v.mode,w),w.ref=jr(v,m,h),w.return=v,w)}function c(v,m,h,w){return m===null||m.tag!==4||m.stateNode.containerInfo!==h.containerInfo||m.stateNode.implementation!==h.implementation?(m=Ga(h,v.mode,w),m.return=v,m):(m=i(m,h.children||[]),m.return=v,m)}function u(v,m,h,w,S){return m===null||m.tag!==7?(m=hn(h,v.mode,w,S),m.return=v,m):(m=i(m,h),m.return=v,m)}function d(v,m,h){if(typeof m=="string"&&m!==""||typeof m=="number")return m=Qa(""+m,v.mode,h),m.return=v,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case Ti:return h=cs(m.type,m.key,m.props,null,v.mode,h),h.ref=jr(v,null,m),h.return=v,h;case $n:return m=Ga(m,v.mode,h),m.return=v,m;case Tt:var w=m._init;return d(v,w(m._payload),h)}if(Or(m)||wr(m))return m=hn(m,v.mode,h,null),m.return=v,m;Fi(v,m)}return null}function f(v,m,h,w){var S=m!==null?m.key:null;if(typeof h=="string"&&h!==""||typeof h=="number")return S!==null?null:o(v,m,""+h,w);if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Ti:return h.key===S?l(v,m,h,w):null;case $n:return h.key===S?c(v,m,h,w):null;case Tt:return S=h._init,f(v,m,S(h._payload),w)}if(Or(h)||wr(h))return S!==null?null:u(v,m,h,w,null);Fi(v,h)}return null}function g(v,m,h,w,S){if(typeof w=="string"&&w!==""||typeof w=="number")return v=v.get(h)||null,o(m,v,""+w,S);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case Ti:return v=v.get(w.key===null?h:w.key)||null,l(m,v,w,S);case $n:return v=v.get(w.key===null?h:w.key)||null,c(m,v,w,S);case Tt:var b=w._init;return g(v,m,h,b(w._payload),S)}if(Or(w)||wr(w))return v=v.get(h)||null,u(m,v,w,S,null);Fi(m,w)}return null}function y(v,m,h,w){for(var S=null,b=null,E=m,C=m=0,N=null;E!==null&&C<h.length;C++){E.index>C?(N=E,E=null):N=E.sibling;var I=f(v,E,h[C],w);if(I===null){E===null&&(E=N);break}e&&E&&I.alternate===null&&t(v,E),m=s(I,m,C),b===null?S=I:b.sibling=I,b=I,E=N}if(C===h.length)return n(v,E),Q&&sn(v,C),S;if(E===null){for(;C<h.length;C++)E=d(v,h[C],w),E!==null&&(m=s(E,m,C),b===null?S=E:b.sibling=E,b=E);return Q&&sn(v,C),S}for(E=r(v,E);C<h.length;C++)N=g(E,v,C,h[C],w),N!==null&&(e&&N.alternate!==null&&E.delete(N.key===null?C:N.key),m=s(N,m,C),b===null?S=N:b.sibling=N,b=N);return e&&E.forEach(function(M){return t(v,M)}),Q&&sn(v,C),S}function x(v,m,h,w){var S=wr(h);if(typeof S!="function")throw Error(j(150));if(h=S.call(h),h==null)throw Error(j(151));for(var b=S=null,E=m,C=m=0,N=null,I=h.next();E!==null&&!I.done;C++,I=h.next()){E.index>C?(N=E,E=null):N=E.sibling;var M=f(v,E,I.value,w);if(M===null){E===null&&(E=N);break}e&&E&&M.alternate===null&&t(v,E),m=s(M,m,C),b===null?S=M:b.sibling=M,b=M,E=N}if(I.done)return n(v,E),Q&&sn(v,C),S;if(E===null){for(;!I.done;C++,I=h.next())I=d(v,I.value,w),I!==null&&(m=s(I,m,C),b===null?S=I:b.sibling=I,b=I);return Q&&sn(v,C),S}for(E=r(v,E);!I.done;C++,I=h.next())I=g(E,v,C,I.value,w),I!==null&&(e&&I.alternate!==null&&E.delete(I.key===null?C:I.key),m=s(I,m,C),b===null?S=I:b.sibling=I,b=I);return e&&E.forEach(function(le){return t(v,le)}),Q&&sn(v,C),S}function k(v,m,h,w){if(typeof h=="object"&&h!==null&&h.type===zn&&h.key===null&&(h=h.props.children),typeof h=="object"&&h!==null){switch(h.$$typeof){case Ti:e:{for(var S=h.key,b=m;b!==null;){if(b.key===S){if(S=h.type,S===zn){if(b.tag===7){n(v,b.sibling),m=i(b,h.props.children),m.return=v,v=m;break e}}else if(b.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Tt&&uu(S)===b.type){n(v,b.sibling),m=i(b,h.props),m.ref=jr(v,b,h),m.return=v,v=m;break e}n(v,b);break}else t(v,b);b=b.sibling}h.type===zn?(m=hn(h.props.children,v.mode,w,h.key),m.return=v,v=m):(w=cs(h.type,h.key,h.props,null,v.mode,w),w.ref=jr(v,m,h),w.return=v,v=w)}return a(v);case $n:e:{for(b=h.key;m!==null;){if(m.key===b)if(m.tag===4&&m.stateNode.containerInfo===h.containerInfo&&m.stateNode.implementation===h.implementation){n(v,m.sibling),m=i(m,h.children||[]),m.return=v,v=m;break e}else{n(v,m);break}else t(v,m);m=m.sibling}m=Ga(h,v.mode,w),m.return=v,v=m}return a(v);case Tt:return b=h._init,k(v,m,b(h._payload),w)}if(Or(h))return y(v,m,h,w);if(wr(h))return x(v,m,h,w);Fi(v,h)}return typeof h=="string"&&h!==""||typeof h=="number"?(h=""+h,m!==null&&m.tag===6?(n(v,m.sibling),m=i(m,h),m.return=v,v=m):(n(v,m),m=Qa(h,v.mode,w),m.return=v,v=m),a(v)):n(v,m)}return k}var rr=Rp(!0),Ap=Rp(!1),Ss=Yt(null),bs=null,Hn=null,Fl=null;function Ml(){Fl=Hn=bs=null}function Bl(e){var t=Ss.current;V(Ss),e._currentValue=t}function zo(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Xn(e,t){bs=e,Fl=Hn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Re=!0),e.firstContext=null)}function Ge(e){var t=e._currentValue;if(Fl!==e)if(e={context:e,memoizedValue:t,next:null},Hn===null){if(bs===null)throw Error(j(308));Hn=e,bs.dependencies={lanes:0,firstContext:e}}else Hn=Hn.next=e;return t}var dn=null;function Wl(e){dn===null?dn=[e]:dn.push(e)}function Dp(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,Wl(t)):(n.next=i.next,i.next=n),t.interleaved=n,kt(e,r)}function kt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Ot=!1;function ql(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function $p(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function xt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Wt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,U&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,kt(e,n)}return i=r.interleaved,i===null?(t.next=t,Wl(r)):(t.next=i.next,i.next=t),r.interleaved=t,kt(e,n)}function rs(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Tl(e,n)}}function du(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=a:s=s.next=a,n=n.next}while(n!==null);s===null?i=s=t:s=s.next=t}else i=s=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function js(e,t,n,r){var i=e.updateQueue;Ot=!1;var s=i.firstBaseUpdate,a=i.lastBaseUpdate,o=i.shared.pending;if(o!==null){i.shared.pending=null;var l=o,c=l.next;l.next=null,a===null?s=c:a.next=c,a=l;var u=e.alternate;u!==null&&(u=u.updateQueue,o=u.lastBaseUpdate,o!==a&&(o===null?u.firstBaseUpdate=c:o.next=c,u.lastBaseUpdate=l))}if(s!==null){var d=i.baseState;a=0,u=c=l=null,o=s;do{var f=o.lane,g=o.eventTime;if((r&f)===f){u!==null&&(u=u.next={eventTime:g,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var y=e,x=o;switch(f=t,g=n,x.tag){case 1:if(y=x.payload,typeof y=="function"){d=y.call(g,d,f);break e}d=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=x.payload,f=typeof y=="function"?y.call(g,d,f):y,f==null)break e;d=Z({},d,f);break e;case 2:Ot=!0}}o.callback!==null&&o.lane!==0&&(e.flags|=64,f=i.effects,f===null?i.effects=[o]:f.push(o))}else g={eventTime:g,lane:f,tag:o.tag,payload:o.payload,callback:o.callback,next:null},u===null?(c=u=g,l=d):u=u.next=g,a|=f;if(o=o.next,o===null){if(o=i.shared.pending,o===null)break;f=o,o=f.next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}while(1);if(u===null&&(l=d),i.baseState=l,i.firstBaseUpdate=c,i.lastBaseUpdate=u,t=i.shared.interleaved,t!==null){i=t;do a|=i.lane,i=i.next;while(i!==t)}else s===null&&(i.shared.lanes=0);yn|=a,e.lanes=a,e.memoizedState=d}}function pu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(j(191,i));i.call(r)}}}var xi={},pt=Yt(xi),ii=Yt(xi),si=Yt(xi);function pn(e){if(e===xi)throw Error(j(174));return e}function Hl(e,t){switch(q(si,t),q(ii,e),q(pt,xi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:go(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=go(t,e)}V(pt),q(pt,t)}function ir(){V(pt),V(ii),V(si)}function zp(e){pn(si.current);var t=pn(pt.current),n=go(t,e.type);t!==n&&(q(ii,e),q(pt,n))}function Vl(e){ii.current===e&&(V(pt),V(ii))}var Y=Yt(0);function Es(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ba=[];function Kl(){for(var e=0;e<Ba.length;e++)Ba[e]._workInProgressVersionPrimary=null;Ba.length=0}var is=bt.ReactCurrentDispatcher,Wa=bt.ReactCurrentBatchConfig,gn=0,X=null,ae=null,pe=null,Cs=!1,Ur=!1,ai=0,Av=0;function ye(){throw Error(j(321))}function Ql(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!st(e[n],t[n]))return!1;return!0}function Gl(e,t,n,r,i,s){if(gn=s,X=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,is.current=e===null||e.memoizedState===null?Lv:Uv,e=n(r,i),Ur){s=0;do{if(Ur=!1,ai=0,25<=s)throw Error(j(301));s+=1,pe=ae=null,t.updateQueue=null,is.current=Fv,e=n(r,i)}while(Ur)}if(is.current=Ps,t=ae!==null&&ae.next!==null,gn=0,pe=ae=X=null,Cs=!1,t)throw Error(j(300));return e}function Jl(){var e=ai!==0;return ai=0,e}function ct(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return pe===null?X.memoizedState=pe=e:pe=pe.next=e,pe}function Je(){if(ae===null){var e=X.alternate;e=e!==null?e.memoizedState:null}else e=ae.next;var t=pe===null?X.memoizedState:pe.next;if(t!==null)pe=t,ae=e;else{if(e===null)throw Error(j(310));ae=e,e={memoizedState:ae.memoizedState,baseState:ae.baseState,baseQueue:ae.baseQueue,queue:ae.queue,next:null},pe===null?X.memoizedState=pe=e:pe=pe.next=e}return pe}function oi(e,t){return typeof t=="function"?t(e):t}function qa(e){var t=Je(),n=t.queue;if(n===null)throw Error(j(311));n.lastRenderedReducer=e;var r=ae,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var a=i.next;i.next=s.next,s.next=a}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var o=a=null,l=null,c=s;do{var u=c.lane;if((gn&u)===u)l!==null&&(l=l.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var d={lane:u,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};l===null?(o=l=d,a=r):l=l.next=d,X.lanes|=u,yn|=u}c=c.next}while(c!==null&&c!==s);l===null?a=r:l.next=o,st(r,t.memoizedState)||(Re=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do s=i.lane,X.lanes|=s,yn|=s,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ha(e){var t=Je(),n=t.queue;if(n===null)throw Error(j(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,s=t.memoizedState;if(i!==null){n.pending=null;var a=i=i.next;do s=e(s,a.action),a=a.next;while(a!==i);st(s,t.memoizedState)||(Re=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function Lp(){}function Up(e,t){var n=X,r=Je(),i=t(),s=!st(r.memoizedState,i);if(s&&(r.memoizedState=i,Re=!0),r=r.queue,Yl(Bp.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||pe!==null&&pe.memoizedState.tag&1){if(n.flags|=2048,li(9,Mp.bind(null,n,r,i,t),void 0,null),fe===null)throw Error(j(349));gn&30||Fp(n,t,i)}return i}function Fp(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=X.updateQueue,t===null?(t={lastEffect:null,stores:null},X.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Mp(e,t,n,r){t.value=n,t.getSnapshot=r,Wp(t)&&qp(e)}function Bp(e,t,n){return n(function(){Wp(t)&&qp(e)})}function Wp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!st(e,n)}catch{return!0}}function qp(e){var t=kt(e,1);t!==null&&it(t,e,1,-1)}function fu(e){var t=ct();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:oi,lastRenderedState:e},t.queue=e,e=e.dispatch=zv.bind(null,X,e),[t.memoizedState,e]}function li(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=X.updateQueue,t===null?(t={lastEffect:null,stores:null},X.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Hp(){return Je().memoizedState}function ss(e,t,n,r){var i=ct();X.flags|=e,i.memoizedState=li(1|t,n,void 0,r===void 0?null:r)}function Gs(e,t,n,r){var i=Je();r=r===void 0?null:r;var s=void 0;if(ae!==null){var a=ae.memoizedState;if(s=a.destroy,r!==null&&Ql(r,a.deps)){i.memoizedState=li(t,n,s,r);return}}X.flags|=e,i.memoizedState=li(1|t,n,s,r)}function hu(e,t){return ss(8390656,8,e,t)}function Yl(e,t){return Gs(2048,8,e,t)}function Vp(e,t){return Gs(4,2,e,t)}function Kp(e,t){return Gs(4,4,e,t)}function Qp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Gp(e,t,n){return n=n!=null?n.concat([e]):null,Gs(4,4,Qp.bind(null,t,e),n)}function Xl(){}function Jp(e,t){var n=Je();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ql(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Yp(e,t){var n=Je();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ql(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Xp(e,t,n){return gn&21?(st(n,t)||(n=rp(),X.lanes|=n,yn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Re=!0),e.memoizedState=n)}function Dv(e,t){var n=F;F=n!==0&&4>n?n:4,e(!0);var r=Wa.transition;Wa.transition={};try{e(!1),t()}finally{F=n,Wa.transition=r}}function Zp(){return Je().memoizedState}function $v(e,t,n){var r=Ht(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},ef(e))tf(t,n);else if(n=Dp(e,t,n,r),n!==null){var i=je();it(n,e,r,i),nf(n,t,r)}}function zv(e,t,n){var r=Ht(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(ef(e))tf(t,i);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var a=t.lastRenderedState,o=s(a,n);if(i.hasEagerState=!0,i.eagerState=o,st(o,a)){var l=t.interleaved;l===null?(i.next=i,Wl(t)):(i.next=l.next,l.next=i),t.interleaved=i;return}}catch{}finally{}n=Dp(e,t,i,r),n!==null&&(i=je(),it(n,e,r,i),nf(n,t,r))}}function ef(e){var t=e.alternate;return e===X||t!==null&&t===X}function tf(e,t){Ur=Cs=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function nf(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Tl(e,n)}}var Ps={readContext:Ge,useCallback:ye,useContext:ye,useEffect:ye,useImperativeHandle:ye,useInsertionEffect:ye,useLayoutEffect:ye,useMemo:ye,useReducer:ye,useRef:ye,useState:ye,useDebugValue:ye,useDeferredValue:ye,useTransition:ye,useMutableSource:ye,useSyncExternalStore:ye,useId:ye,unstable_isNewReconciler:!1},Lv={readContext:Ge,useCallback:function(e,t){return ct().memoizedState=[e,t===void 0?null:t],e},useContext:Ge,useEffect:hu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ss(4194308,4,Qp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ss(4194308,4,e,t)},useInsertionEffect:function(e,t){return ss(4,2,e,t)},useMemo:function(e,t){var n=ct();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=ct();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=$v.bind(null,X,e),[r.memoizedState,e]},useRef:function(e){var t=ct();return e={current:e},t.memoizedState=e},useState:fu,useDebugValue:Xl,useDeferredValue:function(e){return ct().memoizedState=e},useTransition:function(){var e=fu(!1),t=e[0];return e=Dv.bind(null,e[1]),ct().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=X,i=ct();if(Q){if(n===void 0)throw Error(j(407));n=n()}else{if(n=t(),fe===null)throw Error(j(349));gn&30||Fp(r,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,hu(Bp.bind(null,r,s,e),[e]),r.flags|=2048,li(9,Mp.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=ct(),t=fe.identifierPrefix;if(Q){var n=yt,r=gt;n=(r&~(1<<32-rt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=ai++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Av++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Uv={readContext:Ge,useCallback:Jp,useContext:Ge,useEffect:Yl,useImperativeHandle:Gp,useInsertionEffect:Vp,useLayoutEffect:Kp,useMemo:Yp,useReducer:qa,useRef:Hp,useState:function(){return qa(oi)},useDebugValue:Xl,useDeferredValue:function(e){var t=Je();return Xp(t,ae.memoizedState,e)},useTransition:function(){var e=qa(oi)[0],t=Je().memoizedState;return[e,t]},useMutableSource:Lp,useSyncExternalStore:Up,useId:Zp,unstable_isNewReconciler:!1},Fv={readContext:Ge,useCallback:Jp,useContext:Ge,useEffect:Yl,useImperativeHandle:Gp,useInsertionEffect:Vp,useLayoutEffect:Kp,useMemo:Yp,useReducer:Ha,useRef:Hp,useState:function(){return Ha(oi)},useDebugValue:Xl,useDeferredValue:function(e){var t=Je();return ae===null?t.memoizedState=e:Xp(t,ae.memoizedState,e)},useTransition:function(){var e=Ha(oi)[0],t=Je().memoizedState;return[e,t]},useMutableSource:Lp,useSyncExternalStore:Up,useId:Zp,unstable_isNewReconciler:!1};function Ze(e,t){if(e&&e.defaultProps){t=Z({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Lo(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Z({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Js={isMounted:function(e){return(e=e._reactInternals)?_n(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=je(),i=Ht(e),s=xt(r,i);s.payload=t,n!=null&&(s.callback=n),t=Wt(e,s,i),t!==null&&(it(t,e,i,r),rs(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=je(),i=Ht(e),s=xt(r,i);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=Wt(e,s,i),t!==null&&(it(t,e,i,r),rs(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=je(),r=Ht(e),i=xt(n,r);i.tag=2,t!=null&&(i.callback=t),t=Wt(e,i,r),t!==null&&(it(t,e,r,n),rs(t,e,r))}};function mu(e,t,n,r,i,s,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,a):t.prototype&&t.prototype.isPureReactComponent?!ei(n,r)||!ei(i,s):!0}function rf(e,t,n){var r=!1,i=Gt,s=t.contextType;return typeof s=="object"&&s!==null?s=Ge(s):(i=De(t)?mn:ke.current,r=t.contextTypes,s=(r=r!=null)?tr(e,i):Gt),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Js,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=s),t}function vu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Js.enqueueReplaceState(t,t.state,null)}function Uo(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},ql(e);var s=t.contextType;typeof s=="object"&&s!==null?i.context=Ge(s):(s=De(t)?mn:ke.current,i.context=tr(e,s)),i.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(Lo(e,t,s,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Js.enqueueReplaceState(i,i.state,null),js(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function sr(e,t){try{var n="",r=t;do n+=hm(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:i,digest:null}}function Va(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Fo(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Mv=typeof WeakMap=="function"?WeakMap:Map;function sf(e,t,n){n=xt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Os||(Os=!0,Jo=r),Fo(e,t)},n}function af(e,t,n){n=xt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Fo(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){Fo(e,t),typeof r!="function"&&(qt===null?qt=new Set([this]):qt.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),n}function gu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Mv;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=tg.bind(null,e,t,n),t.then(e,e))}function yu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function xu(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=xt(-1,1),t.tag=2,Wt(n,t,1))),n.lanes|=1),e)}var Bv=bt.ReactCurrentOwner,Re=!1;function be(e,t,n,r){t.child=e===null?Ap(t,null,n,r):rr(t,e.child,n,r)}function wu(e,t,n,r,i){n=n.render;var s=t.ref;return Xn(t,i),r=Gl(e,t,n,r,s,i),n=Jl(),e!==null&&!Re?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,St(e,t,i)):(Q&&n&&zl(t),t.flags|=1,be(e,t,r,i),t.child)}function _u(e,t,n,r,i){if(e===null){var s=n.type;return typeof s=="function"&&!ac(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,of(e,t,s,r,i)):(e=cs(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&i)){var a=s.memoizedProps;if(n=n.compare,n=n!==null?n:ei,n(a,r)&&e.ref===t.ref)return St(e,t,i)}return t.flags|=1,e=Vt(s,r),e.ref=t.ref,e.return=t,t.child=e}function of(e,t,n,r,i){if(e!==null){var s=e.memoizedProps;if(ei(s,r)&&e.ref===t.ref)if(Re=!1,t.pendingProps=r=s,(e.lanes&i)!==0)e.flags&131072&&(Re=!0);else return t.lanes=e.lanes,St(e,t,i)}return Mo(e,t,n,r,i)}function lf(e,t,n){var r=t.pendingProps,i=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},q(Kn,Le),Le|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,q(Kn,Le),Le|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,q(Kn,Le),Le|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,q(Kn,Le),Le|=r;return be(e,t,i,n),t.child}function cf(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Mo(e,t,n,r,i){var s=De(n)?mn:ke.current;return s=tr(t,s),Xn(t,i),n=Gl(e,t,n,r,s,i),r=Jl(),e!==null&&!Re?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,St(e,t,i)):(Q&&r&&zl(t),t.flags|=1,be(e,t,n,i),t.child)}function ku(e,t,n,r,i){if(De(n)){var s=!0;ws(t)}else s=!1;if(Xn(t,i),t.stateNode===null)as(e,t),rf(t,n,r),Uo(t,n,r,i),r=!0;else if(e===null){var a=t.stateNode,o=t.memoizedProps;a.props=o;var l=a.context,c=n.contextType;typeof c=="object"&&c!==null?c=Ge(c):(c=De(n)?mn:ke.current,c=tr(t,c));var u=n.getDerivedStateFromProps,d=typeof u=="function"||typeof a.getSnapshotBeforeUpdate=="function";d||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(o!==r||l!==c)&&vu(t,a,r,c),Ot=!1;var f=t.memoizedState;a.state=f,js(t,r,a,i),l=t.memoizedState,o!==r||f!==l||Ae.current||Ot?(typeof u=="function"&&(Lo(t,n,u,r),l=t.memoizedState),(o=Ot||mu(t,n,o,r,f,l,c))?(d||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),a.props=r,a.state=l,a.context=c,r=o):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,$p(e,t),o=t.memoizedProps,c=t.type===t.elementType?o:Ze(t.type,o),a.props=c,d=t.pendingProps,f=a.context,l=n.contextType,typeof l=="object"&&l!==null?l=Ge(l):(l=De(n)?mn:ke.current,l=tr(t,l));var g=n.getDerivedStateFromProps;(u=typeof g=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(o!==d||f!==l)&&vu(t,a,r,l),Ot=!1,f=t.memoizedState,a.state=f,js(t,r,a,i);var y=t.memoizedState;o!==d||f!==y||Ae.current||Ot?(typeof g=="function"&&(Lo(t,n,g,r),y=t.memoizedState),(c=Ot||mu(t,n,c,r,f,y,l)||!1)?(u||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,y,l),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,y,l)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),a.props=r,a.state=y,a.context=l,r=c):(typeof a.componentDidUpdate!="function"||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Bo(e,t,n,r,s,i)}function Bo(e,t,n,r,i,s){cf(e,t);var a=(t.flags&128)!==0;if(!r&&!a)return i&&ou(t,n,!1),St(e,t,s);r=t.stateNode,Bv.current=t;var o=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&a?(t.child=rr(t,e.child,null,s),t.child=rr(t,null,o,s)):be(e,t,o,s),t.memoizedState=r.state,i&&ou(t,n,!0),t.child}function uf(e){var t=e.stateNode;t.pendingContext?au(e,t.pendingContext,t.pendingContext!==t.context):t.context&&au(e,t.context,!1),Hl(e,t.containerInfo)}function Su(e,t,n,r,i){return nr(),Ul(i),t.flags|=256,be(e,t,n,r),t.child}var Wo={dehydrated:null,treeContext:null,retryLane:0};function qo(e){return{baseLanes:e,cachePool:null,transitions:null}}function df(e,t,n){var r=t.pendingProps,i=Y.current,s=!1,a=(t.flags&128)!==0,o;if((o=a)||(o=e!==null&&e.memoizedState===null?!1:(i&2)!==0),o?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),q(Y,i&1),e===null)return $o(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(a=r.children,e=r.fallback,s?(r=t.mode,s=t.child,a={mode:"hidden",children:a},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=a):s=Zs(a,r,0,null),e=hn(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=qo(n),t.memoizedState=Wo,e):Zl(t,a));if(i=e.memoizedState,i!==null&&(o=i.dehydrated,o!==null))return Wv(e,t,a,r,o,i,n);if(s){s=r.fallback,a=t.mode,i=e.child,o=i.sibling;var l={mode:"hidden",children:r.children};return!(a&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Vt(i,l),r.subtreeFlags=i.subtreeFlags&14680064),o!==null?s=Vt(o,s):(s=hn(s,a,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,a=e.child.memoizedState,a=a===null?qo(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},s.memoizedState=a,s.childLanes=e.childLanes&~n,t.memoizedState=Wo,r}return s=e.child,e=s.sibling,r=Vt(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Zl(e,t){return t=Zs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Mi(e,t,n,r){return r!==null&&Ul(r),rr(t,e.child,null,n),e=Zl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Wv(e,t,n,r,i,s,a){if(n)return t.flags&256?(t.flags&=-257,r=Va(Error(j(422))),Mi(e,t,a,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,i=t.mode,r=Zs({mode:"visible",children:r.children},i,0,null),s=hn(s,i,a,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&rr(t,e.child,null,a),t.child.memoizedState=qo(a),t.memoizedState=Wo,s);if(!(t.mode&1))return Mi(e,t,a,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var o=r.dgst;return r=o,s=Error(j(419)),r=Va(s,r,void 0),Mi(e,t,a,r)}if(o=(a&e.childLanes)!==0,Re||o){if(r=fe,r!==null){switch(a&-a){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|a)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,kt(e,i),it(r,e,i,-1))}return sc(),r=Va(Error(j(421))),Mi(e,t,a,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=ng.bind(null,e),i._reactRetry=t,null):(e=s.treeContext,Ue=Bt(i.nextSibling),Fe=t,Q=!0,nt=null,e!==null&&(qe[He++]=gt,qe[He++]=yt,qe[He++]=vn,gt=e.id,yt=e.overflow,vn=t),t=Zl(t,r.children),t.flags|=4096,t)}function bu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),zo(e.return,t,n)}function Ka(e,t,n,r,i){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function pf(e,t,n){var r=t.pendingProps,i=r.revealOrder,s=r.tail;if(be(e,t,r.children,n),r=Y.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&bu(e,n,t);else if(e.tag===19)bu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(q(Y,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Es(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Ka(t,!1,i,n,s);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Es(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Ka(t,!0,n,null,s);break;case"together":Ka(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function as(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function St(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),yn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(j(153));if(t.child!==null){for(e=t.child,n=Vt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Vt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function qv(e,t,n){switch(t.tag){case 3:uf(t),nr();break;case 5:zp(t);break;case 1:De(t.type)&&ws(t);break;case 4:Hl(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;q(Ss,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(q(Y,Y.current&1),t.flags|=128,null):n&t.child.childLanes?df(e,t,n):(q(Y,Y.current&1),e=St(e,t,n),e!==null?e.sibling:null);q(Y,Y.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return pf(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),q(Y,Y.current),r)break;return null;case 22:case 23:return t.lanes=0,lf(e,t,n)}return St(e,t,n)}var ff,Ho,hf,mf;ff=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Ho=function(){};hf=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,pn(pt.current);var s=null;switch(n){case"input":i=fo(e,i),r=fo(e,r),s=[];break;case"select":i=Z({},i,{value:void 0}),r=Z({},r,{value:void 0}),s=[];break;case"textarea":i=vo(e,i),r=vo(e,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ys)}yo(n,r);var a;n=null;for(c in i)if(!r.hasOwnProperty(c)&&i.hasOwnProperty(c)&&i[c]!=null)if(c==="style"){var o=i[c];for(a in o)o.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Kr.hasOwnProperty(c)?s||(s=[]):(s=s||[]).push(c,null));for(c in r){var l=r[c];if(o=i!=null?i[c]:void 0,r.hasOwnProperty(c)&&l!==o&&(l!=null||o!=null))if(c==="style")if(o){for(a in o)!o.hasOwnProperty(a)||l&&l.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in l)l.hasOwnProperty(a)&&o[a]!==l[a]&&(n||(n={}),n[a]=l[a])}else n||(s||(s=[]),s.push(c,n)),n=l;else c==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,o=o?o.__html:void 0,l!=null&&o!==l&&(s=s||[]).push(c,l)):c==="children"?typeof l!="string"&&typeof l!="number"||(s=s||[]).push(c,""+l):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Kr.hasOwnProperty(c)?(l!=null&&c==="onScroll"&&H("scroll",e),s||o===l||(s=[])):(s=s||[]).push(c,l))}n&&(s=s||[]).push("style",n);var c=s;(t.updateQueue=c)&&(t.flags|=4)}};mf=function(e,t,n,r){n!==r&&(t.flags|=4)};function Er(e,t){if(!Q)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function xe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Hv(e,t,n){var r=t.pendingProps;switch(Ll(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return xe(t),null;case 1:return De(t.type)&&xs(),xe(t),null;case 3:return r=t.stateNode,ir(),V(Ae),V(ke),Kl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ui(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,nt!==null&&(Zo(nt),nt=null))),Ho(e,t),xe(t),null;case 5:Vl(t);var i=pn(si.current);if(n=t.type,e!==null&&t.stateNode!=null)hf(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(j(166));return xe(t),null}if(e=pn(pt.current),Ui(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[ut]=t,r[ri]=s,e=(t.mode&1)!==0,n){case"dialog":H("cancel",r),H("close",r);break;case"iframe":case"object":case"embed":H("load",r);break;case"video":case"audio":for(i=0;i<Ir.length;i++)H(Ir[i],r);break;case"source":H("error",r);break;case"img":case"image":case"link":H("error",r),H("load",r);break;case"details":H("toggle",r);break;case"input":Rc(r,s),H("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},H("invalid",r);break;case"textarea":Dc(r,s),H("invalid",r)}yo(n,s),i=null;for(var a in s)if(s.hasOwnProperty(a)){var o=s[a];a==="children"?typeof o=="string"?r.textContent!==o&&(s.suppressHydrationWarning!==!0&&Li(r.textContent,o,e),i=["children",o]):typeof o=="number"&&r.textContent!==""+o&&(s.suppressHydrationWarning!==!0&&Li(r.textContent,o,e),i=["children",""+o]):Kr.hasOwnProperty(a)&&o!=null&&a==="onScroll"&&H("scroll",r)}switch(n){case"input":Oi(r),Ac(r,s,!0);break;case"textarea":Oi(r),$c(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=ys)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{a=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Bd(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=a.createElement(n,{is:r.is}):(e=a.createElement(n),n==="select"&&(a=e,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):e=a.createElementNS(e,n),e[ut]=t,e[ri]=r,ff(e,t,!1,!1),t.stateNode=e;e:{switch(a=xo(n,r),n){case"dialog":H("cancel",e),H("close",e),i=r;break;case"iframe":case"object":case"embed":H("load",e),i=r;break;case"video":case"audio":for(i=0;i<Ir.length;i++)H(Ir[i],e);i=r;break;case"source":H("error",e),i=r;break;case"img":case"image":case"link":H("error",e),H("load",e),i=r;break;case"details":H("toggle",e),i=r;break;case"input":Rc(e,r),i=fo(e,r),H("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=Z({},r,{value:void 0}),H("invalid",e);break;case"textarea":Dc(e,r),i=vo(e,r),H("invalid",e);break;default:i=r}yo(n,i),o=i;for(s in o)if(o.hasOwnProperty(s)){var l=o[s];s==="style"?Hd(e,l):s==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Wd(e,l)):s==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Qr(e,l):typeof l=="number"&&Qr(e,""+l):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Kr.hasOwnProperty(s)?l!=null&&s==="onScroll"&&H("scroll",e):l!=null&&Sl(e,s,l,a))}switch(n){case"input":Oi(e),Ac(e,r,!1);break;case"textarea":Oi(e),$c(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Qt(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?Qn(e,!!r.multiple,s,!1):r.defaultValue!=null&&Qn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=ys)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return xe(t),null;case 6:if(e&&t.stateNode!=null)mf(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(j(166));if(n=pn(si.current),pn(pt.current),Ui(t)){if(r=t.stateNode,n=t.memoizedProps,r[ut]=t,(s=r.nodeValue!==n)&&(e=Fe,e!==null))switch(e.tag){case 3:Li(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Li(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[ut]=t,t.stateNode=r}return xe(t),null;case 13:if(V(Y),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Q&&Ue!==null&&t.mode&1&&!(t.flags&128))Ip(),nr(),t.flags|=98560,s=!1;else if(s=Ui(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(j(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(j(317));s[ut]=t}else nr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;xe(t),s=!1}else nt!==null&&(Zo(nt),nt=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Y.current&1?oe===0&&(oe=3):sc())),t.updateQueue!==null&&(t.flags|=4),xe(t),null);case 4:return ir(),Ho(e,t),e===null&&ti(t.stateNode.containerInfo),xe(t),null;case 10:return Bl(t.type._context),xe(t),null;case 17:return De(t.type)&&xs(),xe(t),null;case 19:if(V(Y),s=t.memoizedState,s===null)return xe(t),null;if(r=(t.flags&128)!==0,a=s.rendering,a===null)if(r)Er(s,!1);else{if(oe!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(a=Es(e),a!==null){for(t.flags|=128,Er(s,!1),r=a.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,a=s.alternate,a===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=a.childLanes,s.lanes=a.lanes,s.child=a.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=a.memoizedProps,s.memoizedState=a.memoizedState,s.updateQueue=a.updateQueue,s.type=a.type,e=a.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return q(Y,Y.current&1|2),t.child}e=e.sibling}s.tail!==null&&ne()>ar&&(t.flags|=128,r=!0,Er(s,!1),t.lanes=4194304)}else{if(!r)if(e=Es(a),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Er(s,!0),s.tail===null&&s.tailMode==="hidden"&&!a.alternate&&!Q)return xe(t),null}else 2*ne()-s.renderingStartTime>ar&&n!==1073741824&&(t.flags|=128,r=!0,Er(s,!1),t.lanes=4194304);s.isBackwards?(a.sibling=t.child,t.child=a):(n=s.last,n!==null?n.sibling=a:t.child=a,s.last=a)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=ne(),t.sibling=null,n=Y.current,q(Y,r?n&1|2:n&1),t):(xe(t),null);case 22:case 23:return ic(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Le&1073741824&&(xe(t),t.subtreeFlags&6&&(t.flags|=8192)):xe(t),null;case 24:return null;case 25:return null}throw Error(j(156,t.tag))}function Vv(e,t){switch(Ll(t),t.tag){case 1:return De(t.type)&&xs(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return ir(),V(Ae),V(ke),Kl(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Vl(t),null;case 13:if(V(Y),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(j(340));nr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return V(Y),null;case 4:return ir(),null;case 10:return Bl(t.type._context),null;case 22:case 23:return ic(),null;case 24:return null;default:return null}}var Bi=!1,_e=!1,Kv=typeof WeakSet=="function"?WeakSet:Set,O=null;function Vn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ee(e,t,r)}else n.current=null}function Vo(e,t,n){try{n()}catch(r){ee(e,t,r)}}var ju=!1;function Qv(e,t){if(To=ms,e=xp(),$l(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var a=0,o=-1,l=-1,c=0,u=0,d=e,f=null;t:for(;;){for(var g;d!==n||i!==0&&d.nodeType!==3||(o=a+i),d!==s||r!==0&&d.nodeType!==3||(l=a+r),d.nodeType===3&&(a+=d.nodeValue.length),(g=d.firstChild)!==null;)f=d,d=g;for(;;){if(d===e)break t;if(f===n&&++c===i&&(o=a),f===s&&++u===r&&(l=a),(g=d.nextSibling)!==null)break;d=f,f=d.parentNode}d=g}n=o===-1||l===-1?null:{start:o,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Oo={focusedElem:e,selectionRange:n},ms=!1,O=t;O!==null;)if(t=O,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,O=e;else for(;O!==null;){t=O;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var x=y.memoizedProps,k=y.memoizedState,v=t.stateNode,m=v.getSnapshotBeforeUpdate(t.elementType===t.type?x:Ze(t.type,x),k);v.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var h=t.stateNode.containerInfo;h.nodeType===1?h.textContent="":h.nodeType===9&&h.documentElement&&h.removeChild(h.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(j(163))}}catch(w){ee(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,O=e;break}O=t.return}return y=ju,ju=!1,y}function Fr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var s=i.destroy;i.destroy=void 0,s!==void 0&&Vo(t,n,s)}i=i.next}while(i!==r)}}function Ys(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Ko(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function vf(e){var t=e.alternate;t!==null&&(e.alternate=null,vf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[ut],delete t[ri],delete t[Ro],delete t[Ov],delete t[Nv])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function gf(e){return e.tag===5||e.tag===3||e.tag===4}function Eu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||gf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Qo(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ys));else if(r!==4&&(e=e.child,e!==null))for(Qo(e,t,n),e=e.sibling;e!==null;)Qo(e,t,n),e=e.sibling}function Go(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Go(e,t,n),e=e.sibling;e!==null;)Go(e,t,n),e=e.sibling}var me=null,et=!1;function Ct(e,t,n){for(n=n.child;n!==null;)yf(e,t,n),n=n.sibling}function yf(e,t,n){if(dt&&typeof dt.onCommitFiberUnmount=="function")try{dt.onCommitFiberUnmount(Ws,n)}catch{}switch(n.tag){case 5:_e||Vn(n,t);case 6:var r=me,i=et;me=null,Ct(e,t,n),me=r,et=i,me!==null&&(et?(e=me,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):me.removeChild(n.stateNode));break;case 18:me!==null&&(et?(e=me,n=n.stateNode,e.nodeType===8?Fa(e.parentNode,n):e.nodeType===1&&Fa(e,n),Xr(e)):Fa(me,n.stateNode));break;case 4:r=me,i=et,me=n.stateNode.containerInfo,et=!0,Ct(e,t,n),me=r,et=i;break;case 0:case 11:case 14:case 15:if(!_e&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,a=s.destroy;s=s.tag,a!==void 0&&(s&2||s&4)&&Vo(n,t,a),i=i.next}while(i!==r)}Ct(e,t,n);break;case 1:if(!_e&&(Vn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(o){ee(n,t,o)}Ct(e,t,n);break;case 21:Ct(e,t,n);break;case 22:n.mode&1?(_e=(r=_e)||n.memoizedState!==null,Ct(e,t,n),_e=r):Ct(e,t,n);break;default:Ct(e,t,n)}}function Cu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Kv),t.forEach(function(r){var i=rg.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Ye(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=e,a=t,o=a;e:for(;o!==null;){switch(o.tag){case 5:me=o.stateNode,et=!1;break e;case 3:me=o.stateNode.containerInfo,et=!0;break e;case 4:me=o.stateNode.containerInfo,et=!0;break e}o=o.return}if(me===null)throw Error(j(160));yf(s,a,i),me=null,et=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(c){ee(i,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)xf(t,e),t=t.sibling}function xf(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ye(t,e),at(e),r&4){try{Fr(3,e,e.return),Ys(3,e)}catch(x){ee(e,e.return,x)}try{Fr(5,e,e.return)}catch(x){ee(e,e.return,x)}}break;case 1:Ye(t,e),at(e),r&512&&n!==null&&Vn(n,n.return);break;case 5:if(Ye(t,e),at(e),r&512&&n!==null&&Vn(n,n.return),e.flags&32){var i=e.stateNode;try{Qr(i,"")}catch(x){ee(e,e.return,x)}}if(r&4&&(i=e.stateNode,i!=null)){var s=e.memoizedProps,a=n!==null?n.memoizedProps:s,o=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{o==="input"&&s.type==="radio"&&s.name!=null&&Fd(i,s),xo(o,a);var c=xo(o,s);for(a=0;a<l.length;a+=2){var u=l[a],d=l[a+1];u==="style"?Hd(i,d):u==="dangerouslySetInnerHTML"?Wd(i,d):u==="children"?Qr(i,d):Sl(i,u,d,c)}switch(o){case"input":ho(i,s);break;case"textarea":Md(i,s);break;case"select":var f=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var g=s.value;g!=null?Qn(i,!!s.multiple,g,!1):f!==!!s.multiple&&(s.defaultValue!=null?Qn(i,!!s.multiple,s.defaultValue,!0):Qn(i,!!s.multiple,s.multiple?[]:"",!1))}i[ri]=s}catch(x){ee(e,e.return,x)}}break;case 6:if(Ye(t,e),at(e),r&4){if(e.stateNode===null)throw Error(j(162));i=e.stateNode,s=e.memoizedProps;try{i.nodeValue=s}catch(x){ee(e,e.return,x)}}break;case 3:if(Ye(t,e),at(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Xr(t.containerInfo)}catch(x){ee(e,e.return,x)}break;case 4:Ye(t,e),at(e);break;case 13:Ye(t,e),at(e),i=e.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(nc=ne())),r&4&&Cu(e);break;case 22:if(u=n!==null&&n.memoizedState!==null,e.mode&1?(_e=(c=_e)||u,Ye(t,e),_e=c):Ye(t,e),at(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!u&&e.mode&1)for(O=e,u=e.child;u!==null;){for(d=O=u;O!==null;){switch(f=O,g=f.child,f.tag){case 0:case 11:case 14:case 15:Fr(4,f,f.return);break;case 1:Vn(f,f.return);var y=f.stateNode;if(typeof y.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(x){ee(r,n,x)}}break;case 5:Vn(f,f.return);break;case 22:if(f.memoizedState!==null){Tu(d);continue}}g!==null?(g.return=f,O=g):Tu(d)}u=u.sibling}e:for(u=null,d=e;;){if(d.tag===5){if(u===null){u=d;try{i=d.stateNode,c?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(o=d.stateNode,l=d.memoizedProps.style,a=l!=null&&l.hasOwnProperty("display")?l.display:null,o.style.display=qd("display",a))}catch(x){ee(e,e.return,x)}}}else if(d.tag===6){if(u===null)try{d.stateNode.nodeValue=c?"":d.memoizedProps}catch(x){ee(e,e.return,x)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;u===d&&(u=null),d=d.return}u===d&&(u=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:Ye(t,e),at(e),r&4&&Cu(e);break;case 21:break;default:Ye(t,e),at(e)}}function at(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(gf(n)){var r=n;break e}n=n.return}throw Error(j(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Qr(i,""),r.flags&=-33);var s=Eu(e);Go(e,s,i);break;case 3:case 4:var a=r.stateNode.containerInfo,o=Eu(e);Qo(e,o,a);break;default:throw Error(j(161))}}catch(l){ee(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Gv(e,t,n){O=e,wf(e)}function wf(e,t,n){for(var r=(e.mode&1)!==0;O!==null;){var i=O,s=i.child;if(i.tag===22&&r){var a=i.memoizedState!==null||Bi;if(!a){var o=i.alternate,l=o!==null&&o.memoizedState!==null||_e;o=Bi;var c=_e;if(Bi=a,(_e=l)&&!c)for(O=i;O!==null;)a=O,l=a.child,a.tag===22&&a.memoizedState!==null?Ou(i):l!==null?(l.return=a,O=l):Ou(i);for(;s!==null;)O=s,wf(s),s=s.sibling;O=i,Bi=o,_e=c}Pu(e)}else i.subtreeFlags&8772&&s!==null?(s.return=i,O=s):Pu(e)}}function Pu(e){for(;O!==null;){var t=O;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:_e||Ys(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!_e)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Ze(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&pu(t,s,r);break;case 3:var a=t.updateQueue;if(a!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}pu(t,a,n)}break;case 5:var o=t.stateNode;if(n===null&&t.flags&4){n=o;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var u=c.memoizedState;if(u!==null){var d=u.dehydrated;d!==null&&Xr(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(j(163))}_e||t.flags&512&&Ko(t)}catch(f){ee(t,t.return,f)}}if(t===e){O=null;break}if(n=t.sibling,n!==null){n.return=t.return,O=n;break}O=t.return}}function Tu(e){for(;O!==null;){var t=O;if(t===e){O=null;break}var n=t.sibling;if(n!==null){n.return=t.return,O=n;break}O=t.return}}function Ou(e){for(;O!==null;){var t=O;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ys(4,t)}catch(l){ee(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(l){ee(t,i,l)}}var s=t.return;try{Ko(t)}catch(l){ee(t,s,l)}break;case 5:var a=t.return;try{Ko(t)}catch(l){ee(t,a,l)}}}catch(l){ee(t,t.return,l)}if(t===e){O=null;break}var o=t.sibling;if(o!==null){o.return=t.return,O=o;break}O=t.return}}var Jv=Math.ceil,Ts=bt.ReactCurrentDispatcher,ec=bt.ReactCurrentOwner,Ke=bt.ReactCurrentBatchConfig,U=0,fe=null,se=null,ve=0,Le=0,Kn=Yt(0),oe=0,ci=null,yn=0,Xs=0,tc=0,Mr=null,Ne=null,nc=0,ar=1/0,ht=null,Os=!1,Jo=null,qt=null,Wi=!1,$t=null,Ns=0,Br=0,Yo=null,os=-1,ls=0;function je(){return U&6?ne():os!==-1?os:os=ne()}function Ht(e){return e.mode&1?U&2&&ve!==0?ve&-ve:Rv.transition!==null?(ls===0&&(ls=rp()),ls):(e=F,e!==0||(e=window.event,e=e===void 0?16:up(e.type)),e):1}function it(e,t,n,r){if(50<Br)throw Br=0,Yo=null,Error(j(185));vi(e,n,r),(!(U&2)||e!==fe)&&(e===fe&&(!(U&2)&&(Xs|=n),oe===4&&Rt(e,ve)),$e(e,r),n===1&&U===0&&!(t.mode&1)&&(ar=ne()+500,Qs&&Xt()))}function $e(e,t){var n=e.callbackNode;Rm(e,t);var r=hs(e,e===fe?ve:0);if(r===0)n!==null&&Uc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Uc(n),t===1)e.tag===0?Iv(Nu.bind(null,e)):Tp(Nu.bind(null,e)),Pv(function(){!(U&6)&&Xt()}),n=null;else{switch(ip(r)){case 1:n=Pl;break;case 4:n=tp;break;case 16:n=fs;break;case 536870912:n=np;break;default:n=fs}n=Pf(n,_f.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function _f(e,t){if(os=-1,ls=0,U&6)throw Error(j(327));var n=e.callbackNode;if(Zn()&&e.callbackNode!==n)return null;var r=hs(e,e===fe?ve:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Is(e,r);else{t=r;var i=U;U|=2;var s=Sf();(fe!==e||ve!==t)&&(ht=null,ar=ne()+500,fn(e,t));do try{Zv();break}catch(o){kf(e,o)}while(1);Ml(),Ts.current=s,U=i,se!==null?t=0:(fe=null,ve=0,t=oe)}if(t!==0){if(t===2&&(i=bo(e),i!==0&&(r=i,t=Xo(e,i))),t===1)throw n=ci,fn(e,0),Rt(e,r),$e(e,ne()),n;if(t===6)Rt(e,r);else{if(i=e.current.alternate,!(r&30)&&!Yv(i)&&(t=Is(e,r),t===2&&(s=bo(e),s!==0&&(r=s,t=Xo(e,s))),t===1))throw n=ci,fn(e,0),Rt(e,r),$e(e,ne()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(j(345));case 2:an(e,Ne,ht);break;case 3:if(Rt(e,r),(r&130023424)===r&&(t=nc+500-ne(),10<t)){if(hs(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){je(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Io(an.bind(null,e,Ne,ht),t);break}an(e,Ne,ht);break;case 4:if(Rt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var a=31-rt(r);s=1<<a,a=t[a],a>i&&(i=a),r&=~s}if(r=i,r=ne()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Jv(r/1960))-r,10<r){e.timeoutHandle=Io(an.bind(null,e,Ne,ht),r);break}an(e,Ne,ht);break;case 5:an(e,Ne,ht);break;default:throw Error(j(329))}}}return $e(e,ne()),e.callbackNode===n?_f.bind(null,e):null}function Xo(e,t){var n=Mr;return e.current.memoizedState.isDehydrated&&(fn(e,t).flags|=256),e=Is(e,t),e!==2&&(t=Ne,Ne=n,t!==null&&Zo(t)),e}function Zo(e){Ne===null?Ne=e:Ne.push.apply(Ne,e)}function Yv(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!st(s(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Rt(e,t){for(t&=~tc,t&=~Xs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-rt(t),r=1<<n;e[n]=-1,t&=~r}}function Nu(e){if(U&6)throw Error(j(327));Zn();var t=hs(e,0);if(!(t&1))return $e(e,ne()),null;var n=Is(e,t);if(e.tag!==0&&n===2){var r=bo(e);r!==0&&(t=r,n=Xo(e,r))}if(n===1)throw n=ci,fn(e,0),Rt(e,t),$e(e,ne()),n;if(n===6)throw Error(j(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,an(e,Ne,ht),$e(e,ne()),null}function rc(e,t){var n=U;U|=1;try{return e(t)}finally{U=n,U===0&&(ar=ne()+500,Qs&&Xt())}}function xn(e){$t!==null&&$t.tag===0&&!(U&6)&&Zn();var t=U;U|=1;var n=Ke.transition,r=F;try{if(Ke.transition=null,F=1,e)return e()}finally{F=r,Ke.transition=n,U=t,!(U&6)&&Xt()}}function ic(){Le=Kn.current,V(Kn)}function fn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Cv(n)),se!==null)for(n=se.return;n!==null;){var r=n;switch(Ll(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&xs();break;case 3:ir(),V(Ae),V(ke),Kl();break;case 5:Vl(r);break;case 4:ir();break;case 13:V(Y);break;case 19:V(Y);break;case 10:Bl(r.type._context);break;case 22:case 23:ic()}n=n.return}if(fe=e,se=e=Vt(e.current,null),ve=Le=t,oe=0,ci=null,tc=Xs=yn=0,Ne=Mr=null,dn!==null){for(t=0;t<dn.length;t++)if(n=dn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var a=s.next;s.next=i,r.next=a}n.pending=r}dn=null}return e}function kf(e,t){do{var n=se;try{if(Ml(),is.current=Ps,Cs){for(var r=X.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}Cs=!1}if(gn=0,pe=ae=X=null,Ur=!1,ai=0,ec.current=null,n===null||n.return===null){oe=1,ci=t,se=null;break}e:{var s=e,a=n.return,o=n,l=t;if(t=ve,o.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var c=l,u=o,d=u.tag;if(!(u.mode&1)&&(d===0||d===11||d===15)){var f=u.alternate;f?(u.updateQueue=f.updateQueue,u.memoizedState=f.memoizedState,u.lanes=f.lanes):(u.updateQueue=null,u.memoizedState=null)}var g=yu(a);if(g!==null){g.flags&=-257,xu(g,a,o,s,t),g.mode&1&&gu(s,c,t),t=g,l=c;var y=t.updateQueue;if(y===null){var x=new Set;x.add(l),t.updateQueue=x}else y.add(l);break e}else{if(!(t&1)){gu(s,c,t),sc();break e}l=Error(j(426))}}else if(Q&&o.mode&1){var k=yu(a);if(k!==null){!(k.flags&65536)&&(k.flags|=256),xu(k,a,o,s,t),Ul(sr(l,o));break e}}s=l=sr(l,o),oe!==4&&(oe=2),Mr===null?Mr=[s]:Mr.push(s),s=a;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var v=sf(s,l,t);du(s,v);break e;case 1:o=l;var m=s.type,h=s.stateNode;if(!(s.flags&128)&&(typeof m.getDerivedStateFromError=="function"||h!==null&&typeof h.componentDidCatch=="function"&&(qt===null||!qt.has(h)))){s.flags|=65536,t&=-t,s.lanes|=t;var w=af(s,o,t);du(s,w);break e}}s=s.return}while(s!==null)}jf(n)}catch(S){t=S,se===n&&n!==null&&(se=n=n.return);continue}break}while(1)}function Sf(){var e=Ts.current;return Ts.current=Ps,e===null?Ps:e}function sc(){(oe===0||oe===3||oe===2)&&(oe=4),fe===null||!(yn&268435455)&&!(Xs&268435455)||Rt(fe,ve)}function Is(e,t){var n=U;U|=2;var r=Sf();(fe!==e||ve!==t)&&(ht=null,fn(e,t));do try{Xv();break}catch(i){kf(e,i)}while(1);if(Ml(),U=n,Ts.current=r,se!==null)throw Error(j(261));return fe=null,ve=0,oe}function Xv(){for(;se!==null;)bf(se)}function Zv(){for(;se!==null&&!bm();)bf(se)}function bf(e){var t=Cf(e.alternate,e,Le);e.memoizedProps=e.pendingProps,t===null?jf(e):se=t,ec.current=null}function jf(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Vv(n,t),n!==null){n.flags&=32767,se=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{oe=6,se=null;return}}else if(n=Hv(n,t,Le),n!==null){se=n;return}if(t=t.sibling,t!==null){se=t;return}se=t=e}while(t!==null);oe===0&&(oe=5)}function an(e,t,n){var r=F,i=Ke.transition;try{Ke.transition=null,F=1,eg(e,t,n,r)}finally{Ke.transition=i,F=r}return null}function eg(e,t,n,r){do Zn();while($t!==null);if(U&6)throw Error(j(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(j(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(Am(e,s),e===fe&&(se=fe=null,ve=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Wi||(Wi=!0,Pf(fs,function(){return Zn(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=Ke.transition,Ke.transition=null;var a=F;F=1;var o=U;U|=4,ec.current=null,Qv(e,n),xf(n,e),wv(Oo),ms=!!To,Oo=To=null,e.current=n,Gv(n),jm(),U=o,F=a,Ke.transition=s}else e.current=n;if(Wi&&(Wi=!1,$t=e,Ns=i),s=e.pendingLanes,s===0&&(qt=null),Pm(n.stateNode),$e(e,ne()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Os)throw Os=!1,e=Jo,Jo=null,e;return Ns&1&&e.tag!==0&&Zn(),s=e.pendingLanes,s&1?e===Yo?Br++:(Br=0,Yo=e):Br=0,Xt(),null}function Zn(){if($t!==null){var e=ip(Ns),t=Ke.transition,n=F;try{if(Ke.transition=null,F=16>e?16:e,$t===null)var r=!1;else{if(e=$t,$t=null,Ns=0,U&6)throw Error(j(331));var i=U;for(U|=4,O=e.current;O!==null;){var s=O,a=s.child;if(O.flags&16){var o=s.deletions;if(o!==null){for(var l=0;l<o.length;l++){var c=o[l];for(O=c;O!==null;){var u=O;switch(u.tag){case 0:case 11:case 15:Fr(8,u,s)}var d=u.child;if(d!==null)d.return=u,O=d;else for(;O!==null;){u=O;var f=u.sibling,g=u.return;if(vf(u),u===c){O=null;break}if(f!==null){f.return=g,O=f;break}O=g}}}var y=s.alternate;if(y!==null){var x=y.child;if(x!==null){y.child=null;do{var k=x.sibling;x.sibling=null,x=k}while(x!==null)}}O=s}}if(s.subtreeFlags&2064&&a!==null)a.return=s,O=a;else e:for(;O!==null;){if(s=O,s.flags&2048)switch(s.tag){case 0:case 11:case 15:Fr(9,s,s.return)}var v=s.sibling;if(v!==null){v.return=s.return,O=v;break e}O=s.return}}var m=e.current;for(O=m;O!==null;){a=O;var h=a.child;if(a.subtreeFlags&2064&&h!==null)h.return=a,O=h;else e:for(a=m;O!==null;){if(o=O,o.flags&2048)try{switch(o.tag){case 0:case 11:case 15:Ys(9,o)}}catch(S){ee(o,o.return,S)}if(o===a){O=null;break e}var w=o.sibling;if(w!==null){w.return=o.return,O=w;break e}O=o.return}}if(U=i,Xt(),dt&&typeof dt.onPostCommitFiberRoot=="function")try{dt.onPostCommitFiberRoot(Ws,e)}catch{}r=!0}return r}finally{F=n,Ke.transition=t}}return!1}function Iu(e,t,n){t=sr(n,t),t=sf(e,t,1),e=Wt(e,t,1),t=je(),e!==null&&(vi(e,1,t),$e(e,t))}function ee(e,t,n){if(e.tag===3)Iu(e,e,n);else for(;t!==null;){if(t.tag===3){Iu(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(qt===null||!qt.has(r))){e=sr(n,e),e=af(t,e,1),t=Wt(t,e,1),e=je(),t!==null&&(vi(t,1,e),$e(t,e));break}}t=t.return}}function tg(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=je(),e.pingedLanes|=e.suspendedLanes&n,fe===e&&(ve&n)===n&&(oe===4||oe===3&&(ve&130023424)===ve&&500>ne()-nc?fn(e,0):tc|=n),$e(e,t)}function Ef(e,t){t===0&&(e.mode&1?(t=Ri,Ri<<=1,!(Ri&130023424)&&(Ri=4194304)):t=1);var n=je();e=kt(e,t),e!==null&&(vi(e,t,n),$e(e,n))}function ng(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Ef(e,n)}function rg(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(j(314))}r!==null&&r.delete(t),Ef(e,n)}var Cf;Cf=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ae.current)Re=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Re=!1,qv(e,t,n);Re=!!(e.flags&131072)}else Re=!1,Q&&t.flags&1048576&&Op(t,ks,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;as(e,t),e=t.pendingProps;var i=tr(t,ke.current);Xn(t,n),i=Gl(null,t,r,e,i,n);var s=Jl();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,De(r)?(s=!0,ws(t)):s=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,ql(t),i.updater=Js,t.stateNode=i,i._reactInternals=t,Uo(t,r,e,n),t=Bo(null,t,r,!0,s,n)):(t.tag=0,Q&&s&&zl(t),be(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(as(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=sg(r),e=Ze(r,e),i){case 0:t=Mo(null,t,r,e,n);break e;case 1:t=ku(null,t,r,e,n);break e;case 11:t=wu(null,t,r,e,n);break e;case 14:t=_u(null,t,r,Ze(r.type,e),n);break e}throw Error(j(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ze(r,i),Mo(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ze(r,i),ku(e,t,r,i,n);case 3:e:{if(uf(t),e===null)throw Error(j(387));r=t.pendingProps,s=t.memoizedState,i=s.element,$p(e,t),js(t,r,null,n);var a=t.memoizedState;if(r=a.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){i=sr(Error(j(423)),t),t=Su(e,t,r,n,i);break e}else if(r!==i){i=sr(Error(j(424)),t),t=Su(e,t,r,n,i);break e}else for(Ue=Bt(t.stateNode.containerInfo.firstChild),Fe=t,Q=!0,nt=null,n=Ap(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(nr(),r===i){t=St(e,t,n);break e}be(e,t,r,n)}t=t.child}return t;case 5:return zp(t),e===null&&$o(t),r=t.type,i=t.pendingProps,s=e!==null?e.memoizedProps:null,a=i.children,No(r,i)?a=null:s!==null&&No(r,s)&&(t.flags|=32),cf(e,t),be(e,t,a,n),t.child;case 6:return e===null&&$o(t),null;case 13:return df(e,t,n);case 4:return Hl(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=rr(t,null,r,n):be(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ze(r,i),wu(e,t,r,i,n);case 7:return be(e,t,t.pendingProps,n),t.child;case 8:return be(e,t,t.pendingProps.children,n),t.child;case 12:return be(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,s=t.memoizedProps,a=i.value,q(Ss,r._currentValue),r._currentValue=a,s!==null)if(st(s.value,a)){if(s.children===i.children&&!Ae.current){t=St(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var o=s.dependencies;if(o!==null){a=s.child;for(var l=o.firstContext;l!==null;){if(l.context===r){if(s.tag===1){l=xt(-1,n&-n),l.tag=2;var c=s.updateQueue;if(c!==null){c=c.shared;var u=c.pending;u===null?l.next=l:(l.next=u.next,u.next=l),c.pending=l}}s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),zo(s.return,n,t),o.lanes|=n;break}l=l.next}}else if(s.tag===10)a=s.type===t.type?null:s.child;else if(s.tag===18){if(a=s.return,a===null)throw Error(j(341));a.lanes|=n,o=a.alternate,o!==null&&(o.lanes|=n),zo(a,n,t),a=s.sibling}else a=s.child;if(a!==null)a.return=s;else for(a=s;a!==null;){if(a===t){a=null;break}if(s=a.sibling,s!==null){s.return=a.return,a=s;break}a=a.return}s=a}be(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Xn(t,n),i=Ge(i),r=r(i),t.flags|=1,be(e,t,r,n),t.child;case 14:return r=t.type,i=Ze(r,t.pendingProps),i=Ze(r.type,i),_u(e,t,r,i,n);case 15:return of(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ze(r,i),as(e,t),t.tag=1,De(r)?(e=!0,ws(t)):e=!1,Xn(t,n),rf(t,r,i),Uo(t,r,i,n),Bo(null,t,r,!0,e,n);case 19:return pf(e,t,n);case 22:return lf(e,t,n)}throw Error(j(156,t.tag))};function Pf(e,t){return ep(e,t)}function ig(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ve(e,t,n,r){return new ig(e,t,n,r)}function ac(e){return e=e.prototype,!(!e||!e.isReactComponent)}function sg(e){if(typeof e=="function")return ac(e)?1:0;if(e!=null){if(e=e.$$typeof,e===jl)return 11;if(e===El)return 14}return 2}function Vt(e,t){var n=e.alternate;return n===null?(n=Ve(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function cs(e,t,n,r,i,s){var a=2;if(r=e,typeof e=="function")ac(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case zn:return hn(n.children,i,s,t);case bl:a=8,i|=8;break;case lo:return e=Ve(12,n,t,i|2),e.elementType=lo,e.lanes=s,e;case co:return e=Ve(13,n,t,i),e.elementType=co,e.lanes=s,e;case uo:return e=Ve(19,n,t,i),e.elementType=uo,e.lanes=s,e;case zd:return Zs(n,i,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Dd:a=10;break e;case $d:a=9;break e;case jl:a=11;break e;case El:a=14;break e;case Tt:a=16,r=null;break e}throw Error(j(130,e==null?e:typeof e,""))}return t=Ve(a,n,t,i),t.elementType=e,t.type=r,t.lanes=s,t}function hn(e,t,n,r){return e=Ve(7,e,r,t),e.lanes=n,e}function Zs(e,t,n,r){return e=Ve(22,e,r,t),e.elementType=zd,e.lanes=n,e.stateNode={isHidden:!1},e}function Qa(e,t,n){return e=Ve(6,e,null,t),e.lanes=n,e}function Ga(e,t,n){return t=Ve(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ag(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ta(0),this.expirationTimes=Ta(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ta(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function oc(e,t,n,r,i,s,a,o,l){return e=new ag(e,t,n,o,l),t===1?(t=1,s===!0&&(t|=8)):t=0,s=Ve(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},ql(s),e}function og(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:$n,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Tf(e){if(!e)return Gt;e=e._reactInternals;e:{if(_n(e)!==e||e.tag!==1)throw Error(j(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(De(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(j(171))}if(e.tag===1){var n=e.type;if(De(n))return Pp(e,n,t)}return t}function Of(e,t,n,r,i,s,a,o,l){return e=oc(n,r,!0,e,i,s,a,o,l),e.context=Tf(null),n=e.current,r=je(),i=Ht(n),s=xt(r,i),s.callback=t??null,Wt(n,s,i),e.current.lanes=i,vi(e,i,r),$e(e,r),e}function ea(e,t,n,r){var i=t.current,s=je(),a=Ht(i);return n=Tf(n),t.context===null?t.context=n:t.pendingContext=n,t=xt(s,a),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Wt(i,t,a),e!==null&&(it(e,i,a,s),rs(e,i,a)),a}function Rs(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Ru(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function lc(e,t){Ru(e,t),(e=e.alternate)&&Ru(e,t)}function lg(){return null}var Nf=typeof reportError=="function"?reportError:function(e){console.error(e)};function cc(e){this._internalRoot=e}ta.prototype.render=cc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(j(409));ea(e,t,null,null)};ta.prototype.unmount=cc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;xn(function(){ea(null,e,null,null)}),t[_t]=null}};function ta(e){this._internalRoot=e}ta.prototype.unstable_scheduleHydration=function(e){if(e){var t=op();e={blockedOn:null,target:e,priority:t};for(var n=0;n<It.length&&t!==0&&t<It[n].priority;n++);It.splice(n,0,e),n===0&&cp(e)}};function uc(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function na(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Au(){}function cg(e,t,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var c=Rs(a);s.call(c)}}var a=Of(t,r,e,0,null,!1,!1,"",Au);return e._reactRootContainer=a,e[_t]=a.current,ti(e.nodeType===8?e.parentNode:e),xn(),a}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var o=r;r=function(){var c=Rs(l);o.call(c)}}var l=oc(e,0,!1,null,null,!1,!1,"",Au);return e._reactRootContainer=l,e[_t]=l.current,ti(e.nodeType===8?e.parentNode:e),xn(function(){ea(t,l,n,r)}),l}function ra(e,t,n,r,i){var s=n._reactRootContainer;if(s){var a=s;if(typeof i=="function"){var o=i;i=function(){var l=Rs(a);o.call(l)}}ea(t,a,e,i)}else a=cg(n,t,e,i,r);return Rs(a)}sp=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Nr(t.pendingLanes);n!==0&&(Tl(t,n|1),$e(t,ne()),!(U&6)&&(ar=ne()+500,Xt()))}break;case 13:xn(function(){var r=kt(e,1);if(r!==null){var i=je();it(r,e,1,i)}}),lc(e,1)}};Ol=function(e){if(e.tag===13){var t=kt(e,134217728);if(t!==null){var n=je();it(t,e,134217728,n)}lc(e,134217728)}};ap=function(e){if(e.tag===13){var t=Ht(e),n=kt(e,t);if(n!==null){var r=je();it(n,e,t,r)}lc(e,t)}};op=function(){return F};lp=function(e,t){var n=F;try{return F=e,t()}finally{F=n}};_o=function(e,t,n){switch(t){case"input":if(ho(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Ks(r);if(!i)throw Error(j(90));Ud(r),ho(r,i)}}}break;case"textarea":Md(e,n);break;case"select":t=n.value,t!=null&&Qn(e,!!n.multiple,t,!1)}};Qd=rc;Gd=xn;var ug={usingClientEntryPoint:!1,Events:[yi,Mn,Ks,Vd,Kd,rc]},Cr={findFiberByHostInstance:un,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},dg={bundleType:Cr.bundleType,version:Cr.version,rendererPackageName:Cr.rendererPackageName,rendererConfig:Cr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:bt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Xd(e),e===null?null:e.stateNode},findFiberByHostInstance:Cr.findFiberByHostInstance||lg,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var qi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!qi.isDisabled&&qi.supportsFiber)try{Ws=qi.inject(dg),dt=qi}catch{}}Be.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ug;Be.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!uc(t))throw Error(j(200));return og(e,t,null,n)};Be.createRoot=function(e,t){if(!uc(e))throw Error(j(299));var n=!1,r="",i=Nf;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=oc(e,1,!1,null,null,n,!1,r,i),e[_t]=t.current,ti(e.nodeType===8?e.parentNode:e),new cc(t)};Be.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(j(188)):(e=Object.keys(e).join(","),Error(j(268,e)));return e=Xd(t),e=e===null?null:e.stateNode,e};Be.flushSync=function(e){return xn(e)};Be.hydrate=function(e,t,n){if(!na(t))throw Error(j(200));return ra(null,e,t,!0,n)};Be.hydrateRoot=function(e,t,n){if(!uc(e))throw Error(j(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",a=Nf;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),t=Of(t,null,e,1,n??null,i,!1,s,a),e[_t]=t.current,ti(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new ta(t)};Be.render=function(e,t,n){if(!na(t))throw Error(j(200));return ra(null,e,t,!1,n)};Be.unmountComponentAtNode=function(e){if(!na(e))throw Error(j(40));return e._reactRootContainer?(xn(function(){ra(null,null,e,!1,function(){e._reactRootContainer=null,e[_t]=null})}),!0):!1};Be.unstable_batchedUpdates=rc;Be.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!na(n))throw Error(j(200));if(e==null||e._reactInternals===void 0)throw Error(j(38));return ra(e,t,n,!1,r)};Be.version="18.3.1-next-f1338f8080-20240426";function If(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(If)}catch(e){console.error(e)}}If(),Nd.exports=Be;var pg=Nd.exports,Du=pg;ao.createRoot=Du.createRoot,ao.hydrateRoot=Du.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ui(){return ui=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ui.apply(this,arguments)}var zt;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(zt||(zt={}));const $u="popstate";function fg(e){e===void 0&&(e={});function t(r,i){let{pathname:s,search:a,hash:o}=r.location;return el("",{pathname:s,search:a,hash:o},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:As(i)}return mg(t,n,null,e)}function re(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Rf(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function hg(){return Math.random().toString(36).substr(2,8)}function zu(e,t){return{usr:e.state,key:e.key,idx:t}}function el(e,t,n,r){return n===void 0&&(n=null),ui({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?fr(t):t,{state:n,key:t&&t.key||r||hg()})}function As(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function fr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function mg(e,t,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:s=!1}=r,a=i.history,o=zt.Pop,l=null,c=u();c==null&&(c=0,a.replaceState(ui({},a.state,{idx:c}),""));function u(){return(a.state||{idx:null}).idx}function d(){o=zt.Pop;let k=u(),v=k==null?null:k-c;c=k,l&&l({action:o,location:x.location,delta:v})}function f(k,v){o=zt.Push;let m=el(x.location,k,v);n&&n(m,k),c=u()+1;let h=zu(m,c),w=x.createHref(m);try{a.pushState(h,"",w)}catch(S){if(S instanceof DOMException&&S.name==="DataCloneError")throw S;i.location.assign(w)}s&&l&&l({action:o,location:x.location,delta:1})}function g(k,v){o=zt.Replace;let m=el(x.location,k,v);n&&n(m,k),c=u();let h=zu(m,c),w=x.createHref(m);a.replaceState(h,"",w),s&&l&&l({action:o,location:x.location,delta:0})}function y(k){let v=i.location.origin!=="null"?i.location.origin:i.location.href,m=typeof k=="string"?k:As(k);return m=m.replace(/ $/,"%20"),re(v,"No window.location.(origin|href) available to create URL for href: "+m),new URL(m,v)}let x={get action(){return o},get location(){return e(i,a)},listen(k){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener($u,d),l=k,()=>{i.removeEventListener($u,d),l=null}},createHref(k){return t(i,k)},createURL:y,encodeLocation(k){let v=y(k);return{pathname:v.pathname,search:v.search,hash:v.hash}},push:f,replace:g,go(k){return a.go(k)}};return x}var Lu;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Lu||(Lu={}));function vg(e,t,n){return n===void 0&&(n="/"),gg(e,t,n,!1)}function gg(e,t,n,r){let i=typeof t=="string"?fr(t):t,s=dc(i.pathname||"/",n);if(s==null)return null;let a=Af(e);yg(a);let o=null;for(let l=0;o==null&&l<a.length;++l){let c=Tg(s);o=Cg(a[l],c,r)}return o}function Af(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(s,a,o)=>{let l={relativePath:o===void 0?s.path||"":o,caseSensitive:s.caseSensitive===!0,childrenIndex:a,route:s};l.relativePath.startsWith("/")&&(re(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let c=Kt([r,l.relativePath]),u=n.concat(l);s.children&&s.children.length>0&&(re(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+c+'".')),Af(s.children,t,u,c)),!(s.path==null&&!s.index)&&t.push({path:c,score:jg(c,s.index),routesMeta:u})};return e.forEach((s,a)=>{var o;if(s.path===""||!((o=s.path)!=null&&o.includes("?")))i(s,a);else for(let l of Df(s.path))i(s,a,l)}),t}function Df(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,i=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return i?[s,""]:[s];let a=Df(r.join("/")),o=[];return o.push(...a.map(l=>l===""?s:[s,l].join("/"))),i&&o.push(...a),o.map(l=>e.startsWith("/")&&l===""?"/":l)}function yg(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Eg(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const xg=/^:[\w-]+$/,wg=3,_g=2,kg=1,Sg=10,bg=-2,Uu=e=>e==="*";function jg(e,t){let n=e.split("/"),r=n.length;return n.some(Uu)&&(r+=bg),t&&(r+=_g),n.filter(i=>!Uu(i)).reduce((i,s)=>i+(xg.test(s)?wg:s===""?kg:Sg),r)}function Eg(e,t){return e.length===t.length&&e.slice(0,-1).every((r,i)=>r===t[i])?e[e.length-1]-t[t.length-1]:0}function Cg(e,t,n){n===void 0&&(n=!1);let{routesMeta:r}=e,i={},s="/",a=[];for(let o=0;o<r.length;++o){let l=r[o],c=o===r.length-1,u=s==="/"?t:t.slice(s.length)||"/",d=Fu({path:l.relativePath,caseSensitive:l.caseSensitive,end:c},u),f=l.route;if(!d&&c&&n&&!r[r.length-1].route.index&&(d=Fu({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},u)),!d)return null;Object.assign(i,d.params),a.push({params:i,pathname:Kt([s,d.pathname]),pathnameBase:Rg(Kt([s,d.pathnameBase])),route:f}),d.pathnameBase!=="/"&&(s=Kt([s,d.pathnameBase]))}return a}function Fu(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=Pg(e.path,e.caseSensitive,e.end),i=t.match(n);if(!i)return null;let s=i[0],a=s.replace(/(.)\/+$/,"$1"),o=i.slice(1);return{params:r.reduce((c,u,d)=>{let{paramName:f,isOptional:g}=u;if(f==="*"){let x=o[d]||"";a=s.slice(0,s.length-x.length).replace(/(.)\/+$/,"$1")}const y=o[d];return g&&!y?c[f]=void 0:c[f]=(y||"").replace(/%2F/g,"/"),c},{}),pathname:s,pathnameBase:a,pattern:e}}function Pg(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Rf(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(a,o,l)=>(r.push({paramName:o,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),i+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":e!==""&&e!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,t?void 0:"i"),r]}function Tg(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Rf(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function dc(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Og(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:i=""}=typeof e=="string"?fr(e):e;return{pathname:n?n.startsWith("/")?n:Ng(n,t):t,search:Ag(r),hash:Dg(i)}}function Ng(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function Ja(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Ig(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function pc(e,t){let n=Ig(e);return t?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function fc(e,t,n,r){r===void 0&&(r=!1);let i;typeof e=="string"?i=fr(e):(i=ui({},e),re(!i.pathname||!i.pathname.includes("?"),Ja("?","pathname","search",i)),re(!i.pathname||!i.pathname.includes("#"),Ja("#","pathname","hash",i)),re(!i.search||!i.search.includes("#"),Ja("#","search","hash",i)));let s=e===""||i.pathname==="",a=s?"/":i.pathname,o;if(a==null)o=n;else{let d=t.length-1;if(!r&&a.startsWith("..")){let f=a.split("/");for(;f[0]==="..";)f.shift(),d-=1;i.pathname=f.join("/")}o=d>=0?t[d]:"/"}let l=Og(i,o),c=a&&a!=="/"&&a.endsWith("/"),u=(s||a===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(c||u)&&(l.pathname+="/"),l}const Kt=e=>e.join("/").replace(/\/\/+/g,"/"),Rg=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ag=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Dg=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function $g(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const $f=["post","put","patch","delete"];new Set($f);const zg=["get",...$f];new Set(zg);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function di(){return di=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},di.apply(this,arguments)}const hc=_.createContext(null),Lg=_.createContext(null),Zt=_.createContext(null),ia=_.createContext(null),jt=_.createContext({outlet:null,matches:[],isDataRoute:!1}),zf=_.createContext(null);function Ug(e,t){let{relative:n}=t===void 0?{}:t;hr()||re(!1);let{basename:r,navigator:i}=_.useContext(Zt),{hash:s,pathname:a,search:o}=Ff(e,{relative:n}),l=a;return r!=="/"&&(l=a==="/"?r:Kt([r,a])),i.createHref({pathname:l,search:o,hash:s})}function hr(){return _.useContext(ia)!=null}function mr(){return hr()||re(!1),_.useContext(ia).location}function Lf(e){_.useContext(Zt).static||_.useLayoutEffect(e)}function Et(){let{isDataRoute:e}=_.useContext(jt);return e?Xg():Fg()}function Fg(){hr()||re(!1);let e=_.useContext(hc),{basename:t,future:n,navigator:r}=_.useContext(Zt),{matches:i}=_.useContext(jt),{pathname:s}=mr(),a=JSON.stringify(pc(i,n.v7_relativeSplatPath)),o=_.useRef(!1);return Lf(()=>{o.current=!0}),_.useCallback(function(c,u){if(u===void 0&&(u={}),!o.current)return;if(typeof c=="number"){r.go(c);return}let d=fc(c,JSON.parse(a),s,u.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:Kt([t,d.pathname])),(u.replace?r.replace:r.push)(d,u.state,u)},[t,r,a,s,e])}function Uf(){let{matches:e}=_.useContext(jt),t=e[e.length-1];return t?t.params:{}}function Ff(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=_.useContext(Zt),{matches:i}=_.useContext(jt),{pathname:s}=mr(),a=JSON.stringify(pc(i,r.v7_relativeSplatPath));return _.useMemo(()=>fc(e,JSON.parse(a),s,n==="path"),[e,a,s,n])}function Mg(e,t){return Bg(e,t)}function Bg(e,t,n,r){hr()||re(!1);let{navigator:i}=_.useContext(Zt),{matches:s}=_.useContext(jt),a=s[s.length-1],o=a?a.params:{};a&&a.pathname;let l=a?a.pathnameBase:"/";a&&a.route;let c=mr(),u;if(t){var d;let k=typeof t=="string"?fr(t):t;l==="/"||(d=k.pathname)!=null&&d.startsWith(l)||re(!1),u=k}else u=c;let f=u.pathname||"/",g=f;if(l!=="/"){let k=l.replace(/^\//,"").split("/");g="/"+f.replace(/^\//,"").split("/").slice(k.length).join("/")}let y=vg(e,{pathname:g}),x=Kg(y&&y.map(k=>Object.assign({},k,{params:Object.assign({},o,k.params),pathname:Kt([l,i.encodeLocation?i.encodeLocation(k.pathname).pathname:k.pathname]),pathnameBase:k.pathnameBase==="/"?l:Kt([l,i.encodeLocation?i.encodeLocation(k.pathnameBase).pathname:k.pathnameBase])})),s,n,r);return t&&x?_.createElement(ia.Provider,{value:{location:di({pathname:"/",search:"",hash:"",state:null,key:"default"},u),navigationType:zt.Pop}},x):x}function Wg(){let e=Yg(),t=$g(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},s=null;return _.createElement(_.Fragment,null,_.createElement("h2",null,"Unexpected Application Error!"),_.createElement("h3",{style:{fontStyle:"italic"}},t),n?_.createElement("pre",{style:i},n):null,s)}const qg=_.createElement(Wg,null);class Hg extends _.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?_.createElement(jt.Provider,{value:this.props.routeContext},_.createElement(zf.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Vg(e){let{routeContext:t,match:n,children:r}=e,i=_.useContext(hc);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),_.createElement(jt.Provider,{value:t},r)}function Kg(e,t,n,r){var i;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var s;if(!n)return null;if(n.errors)e=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let a=e,o=(i=n)==null?void 0:i.errors;if(o!=null){let u=a.findIndex(d=>d.route.id&&(o==null?void 0:o[d.route.id])!==void 0);u>=0||re(!1),a=a.slice(0,Math.min(a.length,u+1))}let l=!1,c=-1;if(n&&r&&r.v7_partialHydration)for(let u=0;u<a.length;u++){let d=a[u];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(c=u),d.route.id){let{loaderData:f,errors:g}=n,y=d.route.loader&&f[d.route.id]===void 0&&(!g||g[d.route.id]===void 0);if(d.route.lazy||y){l=!0,c>=0?a=a.slice(0,c+1):a=[a[0]];break}}}return a.reduceRight((u,d,f)=>{let g,y=!1,x=null,k=null;n&&(g=o&&d.route.id?o[d.route.id]:void 0,x=d.route.errorElement||qg,l&&(c<0&&f===0?(Zg("route-fallback",!1),y=!0,k=null):c===f&&(y=!0,k=d.route.hydrateFallbackElement||null)));let v=t.concat(a.slice(0,f+1)),m=()=>{let h;return g?h=x:y?h=k:d.route.Component?h=_.createElement(d.route.Component,null):d.route.element?h=d.route.element:h=u,_.createElement(Vg,{match:d,routeContext:{outlet:u,matches:v,isDataRoute:n!=null},children:h})};return n&&(d.route.ErrorBoundary||d.route.errorElement||f===0)?_.createElement(Hg,{location:n.location,revalidation:n.revalidation,component:x,error:g,children:m(),routeContext:{outlet:null,matches:v,isDataRoute:!0}}):m()},null)}var Mf=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Mf||{}),Ds=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Ds||{});function Qg(e){let t=_.useContext(hc);return t||re(!1),t}function Gg(e){let t=_.useContext(Lg);return t||re(!1),t}function Jg(e){let t=_.useContext(jt);return t||re(!1),t}function Bf(e){let t=Jg(),n=t.matches[t.matches.length-1];return n.route.id||re(!1),n.route.id}function Yg(){var e;let t=_.useContext(zf),n=Gg(Ds.UseRouteError),r=Bf(Ds.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Xg(){let{router:e}=Qg(Mf.UseNavigateStable),t=Bf(Ds.UseNavigateStable),n=_.useRef(!1);return Lf(()=>{n.current=!0}),_.useCallback(function(i,s){s===void 0&&(s={}),n.current&&(typeof i=="number"?e.navigate(i):e.navigate(i,di({fromRouteId:t},s)))},[e,t])}const Mu={};function Zg(e,t,n){!t&&!Mu[e]&&(Mu[e]=!0)}function ey(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function Wf(e){let{to:t,replace:n,state:r,relative:i}=e;hr()||re(!1);let{future:s,static:a}=_.useContext(Zt),{matches:o}=_.useContext(jt),{pathname:l}=mr(),c=Et(),u=fc(t,pc(o,s.v7_relativeSplatPath),l,i==="path"),d=JSON.stringify(u);return _.useEffect(()=>c(JSON.parse(d),{replace:n,state:r,relative:i}),[c,d,i,n,r]),null}function lt(e){re(!1)}function ty(e){let{basename:t="/",children:n=null,location:r,navigationType:i=zt.Pop,navigator:s,static:a=!1,future:o}=e;hr()&&re(!1);let l=t.replace(/^\/*/,"/"),c=_.useMemo(()=>({basename:l,navigator:s,static:a,future:di({v7_relativeSplatPath:!1},o)}),[l,o,s,a]);typeof r=="string"&&(r=fr(r));let{pathname:u="/",search:d="",hash:f="",state:g=null,key:y="default"}=r,x=_.useMemo(()=>{let k=dc(u,l);return k==null?null:{location:{pathname:k,search:d,hash:f,state:g,key:y},navigationType:i}},[l,u,d,f,g,y,i]);return x==null?null:_.createElement(Zt.Provider,{value:c},_.createElement(ia.Provider,{children:n,value:x}))}function ny(e){let{children:t,location:n}=e;return Mg(tl(t),n)}new Promise(()=>{});function tl(e,t){t===void 0&&(t=[]);let n=[];return _.Children.forEach(e,(r,i)=>{if(!_.isValidElement(r))return;let s=[...t,i];if(r.type===_.Fragment){n.push.apply(n,tl(r.props.children,s));return}r.type!==lt&&re(!1),!r.props.index||!r.props.children||re(!1);let a={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(a.children=tl(r.props.children,s)),n.push(a)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function nl(){return nl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},nl.apply(this,arguments)}function ry(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,s;for(s=0;s<r.length;s++)i=r[s],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}function iy(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function sy(e,t){return e.button===0&&(!t||t==="_self")&&!iy(e)}const ay=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],oy="6";try{window.__reactRouterVersion=oy}catch{}const ly="startTransition",Bu=tm[ly];function cy(e){let{basename:t,children:n,future:r,window:i}=e,s=_.useRef();s.current==null&&(s.current=fg({window:i,v5Compat:!0}));let a=s.current,[o,l]=_.useState({action:a.action,location:a.location}),{v7_startTransition:c}=r||{},u=_.useCallback(d=>{c&&Bu?Bu(()=>l(d)):l(d)},[l,c]);return _.useLayoutEffect(()=>a.listen(u),[a,u]),_.useEffect(()=>ey(r),[r]),_.createElement(ty,{basename:t,children:n,location:o.location,navigationType:o.action,navigator:a,future:r})}const uy=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",dy=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,qf=_.forwardRef(function(t,n){let{onClick:r,relative:i,reloadDocument:s,replace:a,state:o,target:l,to:c,preventScrollReset:u,viewTransition:d}=t,f=ry(t,ay),{basename:g}=_.useContext(Zt),y,x=!1;if(typeof c=="string"&&dy.test(c)&&(y=c,uy))try{let h=new URL(window.location.href),w=c.startsWith("//")?new URL(h.protocol+c):new URL(c),S=dc(w.pathname,g);w.origin===h.origin&&S!=null?c=S+w.search+w.hash:x=!0}catch{}let k=Ug(c,{relative:i}),v=py(c,{replace:a,state:o,target:l,preventScrollReset:u,relative:i,viewTransition:d});function m(h){r&&r(h),h.defaultPrevented||v(h)}return _.createElement("a",nl({},f,{href:y||k,onClick:x||s?r:m,ref:n,target:l}))});var Wu;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Wu||(Wu={}));var qu;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(qu||(qu={}));function py(e,t){let{target:n,replace:r,state:i,preventScrollReset:s,relative:a,viewTransition:o}=t===void 0?{}:t,l=Et(),c=mr(),u=Ff(e,{relative:a});return _.useCallback(d=>{if(sy(d,n)){d.preventDefault();let f=r!==void 0?r:As(c)===As(u);l(e,{replace:f,state:i,preventScrollReset:s,relative:a,viewTransition:o})}},[c,l,u,r,i,n,e,s,a,o])}const Hu=e=>{let t;const n=new Set,r=(u,d)=>{const f=typeof u=="function"?u(t):u;if(!Object.is(f,t)){const g=t;t=d??(typeof f!="object"||f===null)?f:Object.assign({},t,f),n.forEach(y=>y(t,g))}},i=()=>t,l={setState:r,getState:i,getInitialState:()=>c,subscribe:u=>(n.add(u),()=>n.delete(u)),destroy:()=>{n.clear()}},c=t=e(r,i,l);return l},fy=e=>e?Hu(e):Hu;var Hf={exports:{}},Vf={},Kf={exports:{}},Qf={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var or=_;function hy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var my=typeof Object.is=="function"?Object.is:hy,vy=or.useState,gy=or.useEffect,yy=or.useLayoutEffect,xy=or.useDebugValue;function wy(e,t){var n=t(),r=vy({inst:{value:n,getSnapshot:t}}),i=r[0].inst,s=r[1];return yy(function(){i.value=n,i.getSnapshot=t,Ya(i)&&s({inst:i})},[e,n,t]),gy(function(){return Ya(i)&&s({inst:i}),e(function(){Ya(i)&&s({inst:i})})},[e]),xy(n),n}function Ya(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!my(e,n)}catch{return!0}}function _y(e,t){return t()}var ky=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?_y:wy;Qf.useSyncExternalStore=or.useSyncExternalStore!==void 0?or.useSyncExternalStore:ky;Kf.exports=Qf;var Sy=Kf.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sa=_,by=Sy;function jy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ey=typeof Object.is=="function"?Object.is:jy,Cy=by.useSyncExternalStore,Py=sa.useRef,Ty=sa.useEffect,Oy=sa.useMemo,Ny=sa.useDebugValue;Vf.useSyncExternalStoreWithSelector=function(e,t,n,r,i){var s=Py(null);if(s.current===null){var a={hasValue:!1,value:null};s.current=a}else a=s.current;s=Oy(function(){function l(g){if(!c){if(c=!0,u=g,g=r(g),i!==void 0&&a.hasValue){var y=a.value;if(i(y,g))return d=y}return d=g}if(y=d,Ey(u,g))return y;var x=r(g);return i!==void 0&&i(y,x)?(u=g,y):(u=g,d=x)}var c=!1,u,d,f=n===void 0?null:n;return[function(){return l(t())},f===null?void 0:function(){return l(f())}]},[t,n,r,i]);var o=Cy(e,s[0],s[1]);return Ty(function(){a.hasValue=!0,a.value=o},[o]),Ny(o),o};Hf.exports=Vf;var Iy=Hf.exports;const Ry=vl(Iy),{useDebugValue:Ay}=Bs,{useSyncExternalStoreWithSelector:Dy}=Ry;const $y=e=>e;function zy(e,t=$y,n){const r=Dy(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return Ay(r),r}const Vu=e=>{const t=typeof e=="function"?fy(e):e,n=(r,i)=>zy(t,r,i);return Object.assign(n,t),n},aa=e=>e?Vu(e):Vu,Ly="modulepreload",Uy=function(e){return"/"+e},Ku={},wi=function(t,n,r){if(!n||n.length===0)return t();const i=document.getElementsByTagName("link");return Promise.all(n.map(s=>{if(s=Uy(s),s in Ku)return;Ku[s]=!0;const a=s.endsWith(".css"),o=a?'[rel="stylesheet"]':"";if(!!r)for(let u=i.length-1;u>=0;u--){const d=i[u];if(d.href===s&&(!a||d.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${s}"]${o}`))return;const c=document.createElement("link");if(c.rel=a?"stylesheet":Ly,a||(c.as="script",c.crossOrigin=""),c.href=s,document.head.appendChild(c),a)return new Promise((u,d)=>{c.addEventListener("load",u),c.addEventListener("error",()=>d(new Error(`Unable to preload CSS for ${s}`)))})})).then(()=>t()).catch(s=>{const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=s,window.dispatchEvent(a),!a.defaultPrevented)throw s})},Fy=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>wi(()=>Promise.resolve().then(()=>vr),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)};class mc extends Error{constructor(t,n="FunctionsError",r){super(t),this.name=n,this.context=r}}class My extends mc{constructor(t){super("Failed to send a request to the Edge Function","FunctionsFetchError",t)}}class By extends mc{constructor(t){super("Relay Error invoking the Edge Function","FunctionsRelayError",t)}}class Wy extends mc{constructor(t){super("Edge Function returned a non-2xx status code","FunctionsHttpError",t)}}var rl;(function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"})(rl||(rl={}));var qy=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(a){a(s)})}return new(n||(n=Promise))(function(s,a){function o(u){try{c(r.next(u))}catch(d){a(d)}}function l(u){try{c(r.throw(u))}catch(d){a(d)}}function c(u){u.done?s(u.value):i(u.value).then(o,l)}c((r=r.apply(e,t||[])).next())})};class Hy{constructor(t,{headers:n={},customFetch:r,region:i=rl.Any}={}){this.url=t,this.headers=n,this.region=i,this.fetch=Fy(r)}setAuth(t){this.headers.Authorization=`Bearer ${t}`}invoke(t,n={}){var r;return qy(this,void 0,void 0,function*(){try{const{headers:i,method:s,body:a}=n;let o={},{region:l}=n;l||(l=this.region),l&&l!=="any"&&(o["x-region"]=l);let c;a&&(i&&!Object.prototype.hasOwnProperty.call(i,"Content-Type")||!i)&&(typeof Blob<"u"&&a instanceof Blob||a instanceof ArrayBuffer?(o["Content-Type"]="application/octet-stream",c=a):typeof a=="string"?(o["Content-Type"]="text/plain",c=a):typeof FormData<"u"&&a instanceof FormData?c=a:(o["Content-Type"]="application/json",c=JSON.stringify(a)));const u=yield this.fetch(`${this.url}/${t}`,{method:s||"POST",headers:Object.assign(Object.assign(Object.assign({},o),this.headers),i),body:c}).catch(y=>{throw new My(y)}),d=u.headers.get("x-relay-error");if(d&&d==="true")throw new By(u);if(!u.ok)throw new Wy(u);let f=((r=u.headers.get("Content-Type"))!==null&&r!==void 0?r:"text/plain").split(";")[0].trim(),g;return f==="application/json"?g=yield u.json():f==="application/octet-stream"?g=yield u.blob():f==="text/event-stream"?g=u:f==="multipart/form-data"?g=yield u.formData():g=yield u.text(),{data:g,error:null}}catch(i){return{data:null,error:i}}})}}var Ie={},vc={},oa={},_i={},la={},ca={},Vy=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},lr=Vy();const Ky=lr.fetch,Gf=lr.fetch.bind(lr),Jf=lr.Headers,Qy=lr.Request,Gy=lr.Response,vr=Object.freeze(Object.defineProperty({__proto__:null,Headers:Jf,Request:Qy,Response:Gy,default:Gf,fetch:Ky},Symbol.toStringTag,{value:"Module"})),Jy=Uh(vr);var ua={};Object.defineProperty(ua,"__esModule",{value:!0});let Yy=class extends Error{constructor(t){super(t.message),this.name="PostgrestError",this.details=t.details,this.hint=t.hint,this.code=t.code}};ua.default=Yy;var Yf=Qe&&Qe.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ca,"__esModule",{value:!0});const Xy=Yf(Jy),Zy=Yf(ua);let e0=class{constructor(t){this.shouldThrowOnError=!1,this.method=t.method,this.url=t.url,this.headers=t.headers,this.schema=t.schema,this.body=t.body,this.shouldThrowOnError=t.shouldThrowOnError,this.signal=t.signal,this.isMaybeSingle=t.isMaybeSingle,t.fetch?this.fetch=t.fetch:typeof fetch>"u"?this.fetch=Xy.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(t,n){return this.headers=Object.assign({},this.headers),this.headers[t]=n,this}then(t,n){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const r=this.fetch;let i=r(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async s=>{var a,o,l;let c=null,u=null,d=null,f=s.status,g=s.statusText;if(s.ok){if(this.method!=="HEAD"){const v=await s.text();v===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?u=v:u=JSON.parse(v))}const x=(a=this.headers.Prefer)===null||a===void 0?void 0:a.match(/count=(exact|planned|estimated)/),k=(o=s.headers.get("content-range"))===null||o===void 0?void 0:o.split("/");x&&k&&k.length>1&&(d=parseInt(k[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(u)&&(u.length>1?(c={code:"PGRST116",details:`Results contain ${u.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},u=null,d=null,f=406,g="Not Acceptable"):u.length===1?u=u[0]:u=null)}else{const x=await s.text();try{c=JSON.parse(x),Array.isArray(c)&&s.status===404&&(u=[],c=null,f=200,g="OK")}catch{s.status===404&&x===""?(f=204,g="No Content"):c={message:x}}if(c&&this.isMaybeSingle&&(!((l=c==null?void 0:c.details)===null||l===void 0)&&l.includes("0 rows"))&&(c=null,f=200,g="OK"),c&&this.shouldThrowOnError)throw new Zy.default(c)}return{error:c,data:u,count:d,status:f,statusText:g}});return this.shouldThrowOnError||(i=i.catch(s=>{var a,o,l;return{error:{message:`${(a=s==null?void 0:s.name)!==null&&a!==void 0?a:"FetchError"}: ${s==null?void 0:s.message}`,details:`${(o=s==null?void 0:s.stack)!==null&&o!==void 0?o:""}`,hint:"",code:`${(l=s==null?void 0:s.code)!==null&&l!==void 0?l:""}`},data:null,count:null,status:0,statusText:""}})),i.then(t,n)}returns(){return this}overrideTypes(){return this}};ca.default=e0;var t0=Qe&&Qe.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(la,"__esModule",{value:!0});const n0=t0(ca);let r0=class extends n0.default{select(t){let n=!1;const r=(t??"*").split("").map(i=>/\s/.test(i)&&!n?"":(i==='"'&&(n=!n),i)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(t,{ascending:n=!0,nullsFirst:r,foreignTable:i,referencedTable:s=i}={}){const a=s?`${s}.order`:"order",o=this.url.searchParams.get(a);return this.url.searchParams.set(a,`${o?`${o},`:""}${t}.${n?"asc":"desc"}${r===void 0?"":r?".nullsfirst":".nullslast"}`),this}limit(t,{foreignTable:n,referencedTable:r=n}={}){const i=typeof r>"u"?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${t}`),this}range(t,n,{foreignTable:r,referencedTable:i=r}={}){const s=typeof i>"u"?"offset":`${i}.offset`,a=typeof i>"u"?"limit":`${i}.limit`;return this.url.searchParams.set(s,`${t}`),this.url.searchParams.set(a,`${n-t+1}`),this}abortSignal(t){return this.signal=t,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:t=!1,verbose:n=!1,settings:r=!1,buffers:i=!1,wal:s=!1,format:a="text"}={}){var o;const l=[t?"analyze":null,n?"verbose":null,r?"settings":null,i?"buffers":null,s?"wal":null].filter(Boolean).join("|"),c=(o=this.headers.Accept)!==null&&o!==void 0?o:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${a}; for="${c}"; options=${l};`,a==="json"?this:this}rollback(){var t;return((t=this.headers.Prefer)!==null&&t!==void 0?t:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};la.default=r0;var i0=Qe&&Qe.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(_i,"__esModule",{value:!0});const s0=i0(la);let a0=class extends s0.default{eq(t,n){return this.url.searchParams.append(t,`eq.${n}`),this}neq(t,n){return this.url.searchParams.append(t,`neq.${n}`),this}gt(t,n){return this.url.searchParams.append(t,`gt.${n}`),this}gte(t,n){return this.url.searchParams.append(t,`gte.${n}`),this}lt(t,n){return this.url.searchParams.append(t,`lt.${n}`),this}lte(t,n){return this.url.searchParams.append(t,`lte.${n}`),this}like(t,n){return this.url.searchParams.append(t,`like.${n}`),this}likeAllOf(t,n){return this.url.searchParams.append(t,`like(all).{${n.join(",")}}`),this}likeAnyOf(t,n){return this.url.searchParams.append(t,`like(any).{${n.join(",")}}`),this}ilike(t,n){return this.url.searchParams.append(t,`ilike.${n}`),this}ilikeAllOf(t,n){return this.url.searchParams.append(t,`ilike(all).{${n.join(",")}}`),this}ilikeAnyOf(t,n){return this.url.searchParams.append(t,`ilike(any).{${n.join(",")}}`),this}is(t,n){return this.url.searchParams.append(t,`is.${n}`),this}in(t,n){const r=Array.from(new Set(n)).map(i=>typeof i=="string"&&new RegExp("[,()]").test(i)?`"${i}"`:`${i}`).join(",");return this.url.searchParams.append(t,`in.(${r})`),this}contains(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cs.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cs.{${n.join(",")}}`):this.url.searchParams.append(t,`cs.${JSON.stringify(n)}`),this}containedBy(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cd.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cd.{${n.join(",")}}`):this.url.searchParams.append(t,`cd.${JSON.stringify(n)}`),this}rangeGt(t,n){return this.url.searchParams.append(t,`sr.${n}`),this}rangeGte(t,n){return this.url.searchParams.append(t,`nxl.${n}`),this}rangeLt(t,n){return this.url.searchParams.append(t,`sl.${n}`),this}rangeLte(t,n){return this.url.searchParams.append(t,`nxr.${n}`),this}rangeAdjacent(t,n){return this.url.searchParams.append(t,`adj.${n}`),this}overlaps(t,n){return typeof n=="string"?this.url.searchParams.append(t,`ov.${n}`):this.url.searchParams.append(t,`ov.{${n.join(",")}}`),this}textSearch(t,n,{config:r,type:i}={}){let s="";i==="plain"?s="pl":i==="phrase"?s="ph":i==="websearch"&&(s="w");const a=r===void 0?"":`(${r})`;return this.url.searchParams.append(t,`${s}fts${a}.${n}`),this}match(t){return Object.entries(t).forEach(([n,r])=>{this.url.searchParams.append(n,`eq.${r}`)}),this}not(t,n,r){return this.url.searchParams.append(t,`not.${n}.${r}`),this}or(t,{foreignTable:n,referencedTable:r=n}={}){const i=r?`${r}.or`:"or";return this.url.searchParams.append(i,`(${t})`),this}filter(t,n,r){return this.url.searchParams.append(t,`${n}.${r}`),this}};_i.default=a0;var o0=Qe&&Qe.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(oa,"__esModule",{value:!0});const Pr=o0(_i);let l0=class{constructor(t,{headers:n={},schema:r,fetch:i}){this.url=t,this.headers=n,this.schema=r,this.fetch=i}select(t,{head:n=!1,count:r}={}){const i=n?"HEAD":"GET";let s=!1;const a=(t??"*").split("").map(o=>/\s/.test(o)&&!s?"":(o==='"'&&(s=!s),o)).join("");return this.url.searchParams.set("select",a),r&&(this.headers.Prefer=`count=${r}`),new Pr.default({method:i,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(t,{count:n,defaultToNull:r=!0}={}){const i="POST",s=[];if(this.headers.Prefer&&s.push(this.headers.Prefer),n&&s.push(`count=${n}`),r||s.push("missing=default"),this.headers.Prefer=s.join(","),Array.isArray(t)){const a=t.reduce((o,l)=>o.concat(Object.keys(l)),[]);if(a.length>0){const o=[...new Set(a)].map(l=>`"${l}"`);this.url.searchParams.set("columns",o.join(","))}}return new Pr.default({method:i,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}upsert(t,{onConflict:n,ignoreDuplicates:r=!1,count:i,defaultToNull:s=!0}={}){const a="POST",o=[`resolution=${r?"ignore":"merge"}-duplicates`];if(n!==void 0&&this.url.searchParams.set("on_conflict",n),this.headers.Prefer&&o.push(this.headers.Prefer),i&&o.push(`count=${i}`),s||o.push("missing=default"),this.headers.Prefer=o.join(","),Array.isArray(t)){const l=t.reduce((c,u)=>c.concat(Object.keys(u)),[]);if(l.length>0){const c=[...new Set(l)].map(u=>`"${u}"`);this.url.searchParams.set("columns",c.join(","))}}return new Pr.default({method:a,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}update(t,{count:n}={}){const r="PATCH",i=[];return this.headers.Prefer&&i.push(this.headers.Prefer),n&&i.push(`count=${n}`),this.headers.Prefer=i.join(","),new Pr.default({method:r,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}delete({count:t}={}){const n="DELETE",r=[];return t&&r.push(`count=${t}`),this.headers.Prefer&&r.unshift(this.headers.Prefer),this.headers.Prefer=r.join(","),new Pr.default({method:n,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};oa.default=l0;var da={},pa={};Object.defineProperty(pa,"__esModule",{value:!0});pa.version=void 0;pa.version="0.0.0-automated";Object.defineProperty(da,"__esModule",{value:!0});da.DEFAULT_HEADERS=void 0;const c0=pa;da.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${c0.version}`};var Xf=Qe&&Qe.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(vc,"__esModule",{value:!0});const u0=Xf(oa),d0=Xf(_i),p0=da;let f0=class Zf{constructor(t,{headers:n={},schema:r,fetch:i}={}){this.url=t,this.headers=Object.assign(Object.assign({},p0.DEFAULT_HEADERS),n),this.schemaName=r,this.fetch=i}from(t){const n=new URL(`${this.url}/${t}`);return new u0.default(n,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new Zf(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(t,n={},{head:r=!1,get:i=!1,count:s}={}){let a;const o=new URL(`${this.url}/rpc/${t}`);let l;r||i?(a=r?"HEAD":"GET",Object.entries(n).filter(([u,d])=>d!==void 0).map(([u,d])=>[u,Array.isArray(d)?`{${d.join(",")}}`:`${d}`]).forEach(([u,d])=>{o.searchParams.append(u,d)})):(a="POST",l=n);const c=Object.assign({},this.headers);return s&&(c.Prefer=`count=${s}`),new d0.default({method:a,url:o,headers:c,schema:this.schemaName,body:l,fetch:this.fetch,allowEmpty:!1})}};vc.default=f0;var gr=Qe&&Qe.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ie,"__esModule",{value:!0});Ie.PostgrestError=Ie.PostgrestBuilder=Ie.PostgrestTransformBuilder=Ie.PostgrestFilterBuilder=Ie.PostgrestQueryBuilder=Ie.PostgrestClient=void 0;const eh=gr(vc);Ie.PostgrestClient=eh.default;const th=gr(oa);Ie.PostgrestQueryBuilder=th.default;const nh=gr(_i);Ie.PostgrestFilterBuilder=nh.default;const rh=gr(la);Ie.PostgrestTransformBuilder=rh.default;const ih=gr(ca);Ie.PostgrestBuilder=ih.default;const sh=gr(ua);Ie.PostgrestError=sh.default;var h0=Ie.default={PostgrestClient:eh.default,PostgrestQueryBuilder:th.default,PostgrestFilterBuilder:nh.default,PostgrestTransformBuilder:rh.default,PostgrestBuilder:ih.default,PostgrestError:sh.default};const{PostgrestClient:m0,PostgrestQueryBuilder:k_,PostgrestFilterBuilder:S_,PostgrestTransformBuilder:b_,PostgrestBuilder:j_,PostgrestError:E_}=h0;function v0(){if(typeof WebSocket<"u")return WebSocket;if(typeof global.WebSocket<"u")return global.WebSocket;if(typeof window.WebSocket<"u")return window.WebSocket;if(typeof self.WebSocket<"u")return self.WebSocket;throw new Error("`WebSocket` is not supported in this environment")}const g0=v0(),y0="2.11.15",x0=`realtime-js/${y0}`,w0="1.0.0",ah=1e4,_0=1e3;var Wr;(function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"})(Wr||(Wr={}));var we;(function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"})(we||(we={}));var tt;(function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"})(tt||(tt={}));var il;(function(e){e.websocket="websocket"})(il||(il={}));var ln;(function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"})(ln||(ln={}));class k0{constructor(){this.HEADER_LENGTH=1}decode(t,n){return t.constructor===ArrayBuffer?n(this._binaryDecode(t)):n(typeof t=="string"?JSON.parse(t):{})}_binaryDecode(t){const n=new DataView(t),r=new TextDecoder;return this._decodeBroadcast(t,n,r)}_decodeBroadcast(t,n,r){const i=n.getUint8(1),s=n.getUint8(2);let a=this.HEADER_LENGTH+2;const o=r.decode(t.slice(a,a+i));a=a+i;const l=r.decode(t.slice(a,a+s));a=a+s;const c=JSON.parse(r.decode(t.slice(a,t.byteLength)));return{ref:null,topic:o,event:l,payload:c}}}class oh{constructor(t,n){this.callback=t,this.timerCalc=n,this.timer=void 0,this.tries=0,this.callback=t,this.timerCalc=n}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var W;(function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"})(W||(W={}));const Qu=(e,t,n={})=>{var r;const i=(r=n.skipTypes)!==null&&r!==void 0?r:[];return Object.keys(t).reduce((s,a)=>(s[a]=S0(a,e,t,i),s),{})},S0=(e,t,n,r)=>{const i=t.find(o=>o.name===e),s=i==null?void 0:i.type,a=n[e];return s&&!r.includes(s)?lh(s,a):sl(a)},lh=(e,t)=>{if(e.charAt(0)==="_"){const n=e.slice(1,e.length);return C0(t,n)}switch(e){case W.bool:return b0(t);case W.float4:case W.float8:case W.int2:case W.int4:case W.int8:case W.numeric:case W.oid:return j0(t);case W.json:case W.jsonb:return E0(t);case W.timestamp:return P0(t);case W.abstime:case W.date:case W.daterange:case W.int4range:case W.int8range:case W.money:case W.reltime:case W.text:case W.time:case W.timestamptz:case W.timetz:case W.tsrange:case W.tstzrange:return sl(t);default:return sl(t)}},sl=e=>e,b0=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},j0=e=>{if(typeof e=="string"){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},E0=e=>{if(typeof e=="string")try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},C0=(e,t)=>{if(typeof e!="string")return e;const n=e.length-1,r=e[n];if(e[0]==="{"&&r==="}"){let s;const a=e.slice(1,n);try{s=JSON.parse("["+a+"]")}catch{s=a?a.split(","):[]}return s.map(o=>lh(t,o))}return e},P0=e=>typeof e=="string"?e.replace(" ","T"):e,ch=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class Xa{constructor(t,n,r={},i=ah){this.channel=t,this.event=n,this.payload=r,this.timeout=i,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(t){this.timeout=t,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(t){this.payload=Object.assign(Object.assign({},this.payload),t)}receive(t,n){var r;return this._hasReceived(t)&&n((r=this.receivedResp)===null||r===void 0?void 0:r.response),this.recHooks.push({status:t,callback:n}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const t=n=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=n,this._matchReceive(n)};this.channel._on(this.refEvent,{},t),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(t,n){this.refEvent&&this.channel._trigger(this.refEvent,{status:t,response:n})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:t,response:n}){this.recHooks.filter(r=>r.status===t).forEach(r=>r.callback(n))}_hasReceived(t){return this.receivedResp&&this.receivedResp.status===t}}var Gu;(function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"})(Gu||(Gu={}));class qr{constructor(t,n){this.channel=t,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=(n==null?void 0:n.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},i=>{const{onJoin:s,onLeave:a,onSync:o}=this.caller;this.joinRef=this.channel._joinRef(),this.state=qr.syncState(this.state,i,s,a),this.pendingDiffs.forEach(l=>{this.state=qr.syncDiff(this.state,l,s,a)}),this.pendingDiffs=[],o()}),this.channel._on(r.diff,{},i=>{const{onJoin:s,onLeave:a,onSync:o}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(i):(this.state=qr.syncDiff(this.state,i,s,a),o())}),this.onJoin((i,s,a)=>{this.channel._trigger("presence",{event:"join",key:i,currentPresences:s,newPresences:a})}),this.onLeave((i,s,a)=>{this.channel._trigger("presence",{event:"leave",key:i,currentPresences:s,leftPresences:a})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(t,n,r,i){const s=this.cloneDeep(t),a=this.transformState(n),o={},l={};return this.map(s,(c,u)=>{a[c]||(l[c]=u)}),this.map(a,(c,u)=>{const d=s[c];if(d){const f=u.map(k=>k.presence_ref),g=d.map(k=>k.presence_ref),y=u.filter(k=>g.indexOf(k.presence_ref)<0),x=d.filter(k=>f.indexOf(k.presence_ref)<0);y.length>0&&(o[c]=y),x.length>0&&(l[c]=x)}else o[c]=u}),this.syncDiff(s,{joins:o,leaves:l},r,i)}static syncDiff(t,n,r,i){const{joins:s,leaves:a}={joins:this.transformState(n.joins),leaves:this.transformState(n.leaves)};return r||(r=()=>{}),i||(i=()=>{}),this.map(s,(o,l)=>{var c;const u=(c=t[o])!==null&&c!==void 0?c:[];if(t[o]=this.cloneDeep(l),u.length>0){const d=t[o].map(g=>g.presence_ref),f=u.filter(g=>d.indexOf(g.presence_ref)<0);t[o].unshift(...f)}r(o,u,l)}),this.map(a,(o,l)=>{let c=t[o];if(!c)return;const u=l.map(d=>d.presence_ref);c=c.filter(d=>u.indexOf(d.presence_ref)<0),t[o]=c,i(o,c,l),c.length===0&&delete t[o]}),t}static map(t,n){return Object.getOwnPropertyNames(t).map(r=>n(r,t[r]))}static transformState(t){return t=this.cloneDeep(t),Object.getOwnPropertyNames(t).reduce((n,r)=>{const i=t[r];return"metas"in i?n[r]=i.metas.map(s=>(s.presence_ref=s.phx_ref,delete s.phx_ref,delete s.phx_ref_prev,s)):n[r]=i,n},{})}static cloneDeep(t){return JSON.parse(JSON.stringify(t))}onJoin(t){this.caller.onJoin=t}onLeave(t){this.caller.onLeave=t}onSync(t){this.caller.onSync=t}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var Ju;(function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"})(Ju||(Ju={}));var Yu;(function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"})(Yu||(Yu={}));var mt;(function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"})(mt||(mt={}));class gc{constructor(t,n={config:{}},r){this.topic=t,this.params=n,this.socket=r,this.bindings={},this.state=we.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=t.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},n.config),this.timeout=this.socket.timeout,this.joinPush=new Xa(this,tt.join,this.params,this.timeout),this.rejoinTimer=new oh(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=we.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(i=>i.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=we.closed,this.socket._remove(this)}),this._onError(i=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,i),this.state=we.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=we.errored,this.rejoinTimer.scheduleTimeout())}),this._on(tt.reply,{},(i,s)=>{this._trigger(this._replyEventName(s),i)}),this.presence=new qr(this),this.broadcastEndpointURL=ch(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(t,n=this.timeout){var r,i;if(this.socket.isConnected()||this.socket.connect(),this.state==we.closed){const{config:{broadcast:s,presence:a,private:o}}=this.params;this._onError(u=>t==null?void 0:t(mt.CHANNEL_ERROR,u)),this._onClose(()=>t==null?void 0:t(mt.CLOSED));const l={},c={broadcast:s,presence:a,postgres_changes:(i=(r=this.bindings.postgres_changes)===null||r===void 0?void 0:r.map(u=>u.filter))!==null&&i!==void 0?i:[],private:o};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:c},l)),this.joinedOnce=!0,this._rejoin(n),this.joinPush.receive("ok",async({postgres_changes:u})=>{var d;if(this.socket.setAuth(),u===void 0){t==null||t(mt.SUBSCRIBED);return}else{const f=this.bindings.postgres_changes,g=(d=f==null?void 0:f.length)!==null&&d!==void 0?d:0,y=[];for(let x=0;x<g;x++){const k=f[x],{filter:{event:v,schema:m,table:h,filter:w}}=k,S=u&&u[x];if(S&&S.event===v&&S.schema===m&&S.table===h&&S.filter===w)y.push(Object.assign(Object.assign({},k),{id:S.id}));else{this.unsubscribe(),this.state=we.errored,t==null||t(mt.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=y,t&&t(mt.SUBSCRIBED);return}}).receive("error",u=>{this.state=we.errored,t==null||t(mt.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(u).join(", ")||"error")))}).receive("timeout",()=>{t==null||t(mt.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(t,n={}){return await this.send({type:"presence",event:"track",payload:t},n.timeout||this.timeout)}async untrack(t={}){return await this.send({type:"presence",event:"untrack"},t)}on(t,n,r){return this._on(t,n,r)}async send(t,n={}){var r,i;if(!this._canPush()&&t.type==="broadcast"){const{event:s,payload:a}=t,l={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:s,payload:a,private:this.private}]})};try{const c=await this._fetchWithTimeout(this.broadcastEndpointURL,l,(r=n.timeout)!==null&&r!==void 0?r:this.timeout);return await((i=c.body)===null||i===void 0?void 0:i.cancel()),c.ok?"ok":"error"}catch(c){return c.name==="AbortError"?"timed out":"error"}}else return new Promise(s=>{var a,o,l;const c=this._push(t.type,t,n.timeout||this.timeout);t.type==="broadcast"&&!(!((l=(o=(a=this.params)===null||a===void 0?void 0:a.config)===null||o===void 0?void 0:o.broadcast)===null||l===void 0)&&l.ack)&&s("ok"),c.receive("ok",()=>s("ok")),c.receive("error",()=>s("error")),c.receive("timeout",()=>s("timed out"))})}updateJoinPayload(t){this.joinPush.updatePayload(t)}unsubscribe(t=this.timeout){this.state=we.leaving;const n=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(tt.close,"leave",this._joinRef())};this.joinPush.destroy();let r=null;return new Promise(i=>{r=new Xa(this,tt.leave,{},t),r.receive("ok",()=>{n(),i("ok")}).receive("timeout",()=>{n(),i("timed out")}).receive("error",()=>{i("error")}),r.send(),this._canPush()||r.trigger("ok",{})}).finally(()=>{r==null||r.destroy()})}teardown(){this.pushBuffer.forEach(t=>t.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(t,n,r){const i=new AbortController,s=setTimeout(()=>i.abort(),r),a=await this.socket.fetch(t,Object.assign(Object.assign({},n),{signal:i.signal}));return clearTimeout(s),a}_push(t,n,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${t}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let i=new Xa(this,t,n,r);return this._canPush()?i.send():(i.startTimeout(),this.pushBuffer.push(i)),i}_onMessage(t,n,r){return n}_isMember(t){return this.topic===t}_joinRef(){return this.joinPush.ref}_trigger(t,n,r){var i,s;const a=t.toLocaleLowerCase(),{close:o,error:l,leave:c,join:u}=tt;if(r&&[o,l,c,u].indexOf(a)>=0&&r!==this._joinRef())return;let f=this._onMessage(a,n,r);if(n&&!f)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(a)?(i=this.bindings.postgres_changes)===null||i===void 0||i.filter(g=>{var y,x,k;return((y=g.filter)===null||y===void 0?void 0:y.event)==="*"||((k=(x=g.filter)===null||x===void 0?void 0:x.event)===null||k===void 0?void 0:k.toLocaleLowerCase())===a}).map(g=>g.callback(f,r)):(s=this.bindings[a])===null||s===void 0||s.filter(g=>{var y,x,k,v,m,h;if(["broadcast","presence","postgres_changes"].includes(a))if("id"in g){const w=g.id,S=(y=g.filter)===null||y===void 0?void 0:y.event;return w&&((x=n.ids)===null||x===void 0?void 0:x.includes(w))&&(S==="*"||(S==null?void 0:S.toLocaleLowerCase())===((k=n.data)===null||k===void 0?void 0:k.type.toLocaleLowerCase()))}else{const w=(m=(v=g==null?void 0:g.filter)===null||v===void 0?void 0:v.event)===null||m===void 0?void 0:m.toLocaleLowerCase();return w==="*"||w===((h=n==null?void 0:n.event)===null||h===void 0?void 0:h.toLocaleLowerCase())}else return g.type.toLocaleLowerCase()===a}).map(g=>{if(typeof f=="object"&&"ids"in f){const y=f.data,{schema:x,table:k,commit_timestamp:v,type:m,errors:h}=y;f=Object.assign(Object.assign({},{schema:x,table:k,commit_timestamp:v,eventType:m,new:{},old:{},errors:h}),this._getPayloadRecords(y))}g.callback(f,r)})}_isClosed(){return this.state===we.closed}_isJoined(){return this.state===we.joined}_isJoining(){return this.state===we.joining}_isLeaving(){return this.state===we.leaving}_replyEventName(t){return`chan_reply_${t}`}_on(t,n,r){const i=t.toLocaleLowerCase(),s={type:i,filter:n,callback:r};return this.bindings[i]?this.bindings[i].push(s):this.bindings[i]=[s],this}_off(t,n){const r=t.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(i=>{var s;return!(((s=i.type)===null||s===void 0?void 0:s.toLocaleLowerCase())===r&&gc.isEqual(i.filter,n))}),this}static isEqual(t,n){if(Object.keys(t).length!==Object.keys(n).length)return!1;for(const r in t)if(t[r]!==n[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(t){this._on(tt.close,{},t)}_onError(t){this._on(tt.error,{},n=>t(n))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(t=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=we.joining,this.joinPush.resend(t))}_getPayloadRecords(t){const n={new:{},old:{}};return(t.type==="INSERT"||t.type==="UPDATE")&&(n.new=Qu(t.columns,t.record)),(t.type==="UPDATE"||t.type==="DELETE")&&(n.old=Qu(t.columns,t.old_record)),n}}const Xu=()=>{},T0=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class O0{constructor(t,n){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=ah,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=Xu,this.ref=0,this.logger=Xu,this.conn=null,this.sendBuffer=[],this.serializer=new k0,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=s=>{let a;return s?a=s:typeof fetch>"u"?a=(...o)=>wi(()=>Promise.resolve().then(()=>vr),void 0).then(({default:l})=>l(...o)):a=fetch,(...o)=>a(...o)},this.endPoint=`${t}/${il.websocket}`,this.httpEndpoint=ch(t),n!=null&&n.transport?this.transport=n.transport:this.transport=null,n!=null&&n.params&&(this.params=n.params),n!=null&&n.timeout&&(this.timeout=n.timeout),n!=null&&n.logger&&(this.logger=n.logger),(n!=null&&n.logLevel||n!=null&&n.log_level)&&(this.logLevel=n.logLevel||n.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),n!=null&&n.heartbeatIntervalMs&&(this.heartbeatIntervalMs=n.heartbeatIntervalMs);const i=(r=n==null?void 0:n.params)===null||r===void 0?void 0:r.apikey;if(i&&(this.accessTokenValue=i,this.apiKey=i),this.reconnectAfterMs=n!=null&&n.reconnectAfterMs?n.reconnectAfterMs:s=>[1e3,2e3,5e3,1e4][s-1]||1e4,this.encode=n!=null&&n.encode?n.encode:(s,a)=>a(JSON.stringify(s)),this.decode=n!=null&&n.decode?n.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new oh(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(n==null?void 0:n.fetch),n!=null&&n.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(n==null?void 0:n.worker)||!1,this.workerUrl=n==null?void 0:n.workerUrl}this.accessToken=(n==null?void 0:n.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=g0),!this.transport)throw new Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:w0}))}disconnect(t,n){this.conn&&(this.conn.onclose=function(){},t?this.conn.close(t,n??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(r=>r.teardown()))}getChannels(){return this.channels}async removeChannel(t){const n=await t.unsubscribe();return this.channels.length===0&&this.disconnect(),n}async removeAllChannels(){const t=await Promise.all(this.channels.map(n=>n.unsubscribe()));return this.channels=[],this.disconnect(),t}log(t,n,r){this.logger(t,n,r)}connectionState(){switch(this.conn&&this.conn.readyState){case Wr.connecting:return ln.Connecting;case Wr.open:return ln.Open;case Wr.closing:return ln.Closing;default:return ln.Closed}}isConnected(){return this.connectionState()===ln.Open}channel(t,n={config:{}}){const r=`realtime:${t}`,i=this.getChannels().find(s=>s.topic===r);if(i)return i;{const s=new gc(`realtime:${t}`,n,this);return this.channels.push(s),s}}push(t){const{topic:n,event:r,payload:i,ref:s}=t,a=()=>{this.encode(t,o=>{var l;(l=this.conn)===null||l===void 0||l.send(o)})};this.log("push",`${n} ${r} (${s})`,i),this.isConnected()?a():this.sendBuffer.push(a)}async setAuth(t=null){let n=t||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=n&&(this.accessTokenValue=n,this.channels.forEach(r=>{const i={access_token:n,version:x0};n&&r.updateJoinPayload(i),r.joinedOnce&&r._isJoined()&&r._push(tt.access_token,{access_token:n})}))}async sendHeartbeat(){var t;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),(t=this.conn)===null||t===void 0||t.close(_0,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(t){this.heartbeatCallback=t}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(t=>t()),this.sendBuffer=[])}_makeRef(){let t=this.ref+1;return t===this.ref?this.ref=0:this.ref=t,this.ref.toString()}_leaveOpenTopic(t){let n=this.channels.find(r=>r.topic===t&&(r._isJoined()||r._isJoining()));n&&(this.log("transport",`leaving duplicate topic "${t}"`),n.unsubscribe())}_remove(t){this.channels=this.channels.filter(n=>n.topic!==t.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=t=>this._onConnError(t),this.conn.onmessage=t=>this._onConnMessage(t),this.conn.onclose=t=>this._onConnClose(t))}_onConnMessage(t){this.decode(t.data,n=>{let{topic:r,event:i,payload:s,ref:a}=n;r==="phoenix"&&i==="phx_reply"&&this.heartbeatCallback(n.payload.status=="ok"?"ok":"error"),a&&a===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${s.status||""} ${r} ${i} ${a&&"("+a+")"||""}`,s),Array.from(this.channels).filter(o=>o._isMember(r)).forEach(o=>o._trigger(i,s,a)),this.stateChangeCallbacks.message.forEach(o=>o(n))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(t=>t())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const t=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(t),this.workerRef.onerror=n=>{this.log("worker","worker error",n.message),this.workerRef.terminate()},this.workerRef.onmessage=n=>{n.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(t){this.log("transport","close",t),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(n=>n(t))}_onConnError(t){this.log("transport",`${t}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(n=>n(t))}_triggerChanError(){this.channels.forEach(t=>t._trigger(tt.error))}_appendParams(t,n){if(Object.keys(n).length===0)return t;const r=t.match(/\?/)?"&":"?",i=new URLSearchParams(n);return`${t}${r}${i}`}_workerObjectUrl(t){let n;if(t)n=t;else{const r=new Blob([T0],{type:"application/javascript"});n=URL.createObjectURL(r)}return n}}class yc extends Error{constructor(t){super(t),this.__isStorageError=!0,this.name="StorageError"}}function de(e){return typeof e=="object"&&e!==null&&"__isStorageError"in e}class N0 extends yc{constructor(t,n){super(t),this.name="StorageApiError",this.status=n}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class al extends yc{constructor(t,n){super(t),this.name="StorageUnknownError",this.originalError=n}}var I0=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(a){a(s)})}return new(n||(n=Promise))(function(s,a){function o(u){try{c(r.next(u))}catch(d){a(d)}}function l(u){try{c(r.throw(u))}catch(d){a(d)}}function c(u){u.done?s(u.value):i(u.value).then(o,l)}c((r=r.apply(e,t||[])).next())})};const uh=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>wi(()=>Promise.resolve().then(()=>vr),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},R0=()=>I0(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield wi(()=>Promise.resolve().then(()=>vr),void 0)).Response:Response}),ol=e=>{if(Array.isArray(e))return e.map(n=>ol(n));if(typeof e=="function"||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([n,r])=>{const i=n.replace(/([-_][a-z])/gi,s=>s.toUpperCase().replace(/[-_]/g,""));t[i]=ol(r)}),t};var kn=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(a){a(s)})}return new(n||(n=Promise))(function(s,a){function o(u){try{c(r.next(u))}catch(d){a(d)}}function l(u){try{c(r.throw(u))}catch(d){a(d)}}function c(u){u.done?s(u.value):i(u.value).then(o,l)}c((r=r.apply(e,t||[])).next())})};const Za=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),A0=(e,t,n)=>kn(void 0,void 0,void 0,function*(){const r=yield R0();e instanceof r&&!(n!=null&&n.noResolveJson)?e.json().then(i=>{t(new N0(Za(i),e.status||500))}).catch(i=>{t(new al(Za(i),i))}):t(new al(Za(e),e))}),D0=(e,t,n,r)=>{const i={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?i:(i.headers=Object.assign({"Content-Type":"application/json"},t==null?void 0:t.headers),r&&(i.body=JSON.stringify(r)),Object.assign(Object.assign({},i),n))};function ki(e,t,n,r,i,s){return kn(this,void 0,void 0,function*(){return new Promise((a,o)=>{e(n,D0(t,r,i,s)).then(l=>{if(!l.ok)throw l;return r!=null&&r.noResolveJson?l:l.json()}).then(l=>a(l)).catch(l=>A0(l,o,r))})})}function $s(e,t,n,r){return kn(this,void 0,void 0,function*(){return ki(e,"GET",t,n,r)})}function Nt(e,t,n,r,i){return kn(this,void 0,void 0,function*(){return ki(e,"POST",t,r,i,n)})}function $0(e,t,n,r,i){return kn(this,void 0,void 0,function*(){return ki(e,"PUT",t,r,i,n)})}function z0(e,t,n,r){return kn(this,void 0,void 0,function*(){return ki(e,"HEAD",t,Object.assign(Object.assign({},n),{noResolveJson:!0}),r)})}function dh(e,t,n,r,i){return kn(this,void 0,void 0,function*(){return ki(e,"DELETE",t,r,i,n)})}var Oe=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(a){a(s)})}return new(n||(n=Promise))(function(s,a){function o(u){try{c(r.next(u))}catch(d){a(d)}}function l(u){try{c(r.throw(u))}catch(d){a(d)}}function c(u){u.done?s(u.value):i(u.value).then(o,l)}c((r=r.apply(e,t||[])).next())})};const L0={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Zu={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class U0{constructor(t,n={},r,i){this.url=t,this.headers=n,this.bucketId=r,this.fetch=uh(i)}uploadOrUpdate(t,n,r,i){return Oe(this,void 0,void 0,function*(){try{let s;const a=Object.assign(Object.assign({},Zu),i);let o=Object.assign(Object.assign({},this.headers),t==="POST"&&{"x-upsert":String(a.upsert)});const l=a.metadata;typeof Blob<"u"&&r instanceof Blob?(s=new FormData,s.append("cacheControl",a.cacheControl),l&&s.append("metadata",this.encodeMetadata(l)),s.append("",r)):typeof FormData<"u"&&r instanceof FormData?(s=r,s.append("cacheControl",a.cacheControl),l&&s.append("metadata",this.encodeMetadata(l))):(s=r,o["cache-control"]=`max-age=${a.cacheControl}`,o["content-type"]=a.contentType,l&&(o["x-metadata"]=this.toBase64(this.encodeMetadata(l)))),i!=null&&i.headers&&(o=Object.assign(Object.assign({},o),i.headers));const c=this._removeEmptyFolders(n),u=this._getFinalPath(c),d=yield this.fetch(`${this.url}/object/${u}`,Object.assign({method:t,body:s,headers:o},a!=null&&a.duplex?{duplex:a.duplex}:{})),f=yield d.json();return d.ok?{data:{path:c,id:f.Id,fullPath:f.Key},error:null}:{data:null,error:f}}catch(s){if(de(s))return{data:null,error:s};throw s}})}upload(t,n,r){return Oe(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",t,n,r)})}uploadToSignedUrl(t,n,r,i){return Oe(this,void 0,void 0,function*(){const s=this._removeEmptyFolders(t),a=this._getFinalPath(s),o=new URL(this.url+`/object/upload/sign/${a}`);o.searchParams.set("token",n);try{let l;const c=Object.assign({upsert:Zu.upsert},i),u=Object.assign(Object.assign({},this.headers),{"x-upsert":String(c.upsert)});typeof Blob<"u"&&r instanceof Blob?(l=new FormData,l.append("cacheControl",c.cacheControl),l.append("",r)):typeof FormData<"u"&&r instanceof FormData?(l=r,l.append("cacheControl",c.cacheControl)):(l=r,u["cache-control"]=`max-age=${c.cacheControl}`,u["content-type"]=c.contentType);const d=yield this.fetch(o.toString(),{method:"PUT",body:l,headers:u}),f=yield d.json();return d.ok?{data:{path:s,fullPath:f.Key},error:null}:{data:null,error:f}}catch(l){if(de(l))return{data:null,error:l};throw l}})}createSignedUploadUrl(t,n){return Oe(this,void 0,void 0,function*(){try{let r=this._getFinalPath(t);const i=Object.assign({},this.headers);n!=null&&n.upsert&&(i["x-upsert"]="true");const s=yield Nt(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:i}),a=new URL(this.url+s.url),o=a.searchParams.get("token");if(!o)throw new yc("No token returned by API");return{data:{signedUrl:a.toString(),path:t,token:o},error:null}}catch(r){if(de(r))return{data:null,error:r};throw r}})}update(t,n,r){return Oe(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",t,n,r)})}move(t,n,r){return Oe(this,void 0,void 0,function*(){try{return{data:yield Nt(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(i){if(de(i))return{data:null,error:i};throw i}})}copy(t,n,r){return Oe(this,void 0,void 0,function*(){try{return{data:{path:(yield Nt(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(i){if(de(i))return{data:null,error:i};throw i}})}createSignedUrl(t,n,r){return Oe(this,void 0,void 0,function*(){try{let i=this._getFinalPath(t),s=yield Nt(this.fetch,`${this.url}/object/sign/${i}`,Object.assign({expiresIn:n},r!=null&&r.transform?{transform:r.transform}:{}),{headers:this.headers});const a=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return s={signedUrl:encodeURI(`${this.url}${s.signedURL}${a}`)},{data:s,error:null}}catch(i){if(de(i))return{data:null,error:i};throw i}})}createSignedUrls(t,n,r){return Oe(this,void 0,void 0,function*(){try{const i=yield Nt(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:n,paths:t},{headers:this.headers}),s=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return{data:i.map(a=>Object.assign(Object.assign({},a),{signedUrl:a.signedURL?encodeURI(`${this.url}${a.signedURL}${s}`):null})),error:null}}catch(i){if(de(i))return{data:null,error:i};throw i}})}download(t,n){return Oe(this,void 0,void 0,function*(){const i=typeof(n==null?void 0:n.transform)<"u"?"render/image/authenticated":"object",s=this.transformOptsToQueryString((n==null?void 0:n.transform)||{}),a=s?`?${s}`:"";try{const o=this._getFinalPath(t);return{data:yield(yield $s(this.fetch,`${this.url}/${i}/${o}${a}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(o){if(de(o))return{data:null,error:o};throw o}})}info(t){return Oe(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{const r=yield $s(this.fetch,`${this.url}/object/info/${n}`,{headers:this.headers});return{data:ol(r),error:null}}catch(r){if(de(r))return{data:null,error:r};throw r}})}exists(t){return Oe(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{return yield z0(this.fetch,`${this.url}/object/${n}`,{headers:this.headers}),{data:!0,error:null}}catch(r){if(de(r)&&r instanceof al){const i=r.originalError;if([400,404].includes(i==null?void 0:i.status))return{data:!1,error:r}}throw r}})}getPublicUrl(t,n){const r=this._getFinalPath(t),i=[],s=n!=null&&n.download?`download=${n.download===!0?"":n.download}`:"";s!==""&&i.push(s);const o=typeof(n==null?void 0:n.transform)<"u"?"render/image":"object",l=this.transformOptsToQueryString((n==null?void 0:n.transform)||{});l!==""&&i.push(l);let c=i.join("&");return c!==""&&(c=`?${c}`),{data:{publicUrl:encodeURI(`${this.url}/${o}/public/${r}${c}`)}}}remove(t){return Oe(this,void 0,void 0,function*(){try{return{data:yield dh(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:t},{headers:this.headers}),error:null}}catch(n){if(de(n))return{data:null,error:n};throw n}})}list(t,n,r){return Oe(this,void 0,void 0,function*(){try{const i=Object.assign(Object.assign(Object.assign({},L0),n),{prefix:t||""});return{data:yield Nt(this.fetch,`${this.url}/object/list/${this.bucketId}`,i,{headers:this.headers},r),error:null}}catch(i){if(de(i))return{data:null,error:i};throw i}})}encodeMetadata(t){return JSON.stringify(t)}toBase64(t){return typeof Buffer<"u"?Buffer.from(t).toString("base64"):btoa(t)}_getFinalPath(t){return`${this.bucketId}/${t}`}_removeEmptyFolders(t){return t.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(t){const n=[];return t.width&&n.push(`width=${t.width}`),t.height&&n.push(`height=${t.height}`),t.resize&&n.push(`resize=${t.resize}`),t.format&&n.push(`format=${t.format}`),t.quality&&n.push(`quality=${t.quality}`),n.join("&")}}const F0="2.7.1",M0={"X-Client-Info":`storage-js/${F0}`};var On=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(a){a(s)})}return new(n||(n=Promise))(function(s,a){function o(u){try{c(r.next(u))}catch(d){a(d)}}function l(u){try{c(r.throw(u))}catch(d){a(d)}}function c(u){u.done?s(u.value):i(u.value).then(o,l)}c((r=r.apply(e,t||[])).next())})};class B0{constructor(t,n={},r){this.url=t,this.headers=Object.assign(Object.assign({},M0),n),this.fetch=uh(r)}listBuckets(){return On(this,void 0,void 0,function*(){try{return{data:yield $s(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(t){if(de(t))return{data:null,error:t};throw t}})}getBucket(t){return On(this,void 0,void 0,function*(){try{return{data:yield $s(this.fetch,`${this.url}/bucket/${t}`,{headers:this.headers}),error:null}}catch(n){if(de(n))return{data:null,error:n};throw n}})}createBucket(t,n={public:!1}){return On(this,void 0,void 0,function*(){try{return{data:yield Nt(this.fetch,`${this.url}/bucket`,{id:t,name:t,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(de(r))return{data:null,error:r};throw r}})}updateBucket(t,n){return On(this,void 0,void 0,function*(){try{return{data:yield $0(this.fetch,`${this.url}/bucket/${t}`,{id:t,name:t,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(de(r))return{data:null,error:r};throw r}})}emptyBucket(t){return On(this,void 0,void 0,function*(){try{return{data:yield Nt(this.fetch,`${this.url}/bucket/${t}/empty`,{},{headers:this.headers}),error:null}}catch(n){if(de(n))return{data:null,error:n};throw n}})}deleteBucket(t){return On(this,void 0,void 0,function*(){try{return{data:yield dh(this.fetch,`${this.url}/bucket/${t}`,{},{headers:this.headers}),error:null}}catch(n){if(de(n))return{data:null,error:n};throw n}})}}class W0 extends B0{constructor(t,n={},r){super(t,n,r)}from(t){return new U0(this.url,this.headers,t,this.fetch)}}const q0="2.50.2";let Rr="";typeof Deno<"u"?Rr="deno":typeof document<"u"?Rr="web":typeof navigator<"u"&&navigator.product==="ReactNative"?Rr="react-native":Rr="node";const H0={"X-Client-Info":`supabase-js-${Rr}/${q0}`},V0={headers:H0},K0={schema:"public"},Q0={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},G0={};var J0=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(a){a(s)})}return new(n||(n=Promise))(function(s,a){function o(u){try{c(r.next(u))}catch(d){a(d)}}function l(u){try{c(r.throw(u))}catch(d){a(d)}}function c(u){u.done?s(u.value):i(u.value).then(o,l)}c((r=r.apply(e,t||[])).next())})};const Y0=e=>{let t;return e?t=e:typeof fetch>"u"?t=Gf:t=fetch,(...n)=>t(...n)},X0=()=>typeof Headers>"u"?Jf:Headers,Z0=(e,t,n)=>{const r=Y0(n),i=X0();return(s,a)=>J0(void 0,void 0,void 0,function*(){var o;const l=(o=yield t())!==null&&o!==void 0?o:e;let c=new i(a==null?void 0:a.headers);return c.has("apikey")||c.set("apikey",e),c.has("Authorization")||c.set("Authorization",`Bearer ${l}`),r(s,Object.assign(Object.assign({},a),{headers:c}))})};var ex=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(a){a(s)})}return new(n||(n=Promise))(function(s,a){function o(u){try{c(r.next(u))}catch(d){a(d)}}function l(u){try{c(r.throw(u))}catch(d){a(d)}}function c(u){u.done?s(u.value):i(u.value).then(o,l)}c((r=r.apply(e,t||[])).next())})};function tx(e){return e.endsWith("/")?e:e+"/"}function nx(e,t){var n,r;const{db:i,auth:s,realtime:a,global:o}=e,{db:l,auth:c,realtime:u,global:d}=t,f={db:Object.assign(Object.assign({},l),i),auth:Object.assign(Object.assign({},c),s),realtime:Object.assign(Object.assign({},u),a),global:Object.assign(Object.assign(Object.assign({},d),o),{headers:Object.assign(Object.assign({},(n=d==null?void 0:d.headers)!==null&&n!==void 0?n:{}),(r=o==null?void 0:o.headers)!==null&&r!==void 0?r:{})}),accessToken:()=>ex(this,void 0,void 0,function*(){return""})};return e.accessToken?f.accessToken=e.accessToken:delete f.accessToken,f}const ph="2.70.0",Dn=30*1e3,ll=3,eo=ll*Dn,rx="http://localhost:9999",ix="supabase.auth.token",sx={"X-Client-Info":`gotrue-js/${ph}`},cl="X-Supabase-Api-Version",fh={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},ax=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,ox=6e5;class xc extends Error{constructor(t,n,r){super(t),this.__isAuthError=!0,this.name="AuthError",this.status=n,this.code=r}}function D(e){return typeof e=="object"&&e!==null&&"__isAuthError"in e}class lx extends xc{constructor(t,n,r){super(t,n,r),this.name="AuthApiError",this.status=n,this.code=r}}function cx(e){return D(e)&&e.name==="AuthApiError"}class hh extends xc{constructor(t,n){super(t),this.name="AuthUnknownError",this.originalError=n}}class en extends xc{constructor(t,n,r,i){super(t,r,i),this.name=n,this.status=r}}class Pt extends en{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function ux(e){return D(e)&&e.name==="AuthSessionMissingError"}class Hi extends en{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class Vi extends en{constructor(t){super(t,"AuthInvalidCredentialsError",400,void 0)}}class Ki extends en{constructor(t,n=null){super(t,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function dx(e){return D(e)&&e.name==="AuthImplicitGrantRedirectError"}class ed extends en{constructor(t,n=null){super(t,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class ul extends en{constructor(t,n){super(t,"AuthRetryableFetchError",n,void 0)}}function to(e){return D(e)&&e.name==="AuthRetryableFetchError"}class td extends en{constructor(t,n,r){super(t,"AuthWeakPasswordError",n,"weak_password"),this.reasons=r}}class Hr extends en{constructor(t){super(t,"AuthInvalidJwtError",400,"invalid_jwt")}}const zs="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),nd=` 	
\r=`.split(""),px=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<nd.length;t+=1)e[nd[t].charCodeAt(0)]=-2;for(let t=0;t<zs.length;t+=1)e[zs[t].charCodeAt(0)]=t;return e})();function rd(e,t,n){if(e!==null)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;n(zs[r]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;n(zs[r]),t.queuedBits-=6}}function mh(e,t,n){const r=px[e];if(r>-1)for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)n(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else{if(r===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}}function id(e){const t=[],n=a=>{t.push(String.fromCodePoint(a))},r={utf8seq:0,codepoint:0},i={queue:0,queuedBits:0},s=a=>{mx(a,r,n)};for(let a=0;a<e.length;a+=1)mh(e.charCodeAt(a),i,s);return t.join("")}function fx(e,t){if(e<=127){t(e);return}else if(e<=2047){t(192|e>>6),t(128|e&63);return}else if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|e&63);return}else if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|e&63);return}throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}function hx(e,t){for(let n=0;n<e.length;n+=1){let r=e.charCodeAt(n);if(r>55295&&r<=56319){const i=(r-55296)*1024&65535;r=(e.charCodeAt(n+1)-56320&65535|i)+65536,n+=1}fx(r,t)}}function mx(e,t,n){if(t.utf8seq===0){if(e<=127){n(e);return}for(let r=1;r<6;r+=1)if(!(e>>7-r&1)){t.utf8seq=r;break}if(t.utf8seq===2)t.codepoint=e&31;else if(t.utf8seq===3)t.codepoint=e&15;else if(t.utf8seq===4)t.codepoint=e&7;else throw new Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|e&63,t.utf8seq-=1,t.utf8seq===0&&n(t.codepoint)}}function vx(e){const t=[],n={queue:0,queuedBits:0},r=i=>{t.push(i)};for(let i=0;i<e.length;i+=1)mh(e.charCodeAt(i),n,r);return new Uint8Array(t)}function gx(e){const t=[];return hx(e,n=>t.push(n)),new Uint8Array(t)}function yx(e){const t=[],n={queue:0,queuedBits:0},r=i=>{t.push(i)};return e.forEach(i=>rd(i,n,r)),rd(null,n,r),t.join("")}function xx(e){return Math.round(Date.now()/1e3)+e}function wx(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=Math.random()*16|0;return(e=="x"?t:t&3|8).toString(16)})}const Xe=()=>typeof window<"u"&&typeof document<"u",rn={tested:!1,writable:!1},Vr=()=>{if(!Xe())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if(rn.tested)return rn.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),rn.tested=!0,rn.writable=!0}catch{rn.tested=!0,rn.writable=!1}return rn.writable};function _x(e){const t={},n=new URL(e);if(n.hash&&n.hash[0]==="#")try{new URLSearchParams(n.hash.substring(1)).forEach((i,s)=>{t[s]=i})}catch{}return n.searchParams.forEach((r,i)=>{t[i]=r}),t}const vh=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>wi(()=>Promise.resolve().then(()=>vr),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},kx=e=>typeof e=="object"&&e!==null&&"status"in e&&"ok"in e&&"json"in e&&typeof e.json=="function",gh=async(e,t,n)=>{await e.setItem(t,JSON.stringify(n))},Qi=async(e,t)=>{const n=await e.getItem(t);if(!n)return null;try{return JSON.parse(n)}catch{return n}},Gi=async(e,t)=>{await e.removeItem(t)};class fa{constructor(){this.promise=new fa.promiseConstructor((t,n)=>{this.resolve=t,this.reject=n})}}fa.promiseConstructor=Promise;function no(e){const t=e.split(".");if(t.length!==3)throw new Hr("Invalid JWT structure");for(let r=0;r<t.length;r++)if(!ax.test(t[r]))throw new Hr("JWT not in base64url format");return{header:JSON.parse(id(t[0])),payload:JSON.parse(id(t[1])),signature:vx(t[2]),raw:{header:t[0],payload:t[1]}}}async function Sx(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function bx(e,t){return new Promise((r,i)=>{(async()=>{for(let s=0;s<1/0;s++)try{const a=await e(s);if(!t(s,null,a)){r(a);return}}catch(a){if(!t(s,a)){i(a);return}}})()})}function jx(e){return("0"+e.toString(16)).substr(-2)}function Ex(){const t=new Uint32Array(56);if(typeof crypto>"u"){const n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",r=n.length;let i="";for(let s=0;s<56;s++)i+=n.charAt(Math.floor(Math.random()*r));return i}return crypto.getRandomValues(t),Array.from(t,jx).join("")}async function Cx(e){const n=new TextEncoder().encode(e),r=await crypto.subtle.digest("SHA-256",n),i=new Uint8Array(r);return Array.from(i).map(s=>String.fromCharCode(s)).join("")}async function Px(e){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const n=await Cx(e);return btoa(n).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function Nn(e,t,n=!1){const r=Ex();let i=r;n&&(i+="/PASSWORD_RECOVERY"),await gh(e,`${t}-code-verifier`,i);const s=await Px(r);return[s,r===s?"plain":"s256"]}const Tx=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function Ox(e){const t=e.headers.get(cl);if(!t||!t.match(Tx))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch{return null}}function Nx(e){if(!e)throw new Error("Missing exp claim");const t=Math.floor(Date.now()/1e3);if(e<=t)throw new Error("JWT has expired")}function Ix(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}const Rx=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function In(e){if(!Rx.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var Ax=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n};const on=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Dx=[502,503,504];async function sd(e){var t;if(!kx(e))throw new ul(on(e),0);if(Dx.includes(e.status))throw new ul(on(e),e.status);let n;try{n=await e.json()}catch(s){throw new hh(on(s),s)}let r;const i=Ox(e);if(i&&i.getTime()>=fh["2024-01-01"].timestamp&&typeof n=="object"&&n&&typeof n.code=="string"?r=n.code:typeof n=="object"&&n&&typeof n.error_code=="string"&&(r=n.error_code),r){if(r==="weak_password")throw new td(on(n),e.status,((t=n.weak_password)===null||t===void 0?void 0:t.reasons)||[]);if(r==="session_not_found")throw new Pt}else if(typeof n=="object"&&n&&typeof n.weak_password=="object"&&n.weak_password&&Array.isArray(n.weak_password.reasons)&&n.weak_password.reasons.length&&n.weak_password.reasons.reduce((s,a)=>s&&typeof a=="string",!0))throw new td(on(n),e.status,n.weak_password.reasons);throw new lx(on(n),e.status||500,r)}const $x=(e,t,n,r)=>{const i={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?i:(i.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},t==null?void 0:t.headers),i.body=JSON.stringify(r),Object.assign(Object.assign({},i),n))};async function z(e,t,n,r){var i;const s=Object.assign({},r==null?void 0:r.headers);s[cl]||(s[cl]=fh["2024-01-01"].name),r!=null&&r.jwt&&(s.Authorization=`Bearer ${r.jwt}`);const a=(i=r==null?void 0:r.query)!==null&&i!==void 0?i:{};r!=null&&r.redirectTo&&(a.redirect_to=r.redirectTo);const o=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",l=await zx(e,t,n+o,{headers:s,noResolveJson:r==null?void 0:r.noResolveJson},{},r==null?void 0:r.body);return r!=null&&r.xform?r==null?void 0:r.xform(l):{data:Object.assign({},l),error:null}}async function zx(e,t,n,r,i,s){const a=$x(t,r,i,s);let o;try{o=await e(n,Object.assign({},a))}catch(l){throw console.error(l),new ul(on(l),0)}if(o.ok||await sd(o),r!=null&&r.noResolveJson)return o;try{return await o.json()}catch(l){await sd(l)}}function ft(e){var t;let n=null;Mx(e)&&(n=Object.assign({},e),e.expires_at||(n.expires_at=xx(e.expires_in)));const r=(t=e.user)!==null&&t!==void 0?t:e;return{data:{session:n,user:r},error:null}}function ad(e){const t=ft(e);return!t.error&&e.weak_password&&typeof e.weak_password=="object"&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&typeof e.weak_password.message=="string"&&e.weak_password.reasons.reduce((n,r)=>n&&typeof r=="string",!0)&&(t.data.weak_password=e.weak_password),t}function At(e){var t;return{data:{user:(t=e.user)!==null&&t!==void 0?t:e},error:null}}function Lx(e){return{data:e,error:null}}function Ux(e){const{action_link:t,email_otp:n,hashed_token:r,redirect_to:i,verification_type:s}=e,a=Ax(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),o={action_link:t,email_otp:n,hashed_token:r,redirect_to:i,verification_type:s},l=Object.assign({},a);return{data:{properties:o,user:l},error:null}}function Fx(e){return e}function Mx(e){return e.access_token&&e.refresh_token&&e.expires_in}const ro=["global","local","others"];var Bx=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n};class Wx{constructor({url:t="",headers:n={},fetch:r}){this.url=t,this.headers=n,this.fetch=vh(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(t,n=ro[0]){if(ro.indexOf(n)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${ro.join(", ")}`);try{return await z(this.fetch,"POST",`${this.url}/logout?scope=${n}`,{headers:this.headers,jwt:t,noResolveJson:!0}),{data:null,error:null}}catch(r){if(D(r))return{data:null,error:r};throw r}}async inviteUserByEmail(t,n={}){try{return await z(this.fetch,"POST",`${this.url}/invite`,{body:{email:t,data:n.data},headers:this.headers,redirectTo:n.redirectTo,xform:At})}catch(r){if(D(r))return{data:{user:null},error:r};throw r}}async generateLink(t){try{const{options:n}=t,r=Bx(t,["options"]),i=Object.assign(Object.assign({},r),n);return"newEmail"in r&&(i.new_email=r==null?void 0:r.newEmail,delete i.newEmail),await z(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:i,headers:this.headers,xform:Ux,redirectTo:n==null?void 0:n.redirectTo})}catch(n){if(D(n))return{data:{properties:null,user:null},error:n};throw n}}async createUser(t){try{return await z(this.fetch,"POST",`${this.url}/admin/users`,{body:t,headers:this.headers,xform:At})}catch(n){if(D(n))return{data:{user:null},error:n};throw n}}async listUsers(t){var n,r,i,s,a,o,l;try{const c={nextPage:null,lastPage:0,total:0},u=await z(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(r=(n=t==null?void 0:t.page)===null||n===void 0?void 0:n.toString())!==null&&r!==void 0?r:"",per_page:(s=(i=t==null?void 0:t.perPage)===null||i===void 0?void 0:i.toString())!==null&&s!==void 0?s:""},xform:Fx});if(u.error)throw u.error;const d=await u.json(),f=(a=u.headers.get("x-total-count"))!==null&&a!==void 0?a:0,g=(l=(o=u.headers.get("link"))===null||o===void 0?void 0:o.split(","))!==null&&l!==void 0?l:[];return g.length>0&&(g.forEach(y=>{const x=parseInt(y.split(";")[0].split("=")[1].substring(0,1)),k=JSON.parse(y.split(";")[1].split("=")[1]);c[`${k}Page`]=x}),c.total=parseInt(f)),{data:Object.assign(Object.assign({},d),c),error:null}}catch(c){if(D(c))return{data:{users:[]},error:c};throw c}}async getUserById(t){In(t);try{return await z(this.fetch,"GET",`${this.url}/admin/users/${t}`,{headers:this.headers,xform:At})}catch(n){if(D(n))return{data:{user:null},error:n};throw n}}async updateUserById(t,n){In(t);try{return await z(this.fetch,"PUT",`${this.url}/admin/users/${t}`,{body:n,headers:this.headers,xform:At})}catch(r){if(D(r))return{data:{user:null},error:r};throw r}}async deleteUser(t,n=!1){In(t);try{return await z(this.fetch,"DELETE",`${this.url}/admin/users/${t}`,{headers:this.headers,body:{should_soft_delete:n},xform:At})}catch(r){if(D(r))return{data:{user:null},error:r};throw r}}async _listFactors(t){In(t.userId);try{const{data:n,error:r}=await z(this.fetch,"GET",`${this.url}/admin/users/${t.userId}/factors`,{headers:this.headers,xform:i=>({data:{factors:i},error:null})});return{data:n,error:r}}catch(n){if(D(n))return{data:null,error:n};throw n}}async _deleteFactor(t){In(t.userId),In(t.id);try{return{data:await z(this.fetch,"DELETE",`${this.url}/admin/users/${t.userId}/factors/${t.id}`,{headers:this.headers}),error:null}}catch(n){if(D(n))return{data:null,error:n};throw n}}}const qx={getItem:e=>Vr()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{Vr()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{Vr()&&globalThis.localStorage.removeItem(e)}};function od(e={}){return{getItem:t=>e[t]||null,setItem:(t,n)=>{e[t]=n},removeItem:t=>{delete e[t]}}}function Hx(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}const Rn={debug:!!(globalThis&&Vr()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class yh extends Error{constructor(t){super(t),this.isAcquireTimeout=!0}}class Vx extends yh{}async function Kx(e,t,n){Rn.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const r=new globalThis.AbortController;return t>0&&setTimeout(()=>{r.abort(),Rn.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,t===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},async i=>{if(i){Rn.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,i.name);try{return await n()}finally{Rn.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,i.name)}}else{if(t===0)throw Rn.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new Vx(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(Rn.debug)try{const s=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(s,null,"  "))}catch(s){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",s)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await n()}}))}Hx();const Qx={url:rx,storageKey:ix,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:sx,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function ld(e,t,n){return await n()}class pi{constructor(t){var n,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=pi.nextInstanceID,pi.nextInstanceID+=1,this.instanceID>0&&Xe()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const i=Object.assign(Object.assign({},Qx),t);if(this.logDebugMessages=!!i.debug,typeof i.debug=="function"&&(this.logger=i.debug),this.persistSession=i.persistSession,this.storageKey=i.storageKey,this.autoRefreshToken=i.autoRefreshToken,this.admin=new Wx({url:i.url,headers:i.headers,fetch:i.fetch}),this.url=i.url,this.headers=i.headers,this.fetch=vh(i.fetch),this.lock=i.lock||ld,this.detectSessionInUrl=i.detectSessionInUrl,this.flowType=i.flowType,this.hasCustomAuthorizationHeader=i.hasCustomAuthorizationHeader,i.lock?this.lock=i.lock:Xe()&&(!((n=globalThis==null?void 0:globalThis.navigator)===null||n===void 0)&&n.locks)?this.lock=Kx:this.lock=ld,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?i.storage?this.storage=i.storage:Vr()?this.storage=qx:(this.memoryStorage={},this.storage=od(this.memoryStorage)):(this.memoryStorage={},this.storage=od(this.memoryStorage)),Xe()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(s){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",s)}(r=this.broadcastChannel)===null||r===void 0||r.addEventListener("message",async s=>{this._debug("received broadcast notification from other tab or client",s),await this._notifyAllSubscribers(s.data.event,s.data.session,!1)})}this.initialize()}_debug(...t){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${ph}) ${new Date().toISOString()}`,...t),this}async initialize(){return this.initializePromise?await this.initializePromise:(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))(),await this.initializePromise)}async _initialize(){var t;try{const n=_x(window.location.href);let r="none";if(this._isImplicitGrantCallback(n)?r="implicit":await this._isPKCECallback(n)&&(r="pkce"),Xe()&&this.detectSessionInUrl&&r!=="none"){const{data:i,error:s}=await this._getSessionFromURL(n,r);if(s){if(this._debug("#_initialize()","error detecting session from URL",s),dx(s)){const l=(t=s.details)===null||t===void 0?void 0:t.code;if(l==="identity_already_exists"||l==="identity_not_found"||l==="single_identity_not_deletable")return{error:s}}return await this._removeSession(),{error:s}}const{session:a,redirectType:o}=i;return this._debug("#_initialize()","detected session in URL",a,"redirect type",o),await this._saveSession(a),setTimeout(async()=>{o==="recovery"?await this._notifyAllSubscribers("PASSWORD_RECOVERY",a):await this._notifyAllSubscribers("SIGNED_IN",a)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(n){return D(n)?{error:n}:{error:new hh("Unexpected error during initialization",n)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(t){var n,r,i;try{const s=await z(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(r=(n=t==null?void 0:t.options)===null||n===void 0?void 0:n.data)!==null&&r!==void 0?r:{},gotrue_meta_security:{captcha_token:(i=t==null?void 0:t.options)===null||i===void 0?void 0:i.captchaToken}},xform:ft}),{data:a,error:o}=s;if(o||!a)return{data:{user:null,session:null},error:o};const l=a.session,c=a.user;return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:c,session:l},error:null}}catch(s){if(D(s))return{data:{user:null,session:null},error:s};throw s}}async signUp(t){var n,r,i;try{let s;if("email"in t){const{email:u,password:d,options:f}=t;let g=null,y=null;this.flowType==="pkce"&&([g,y]=await Nn(this.storage,this.storageKey)),s=await z(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:f==null?void 0:f.emailRedirectTo,body:{email:u,password:d,data:(n=f==null?void 0:f.data)!==null&&n!==void 0?n:{},gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken},code_challenge:g,code_challenge_method:y},xform:ft})}else if("phone"in t){const{phone:u,password:d,options:f}=t;s=await z(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:u,password:d,data:(r=f==null?void 0:f.data)!==null&&r!==void 0?r:{},channel:(i=f==null?void 0:f.channel)!==null&&i!==void 0?i:"sms",gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken}},xform:ft})}else throw new Vi("You must provide either an email or phone number and a password");const{data:a,error:o}=s;if(o||!a)return{data:{user:null,session:null},error:o};const l=a.session,c=a.user;return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:c,session:l},error:null}}catch(s){if(D(s))return{data:{user:null,session:null},error:s};throw s}}async signInWithPassword(t){try{let n;if("email"in t){const{email:s,password:a,options:o}=t;n=await z(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:s,password:a,gotrue_meta_security:{captcha_token:o==null?void 0:o.captchaToken}},xform:ad})}else if("phone"in t){const{phone:s,password:a,options:o}=t;n=await z(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:s,password:a,gotrue_meta_security:{captcha_token:o==null?void 0:o.captchaToken}},xform:ad})}else throw new Vi("You must provide either an email or phone number and a password");const{data:r,error:i}=n;return i?{data:{user:null,session:null},error:i}:!r||!r.session||!r.user?{data:{user:null,session:null},error:new Hi}:(r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:i})}catch(n){if(D(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOAuth(t){var n,r,i,s;return await this._handleProviderSignIn(t.provider,{redirectTo:(n=t.options)===null||n===void 0?void 0:n.redirectTo,scopes:(r=t.options)===null||r===void 0?void 0:r.scopes,queryParams:(i=t.options)===null||i===void 0?void 0:i.queryParams,skipBrowserRedirect:(s=t.options)===null||s===void 0?void 0:s.skipBrowserRedirect})}async exchangeCodeForSession(t){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(t))}async signInWithWeb3(t){const{chain:n}=t;if(n==="solana")return await this.signInWithSolana(t);throw new Error(`@supabase/auth-js: Unsupported chain "${n}"`)}async signInWithSolana(t){var n,r,i,s,a,o,l,c,u,d,f,g;let y,x;if("message"in t)y=t.message,x=t.signature;else{const{chain:k,wallet:v,statement:m,options:h}=t;let w;if(Xe())if(typeof v=="object")w=v;else{const b=window;if("solana"in b&&typeof b.solana=="object"&&("signIn"in b.solana&&typeof b.solana.signIn=="function"||"signMessage"in b.solana&&typeof b.solana.signMessage=="function"))w=b.solana;else throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if(typeof v!="object"||!(h!=null&&h.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");w=v}const S=new URL((n=h==null?void 0:h.url)!==null&&n!==void 0?n:window.location.href);if("signIn"in w&&w.signIn){const b=await w.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},h==null?void 0:h.signInWithSolana),{version:"1",domain:S.host,uri:S.href}),m?{statement:m}:null));let E;if(Array.isArray(b)&&b[0]&&typeof b[0]=="object")E=b[0];else if(b&&typeof b=="object"&&"signedMessage"in b&&"signature"in b)E=b;else throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in E&&"signature"in E&&(typeof E.signedMessage=="string"||E.signedMessage instanceof Uint8Array)&&E.signature instanceof Uint8Array)y=typeof E.signedMessage=="string"?E.signedMessage:new TextDecoder().decode(E.signedMessage),x=E.signature;else throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in w)||typeof w.signMessage!="function"||!("publicKey"in w)||typeof w!="object"||!w.publicKey||!("toBase58"in w.publicKey)||typeof w.publicKey.toBase58!="function")throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");y=[`${S.host} wants you to sign in with your Solana account:`,w.publicKey.toBase58(),...m?["",m,""]:[""],"Version: 1",`URI: ${S.href}`,`Issued At: ${(i=(r=h==null?void 0:h.signInWithSolana)===null||r===void 0?void 0:r.issuedAt)!==null&&i!==void 0?i:new Date().toISOString()}`,...!((s=h==null?void 0:h.signInWithSolana)===null||s===void 0)&&s.notBefore?[`Not Before: ${h.signInWithSolana.notBefore}`]:[],...!((a=h==null?void 0:h.signInWithSolana)===null||a===void 0)&&a.expirationTime?[`Expiration Time: ${h.signInWithSolana.expirationTime}`]:[],...!((o=h==null?void 0:h.signInWithSolana)===null||o===void 0)&&o.chainId?[`Chain ID: ${h.signInWithSolana.chainId}`]:[],...!((l=h==null?void 0:h.signInWithSolana)===null||l===void 0)&&l.nonce?[`Nonce: ${h.signInWithSolana.nonce}`]:[],...!((c=h==null?void 0:h.signInWithSolana)===null||c===void 0)&&c.requestId?[`Request ID: ${h.signInWithSolana.requestId}`]:[],...!((d=(u=h==null?void 0:h.signInWithSolana)===null||u===void 0?void 0:u.resources)===null||d===void 0)&&d.length?["Resources",...h.signInWithSolana.resources.map(E=>`- ${E}`)]:[]].join(`
`);const b=await w.signMessage(new TextEncoder().encode(y),"utf8");if(!b||!(b instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");x=b}}try{const{data:k,error:v}=await z(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:y,signature:yx(x)},!((f=t.options)===null||f===void 0)&&f.captchaToken?{gotrue_meta_security:{captcha_token:(g=t.options)===null||g===void 0?void 0:g.captchaToken}}:null),xform:ft});if(v)throw v;return!k||!k.session||!k.user?{data:{user:null,session:null},error:new Hi}:(k.session&&(await this._saveSession(k.session),await this._notifyAllSubscribers("SIGNED_IN",k.session)),{data:Object.assign({},k),error:v})}catch(k){if(D(k))return{data:{user:null,session:null},error:k};throw k}}async _exchangeCodeForSession(t){const n=await Qi(this.storage,`${this.storageKey}-code-verifier`),[r,i]=(n??"").split("/");try{const{data:s,error:a}=await z(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:t,code_verifier:r},xform:ft});if(await Gi(this.storage,`${this.storageKey}-code-verifier`),a)throw a;return!s||!s.session||!s.user?{data:{user:null,session:null,redirectType:null},error:new Hi}:(s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:Object.assign(Object.assign({},s),{redirectType:i??null}),error:a})}catch(s){if(D(s))return{data:{user:null,session:null,redirectType:null},error:s};throw s}}async signInWithIdToken(t){try{const{options:n,provider:r,token:i,access_token:s,nonce:a}=t,o=await z(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:i,access_token:s,nonce:a,gotrue_meta_security:{captcha_token:n==null?void 0:n.captchaToken}},xform:ft}),{data:l,error:c}=o;return c?{data:{user:null,session:null},error:c}:!l||!l.session||!l.user?{data:{user:null,session:null},error:new Hi}:(l.session&&(await this._saveSession(l.session),await this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:l,error:c})}catch(n){if(D(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOtp(t){var n,r,i,s,a;try{if("email"in t){const{email:o,options:l}=t;let c=null,u=null;this.flowType==="pkce"&&([c,u]=await Nn(this.storage,this.storageKey));const{error:d}=await z(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:o,data:(n=l==null?void 0:l.data)!==null&&n!==void 0?n:{},create_user:(r=l==null?void 0:l.shouldCreateUser)!==null&&r!==void 0?r:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},code_challenge:c,code_challenge_method:u},redirectTo:l==null?void 0:l.emailRedirectTo});return{data:{user:null,session:null},error:d}}if("phone"in t){const{phone:o,options:l}=t,{data:c,error:u}=await z(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:o,data:(i=l==null?void 0:l.data)!==null&&i!==void 0?i:{},create_user:(s=l==null?void 0:l.shouldCreateUser)!==null&&s!==void 0?s:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},channel:(a=l==null?void 0:l.channel)!==null&&a!==void 0?a:"sms"}});return{data:{user:null,session:null,messageId:c==null?void 0:c.message_id},error:u}}throw new Vi("You must provide either an email or phone number.")}catch(o){if(D(o))return{data:{user:null,session:null},error:o};throw o}}async verifyOtp(t){var n,r;try{let i,s;"options"in t&&(i=(n=t.options)===null||n===void 0?void 0:n.redirectTo,s=(r=t.options)===null||r===void 0?void 0:r.captchaToken);const{data:a,error:o}=await z(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},t),{gotrue_meta_security:{captcha_token:s}}),redirectTo:i,xform:ft});if(o)throw o;if(!a)throw new Error("An error occurred on token verification.");const l=a.session,c=a.user;return l!=null&&l.access_token&&(await this._saveSession(l),await this._notifyAllSubscribers(t.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",l)),{data:{user:c,session:l},error:null}}catch(i){if(D(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithSSO(t){var n,r,i;try{let s=null,a=null;return this.flowType==="pkce"&&([s,a]=await Nn(this.storage,this.storageKey)),await z(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in t?{provider_id:t.providerId}:null),"domain"in t?{domain:t.domain}:null),{redirect_to:(r=(n=t.options)===null||n===void 0?void 0:n.redirectTo)!==null&&r!==void 0?r:void 0}),!((i=t==null?void 0:t.options)===null||i===void 0)&&i.captchaToken?{gotrue_meta_security:{captcha_token:t.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:s,code_challenge_method:a}),headers:this.headers,xform:Lx})}catch(s){if(D(s))return{data:null,error:s};throw s}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async t=>{const{data:{session:n},error:r}=t;if(r)throw r;if(!n)throw new Pt;const{error:i}=await z(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:n.access_token});return{data:{user:null,session:null},error:i}})}catch(t){if(D(t))return{data:{user:null,session:null},error:t};throw t}}async resend(t){try{const n=`${this.url}/resend`;if("email"in t){const{email:r,type:i,options:s}=t,{error:a}=await z(this.fetch,"POST",n,{headers:this.headers,body:{email:r,type:i,gotrue_meta_security:{captcha_token:s==null?void 0:s.captchaToken}},redirectTo:s==null?void 0:s.emailRedirectTo});return{data:{user:null,session:null},error:a}}else if("phone"in t){const{phone:r,type:i,options:s}=t,{data:a,error:o}=await z(this.fetch,"POST",n,{headers:this.headers,body:{phone:r,type:i,gotrue_meta_security:{captcha_token:s==null?void 0:s.captchaToken}}});return{data:{user:null,session:null,messageId:a==null?void 0:a.message_id},error:o}}throw new Vi("You must provide either an email or phone number and a type")}catch(n){if(D(n))return{data:{user:null,session:null},error:n};throw n}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async n=>n))}async _acquireLock(t,n){this._debug("#_acquireLock","begin",t);try{if(this.lockAcquired){const r=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),i=(async()=>(await r,await n()))();return this.pendingInLock.push((async()=>{try{await i}catch{}})()),i}return await this.lock(`lock:${this.storageKey}`,t,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const r=n();for(this.pendingInLock.push((async()=>{try{await r}catch{}})()),await r;this.pendingInLock.length;){const i=[...this.pendingInLock];await Promise.all(i),this.pendingInLock.splice(0,i.length)}return await r}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(t){this._debug("#_useSession","begin");try{const n=await this.__loadSession();return await t(n)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let t=null;const n=await Qi(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",n),n!==null&&(this._isValidSession(n)?t=n:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!t)return{data:{session:null},error:null};const r=t.expires_at?t.expires_at*1e3-Date.now()<eo:!1;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",t.expires_at),!r){if(this.storage.isServer){let a=this.suppressGetSessionWarning;t=new Proxy(t,{get:(l,c,u)=>(!a&&c==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),a=!0,this.suppressGetSessionWarning=!0),Reflect.get(l,c,u))})}return{data:{session:t},error:null}}const{session:i,error:s}=await this._callRefreshToken(t.refresh_token);return s?{data:{session:null},error:s}:{data:{session:i},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(t){return t?await this._getUser(t):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(t){try{return t?await z(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:t,xform:At}):await this._useSession(async n=>{var r,i,s;const{data:a,error:o}=n;if(o)throw o;return!(!((r=a.session)===null||r===void 0)&&r.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new Pt}:await z(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(s=(i=a.session)===null||i===void 0?void 0:i.access_token)!==null&&s!==void 0?s:void 0,xform:At})})}catch(n){if(D(n))return ux(n)&&(await this._removeSession(),await Gi(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:n};throw n}}async updateUser(t,n={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(t,n))}async _updateUser(t,n={}){try{return await this._useSession(async r=>{const{data:i,error:s}=r;if(s)throw s;if(!i.session)throw new Pt;const a=i.session;let o=null,l=null;this.flowType==="pkce"&&t.email!=null&&([o,l]=await Nn(this.storage,this.storageKey));const{data:c,error:u}=await z(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:n==null?void 0:n.emailRedirectTo,body:Object.assign(Object.assign({},t),{code_challenge:o,code_challenge_method:l}),jwt:a.access_token,xform:At});if(u)throw u;return a.user=c.user,await this._saveSession(a),await this._notifyAllSubscribers("USER_UPDATED",a),{data:{user:a.user},error:null}})}catch(r){if(D(r))return{data:{user:null},error:r};throw r}}async setSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(t))}async _setSession(t){try{if(!t.access_token||!t.refresh_token)throw new Pt;const n=Date.now()/1e3;let r=n,i=!0,s=null;const{payload:a}=no(t.access_token);if(a.exp&&(r=a.exp,i=r<=n),i){const{session:o,error:l}=await this._callRefreshToken(t.refresh_token);if(l)return{data:{user:null,session:null},error:l};if(!o)return{data:{user:null,session:null},error:null};s=o}else{const{data:o,error:l}=await this._getUser(t.access_token);if(l)throw l;s={access_token:t.access_token,refresh_token:t.refresh_token,user:o.user,token_type:"bearer",expires_in:r-n,expires_at:r},await this._saveSession(s),await this._notifyAllSubscribers("SIGNED_IN",s)}return{data:{user:s.user,session:s},error:null}}catch(n){if(D(n))return{data:{session:null,user:null},error:n};throw n}}async refreshSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(t))}async _refreshSession(t){try{return await this._useSession(async n=>{var r;if(!t){const{data:a,error:o}=n;if(o)throw o;t=(r=a.session)!==null&&r!==void 0?r:void 0}if(!(t!=null&&t.refresh_token))throw new Pt;const{session:i,error:s}=await this._callRefreshToken(t.refresh_token);return s?{data:{user:null,session:null},error:s}:i?{data:{user:i.user,session:i},error:null}:{data:{user:null,session:null},error:null}})}catch(n){if(D(n))return{data:{user:null,session:null},error:n};throw n}}async _getSessionFromURL(t,n){try{if(!Xe())throw new Ki("No browser detected.");if(t.error||t.error_description||t.error_code)throw new Ki(t.error_description||"Error in URL with unspecified error_description",{error:t.error||"unspecified_error",code:t.error_code||"unspecified_code"});switch(n){case"implicit":if(this.flowType==="pkce")throw new ed("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new Ki("Not a valid implicit grant flow url.");break;default:}if(n==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!t.code)throw new ed("No code detected.");const{data:m,error:h}=await this._exchangeCodeForSession(t.code);if(h)throw h;const w=new URL(window.location.href);return w.searchParams.delete("code"),window.history.replaceState(window.history.state,"",w.toString()),{data:{session:m.session,redirectType:null},error:null}}const{provider_token:r,provider_refresh_token:i,access_token:s,refresh_token:a,expires_in:o,expires_at:l,token_type:c}=t;if(!s||!o||!a||!c)throw new Ki("No session defined in URL");const u=Math.round(Date.now()/1e3),d=parseInt(o);let f=u+d;l&&(f=parseInt(l));const g=f-u;g*1e3<=Dn&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${g}s, should have been closer to ${d}s`);const y=f-d;u-y>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",y,f,u):u-y<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",y,f,u);const{data:x,error:k}=await this._getUser(s);if(k)throw k;const v={provider_token:r,provider_refresh_token:i,access_token:s,expires_in:d,expires_at:f,refresh_token:a,token_type:c,user:x.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:v,redirectType:t.type},error:null}}catch(r){if(D(r))return{data:{session:null,redirectType:null},error:r};throw r}}_isImplicitGrantCallback(t){return!!(t.access_token||t.error_description)}async _isPKCECallback(t){const n=await Qi(this.storage,`${this.storageKey}-code-verifier`);return!!(t.code&&n)}async signOut(t={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(t))}async _signOut({scope:t}={scope:"global"}){return await this._useSession(async n=>{var r;const{data:i,error:s}=n;if(s)return{error:s};const a=(r=i.session)===null||r===void 0?void 0:r.access_token;if(a){const{error:o}=await this.admin.signOut(a,t);if(o&&!(cx(o)&&(o.status===404||o.status===401||o.status===403)))return{error:o}}return t!=="others"&&(await this._removeSession(),await Gi(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(t){const n=wx(),r={id:n,callback:t,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",n),this.stateChangeEmitters.delete(n)}};return this._debug("#onAuthStateChange()","registered callback with id",n),this.stateChangeEmitters.set(n,r),(async()=>(await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(n)})))(),{data:{subscription:r}}}async _emitInitialSession(t){return await this._useSession(async n=>{var r,i;try{const{data:{session:s},error:a}=n;if(a)throw a;await((r=this.stateChangeEmitters.get(t))===null||r===void 0?void 0:r.callback("INITIAL_SESSION",s)),this._debug("INITIAL_SESSION","callback id",t,"session",s)}catch(s){await((i=this.stateChangeEmitters.get(t))===null||i===void 0?void 0:i.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",t,"error",s),console.error(s)}})}async resetPasswordForEmail(t,n={}){let r=null,i=null;this.flowType==="pkce"&&([r,i]=await Nn(this.storage,this.storageKey,!0));try{return await z(this.fetch,"POST",`${this.url}/recover`,{body:{email:t,code_challenge:r,code_challenge_method:i,gotrue_meta_security:{captcha_token:n.captchaToken}},headers:this.headers,redirectTo:n.redirectTo})}catch(s){if(D(s))return{data:null,error:s};throw s}}async getUserIdentities(){var t;try{const{data:n,error:r}=await this.getUser();if(r)throw r;return{data:{identities:(t=n.user.identities)!==null&&t!==void 0?t:[]},error:null}}catch(n){if(D(n))return{data:null,error:n};throw n}}async linkIdentity(t){var n;try{const{data:r,error:i}=await this._useSession(async s=>{var a,o,l,c,u;const{data:d,error:f}=s;if(f)throw f;const g=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,t.provider,{redirectTo:(a=t.options)===null||a===void 0?void 0:a.redirectTo,scopes:(o=t.options)===null||o===void 0?void 0:o.scopes,queryParams:(l=t.options)===null||l===void 0?void 0:l.queryParams,skipBrowserRedirect:!0});return await z(this.fetch,"GET",g,{headers:this.headers,jwt:(u=(c=d.session)===null||c===void 0?void 0:c.access_token)!==null&&u!==void 0?u:void 0})});if(i)throw i;return Xe()&&!(!((n=t.options)===null||n===void 0)&&n.skipBrowserRedirect)&&window.location.assign(r==null?void 0:r.url),{data:{provider:t.provider,url:r==null?void 0:r.url},error:null}}catch(r){if(D(r))return{data:{provider:t.provider,url:null},error:r};throw r}}async unlinkIdentity(t){try{return await this._useSession(async n=>{var r,i;const{data:s,error:a}=n;if(a)throw a;return await z(this.fetch,"DELETE",`${this.url}/user/identities/${t.identity_id}`,{headers:this.headers,jwt:(i=(r=s.session)===null||r===void 0?void 0:r.access_token)!==null&&i!==void 0?i:void 0})})}catch(n){if(D(n))return{data:null,error:n};throw n}}async _refreshAccessToken(t){const n=`#_refreshAccessToken(${t.substring(0,5)}...)`;this._debug(n,"begin");try{const r=Date.now();return await bx(async i=>(i>0&&await Sx(200*Math.pow(2,i-1)),this._debug(n,"refreshing attempt",i),await z(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:t},headers:this.headers,xform:ft})),(i,s)=>{const a=200*Math.pow(2,i);return s&&to(s)&&Date.now()+a-r<Dn})}catch(r){if(this._debug(n,"error",r),D(r))return{data:{session:null,user:null},error:r};throw r}finally{this._debug(n,"end")}}_isValidSession(t){return typeof t=="object"&&t!==null&&"access_token"in t&&"refresh_token"in t&&"expires_at"in t}async _handleProviderSignIn(t,n){const r=await this._getUrlForProvider(`${this.url}/authorize`,t,{redirectTo:n.redirectTo,scopes:n.scopes,queryParams:n.queryParams});return this._debug("#_handleProviderSignIn()","provider",t,"options",n,"url",r),Xe()&&!n.skipBrowserRedirect&&window.location.assign(r),{data:{provider:t,url:r},error:null}}async _recoverAndRefresh(){var t;const n="#_recoverAndRefresh()";this._debug(n,"begin");try{const r=await Qi(this.storage,this.storageKey);if(this._debug(n,"session from storage",r),!this._isValidSession(r)){this._debug(n,"session is not valid"),r!==null&&await this._removeSession();return}const i=((t=r.expires_at)!==null&&t!==void 0?t:1/0)*1e3-Date.now()<eo;if(this._debug(n,`session has${i?"":" not"} expired with margin of ${eo}s`),i){if(this.autoRefreshToken&&r.refresh_token){const{error:s}=await this._callRefreshToken(r.refresh_token);s&&(console.error(s),to(s)||(this._debug(n,"refresh failed with a non-retryable error, removing the session",s),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(r){this._debug(n,"error",r),console.error(r);return}finally{this._debug(n,"end")}}async _callRefreshToken(t){var n,r;if(!t)throw new Pt;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const i=`#_callRefreshToken(${t.substring(0,5)}...)`;this._debug(i,"begin");try{this.refreshingDeferred=new fa;const{data:s,error:a}=await this._refreshAccessToken(t);if(a)throw a;if(!s.session)throw new Pt;await this._saveSession(s.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",s.session);const o={session:s.session,error:null};return this.refreshingDeferred.resolve(o),o}catch(s){if(this._debug(i,"error",s),D(s)){const a={session:null,error:s};return to(s)||await this._removeSession(),(n=this.refreshingDeferred)===null||n===void 0||n.resolve(a),a}throw(r=this.refreshingDeferred)===null||r===void 0||r.reject(s),s}finally{this.refreshingDeferred=null,this._debug(i,"end")}}async _notifyAllSubscribers(t,n,r=!0){const i=`#_notifyAllSubscribers(${t})`;this._debug(i,"begin",n,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:t,session:n});const s=[],a=Array.from(this.stateChangeEmitters.values()).map(async o=>{try{await o.callback(t,n)}catch(l){s.push(l)}});if(await Promise.all(a),s.length>0){for(let o=0;o<s.length;o+=1)console.error(s[o]);throw s[0]}}finally{this._debug(i,"end")}}async _saveSession(t){this._debug("#_saveSession()",t),this.suppressGetSessionWarning=!0,await gh(this.storage,this.storageKey,t)}async _removeSession(){this._debug("#_removeSession()"),await Gi(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const t=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{t&&Xe()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",t)}catch(n){console.error("removing visibilitychange callback failed",n)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const t=setInterval(()=>this._autoRefreshTokenTick(),Dn);this.autoRefreshTicker=t,t&&typeof t=="object"&&typeof t.unref=="function"?t.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(t),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const t=this.autoRefreshTicker;this.autoRefreshTicker=null,t&&clearInterval(t)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async n=>{const{data:{session:r}}=n;if(!r||!r.refresh_token||!r.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const i=Math.floor((r.expires_at*1e3-t)/Dn);this._debug("#_autoRefreshTokenTick()",`access token expires in ${i} ticks, a tick lasts ${Dn}ms, refresh threshold is ${ll} ticks`),i<=ll&&await this._callRefreshToken(r.refresh_token)})}catch(n){console.error("Auto refresh tick failed with error. This is likely a transient error.",n)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(t){if(t.isAcquireTimeout||t instanceof yh)this._debug("auto refresh token tick lock not available");else throw t}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!Xe()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(t){console.error("_handleVisibilityChange",t)}}async _onVisibilityChanged(t){const n=`#_onVisibilityChanged(${t})`;this._debug(n,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),t||(await this.initializePromise,await this._acquireLock(-1,async()=>{if(document.visibilityState!=="visible"){this._debug(n,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(t,n,r){const i=[`provider=${encodeURIComponent(n)}`];if(r!=null&&r.redirectTo&&i.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),r!=null&&r.scopes&&i.push(`scopes=${encodeURIComponent(r.scopes)}`),this.flowType==="pkce"){const[s,a]=await Nn(this.storage,this.storageKey),o=new URLSearchParams({code_challenge:`${encodeURIComponent(s)}`,code_challenge_method:`${encodeURIComponent(a)}`});i.push(o.toString())}if(r!=null&&r.queryParams){const s=new URLSearchParams(r.queryParams);i.push(s.toString())}return r!=null&&r.skipBrowserRedirect&&i.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${t}?${i.join("&")}`}async _unenroll(t){try{return await this._useSession(async n=>{var r;const{data:i,error:s}=n;return s?{data:null,error:s}:await z(this.fetch,"DELETE",`${this.url}/factors/${t.factorId}`,{headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(D(n))return{data:null,error:n};throw n}}async _enroll(t){try{return await this._useSession(async n=>{var r,i;const{data:s,error:a}=n;if(a)return{data:null,error:a};const o=Object.assign({friendly_name:t.friendlyName,factor_type:t.factorType},t.factorType==="phone"?{phone:t.phone}:{issuer:t.issuer}),{data:l,error:c}=await z(this.fetch,"POST",`${this.url}/factors`,{body:o,headers:this.headers,jwt:(r=s==null?void 0:s.session)===null||r===void 0?void 0:r.access_token});return c?{data:null,error:c}:(t.factorType==="totp"&&(!((i=l==null?void 0:l.totp)===null||i===void 0)&&i.qr_code)&&(l.totp.qr_code=`data:image/svg+xml;utf-8,${l.totp.qr_code}`),{data:l,error:null})})}catch(n){if(D(n))return{data:null,error:n};throw n}}async _verify(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:i,error:s}=n;if(s)return{data:null,error:s};const{data:a,error:o}=await z(this.fetch,"POST",`${this.url}/factors/${t.factorId}/verify`,{body:{code:t.code,challenge_id:t.challengeId},headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token});return o?{data:null,error:o}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+a.expires_in},a)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",a),{data:a,error:o})})}catch(n){if(D(n))return{data:null,error:n};throw n}})}async _challenge(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:i,error:s}=n;return s?{data:null,error:s}:await z(this.fetch,"POST",`${this.url}/factors/${t.factorId}/challenge`,{body:{channel:t.channel},headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(D(n))return{data:null,error:n};throw n}})}async _challengeAndVerify(t){const{data:n,error:r}=await this._challenge({factorId:t.factorId});return r?{data:null,error:r}:await this._verify({factorId:t.factorId,challengeId:n.id,code:t.code})}async _listFactors(){const{data:{user:t},error:n}=await this.getUser();if(n)return{data:null,error:n};const r=(t==null?void 0:t.factors)||[],i=r.filter(a=>a.factor_type==="totp"&&a.status==="verified"),s=r.filter(a=>a.factor_type==="phone"&&a.status==="verified");return{data:{all:r,totp:i,phone:s},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async t=>{var n,r;const{data:{session:i},error:s}=t;if(s)return{data:null,error:s};if(!i)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:a}=no(i.access_token);let o=null;a.aal&&(o=a.aal);let l=o;((r=(n=i.user.factors)===null||n===void 0?void 0:n.filter(d=>d.status==="verified"))!==null&&r!==void 0?r:[]).length>0&&(l="aal2");const u=a.amr||[];return{data:{currentLevel:o,nextLevel:l,currentAuthenticationMethods:u},error:null}}))}async fetchJwk(t,n={keys:[]}){let r=n.keys.find(a=>a.kid===t);if(r||(r=this.jwks.keys.find(a=>a.kid===t),r&&this.jwks_cached_at+ox>Date.now()))return r;const{data:i,error:s}=await z(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(s)throw s;if(!i.keys||i.keys.length===0)throw new Hr("JWKS is empty");if(this.jwks=i,this.jwks_cached_at=Date.now(),r=i.keys.find(a=>a.kid===t),!r)throw new Hr("No matching signing key found in JWKS");return r}async getClaims(t,n={keys:[]}){try{let r=t;if(!r){const{data:g,error:y}=await this.getSession();if(y||!g.session)return{data:null,error:y};r=g.session.access_token}const{header:i,payload:s,signature:a,raw:{header:o,payload:l}}=no(r);if(Nx(s.exp),!i.kid||i.alg==="HS256"||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){const{error:g}=await this.getUser(r);if(g)throw g;return{data:{claims:s,header:i,signature:a},error:null}}const c=Ix(i.alg),u=await this.fetchJwk(i.kid,n),d=await crypto.subtle.importKey("jwk",u,c,!0,["verify"]);if(!await crypto.subtle.verify(c,d,a,gx(`${o}.${l}`)))throw new Hr("Invalid JWT signature");return{data:{claims:s,header:i,signature:a},error:null}}catch(r){if(D(r))return{data:null,error:r};throw r}}}pi.nextInstanceID=0;const Gx=pi;class Jx extends Gx{constructor(t){super(t)}}var Yx=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(a){a(s)})}return new(n||(n=Promise))(function(s,a){function o(u){try{c(r.next(u))}catch(d){a(d)}}function l(u){try{c(r.throw(u))}catch(d){a(d)}}function c(u){u.done?s(u.value):i(u.value).then(o,l)}c((r=r.apply(e,t||[])).next())})};class Xx{constructor(t,n,r){var i,s,a;if(this.supabaseUrl=t,this.supabaseKey=n,!t)throw new Error("supabaseUrl is required.");if(!n)throw new Error("supabaseKey is required.");const o=tx(t),l=new URL(o);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const c=`sb-${l.hostname.split(".")[0]}-auth-token`,u={db:K0,realtime:G0,auth:Object.assign(Object.assign({},Q0),{storageKey:c}),global:V0},d=nx(r??{},u);this.storageKey=(i=d.auth.storageKey)!==null&&i!==void 0?i:"",this.headers=(s=d.global.headers)!==null&&s!==void 0?s:{},d.accessToken?(this.accessToken=d.accessToken,this.auth=new Proxy({},{get:(f,g)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(g)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((a=d.auth)!==null&&a!==void 0?a:{},this.headers,d.global.fetch),this.fetch=Z0(n,this._getAccessToken.bind(this),d.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},d.realtime)),this.rest=new m0(new URL("rest/v1",l).href,{headers:this.headers,schema:d.db.schema,fetch:this.fetch}),d.accessToken||this._listenForAuthEvents()}get functions(){return new Hy(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new W0(this.storageUrl.href,this.headers,this.fetch)}from(t){return this.rest.from(t)}schema(t){return this.rest.schema(t)}rpc(t,n={},r={}){return this.rest.rpc(t,n,r)}channel(t,n={config:{}}){return this.realtime.channel(t,n)}getChannels(){return this.realtime.getChannels()}removeChannel(t){return this.realtime.removeChannel(t)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var t,n;return Yx(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:r}=yield this.auth.getSession();return(n=(t=r.session)===null||t===void 0?void 0:t.access_token)!==null&&n!==void 0?n:null})}_initSupabaseAuthClient({autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:i,storageKey:s,flowType:a,lock:o,debug:l},c,u){const d={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new Jx({url:this.authUrl.href,headers:Object.assign(Object.assign({},d),c),storageKey:s,autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:i,flowType:a,lock:o,debug:l,fetch:u,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(t){return new O0(this.realtimeUrl.href,Object.assign(Object.assign({},t),{params:Object.assign({apikey:this.supabaseKey},t==null?void 0:t.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((n,r)=>{this._handleTokenChanged(n,"CLIENT",r==null?void 0:r.access_token)})}_handleTokenChanged(t,n,r){(t==="TOKEN_REFRESHED"||t==="SIGNED_IN")&&this.changedAccessToken!==r?this.changedAccessToken=r:t==="SIGNED_OUT"&&(this.realtime.setAuth(),n=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const Zx=(e,t,n)=>new Xx(e,t,n),ew="https://jpvbtrzvbpyzgtpvltss.supabase.co",tw="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpwdmJ0cnp2YnB5emd0cHZsdHNzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5MTM1MzAsImV4cCI6MjA2NjQ4OTUzMH0.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI";Zx(ew,tw);class nw{async signUp(t,n,r){try{return await(await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,password:n,name:r})})).json()}catch{return{success:!1,error:"Network error during signup"}}}async signIn(t,n){try{const i=await(await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,password:n})})).json();return i.success&&i.token&&localStorage.setItem("auth_token",i.token),i}catch{return{success:!1,error:"Network error during login"}}}async signOut(){try{const t=localStorage.getItem("auth_token");t&&await fetch("/api/auth/logout",{method:"POST",headers:{Authorization:`Bearer ${t}`}})}finally{localStorage.removeItem("auth_token")}}async getCurrentUser(){try{const t=localStorage.getItem("auth_token");if(!t)return null;const n=await fetch("/api/auth/user",{headers:{Authorization:`Bearer ${t}`}});if(!n.ok)return n.status===401&&localStorage.removeItem("auth_token"),null;const r=await n.json();return r.success?r.data:null}catch{return null}}getToken(){return localStorage.getItem("auth_token")}isAuthenticated(){return!!this.getToken()}}const Ji=new nw,ha=aa((e,t)=>({user:null,isLoading:!1,isAuthenticated:!1,login:async(n,r)=>{e({isLoading:!0});try{const i=await Ji.signIn(n,r);return i.success&&i.user?(e({user:i.user,isAuthenticated:!0,isLoading:!1}),{success:!0}):(e({isLoading:!1}),{success:!1,error:i.error||"Login failed"})}catch{return e({isLoading:!1}),{success:!1,error:"Network error"}}},signup:async(n,r,i)=>{e({isLoading:!0});try{const s=await Ji.signUp(n,r,i);return s.success&&s.user?(e({user:s.user,isAuthenticated:!0,isLoading:!1}),{success:!0}):(e({isLoading:!1}),{success:!1,error:s.error||"Signup failed"})}catch{return e({isLoading:!1}),{success:!1,error:"Network error"}}},logout:async()=>{e({isLoading:!0});try{await Ji.signOut()}finally{e({user:null,isAuthenticated:!1,isLoading:!1})}},checkAuth:async()=>{e({isLoading:!0});try{const n=await Ji.getCurrentUser();e({user:n,isAuthenticated:!!n,isLoading:!1})}catch{e({user:null,isAuthenticated:!1,isLoading:!1})}},updateUser:n=>{const{user:r}=t();r&&e({user:{...r,...n}})}})),te=({children:e,onClick:t,variant:n="primary",size:r="md",isLoading:i=!1,disabled:s=!1,type:a="button",className:o="",...l})=>{const c="font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",u={primary:"bg-primary-500 hover:bg-primary-600 text-white focus:ring-primary-500",secondary:"border-2 border-primary-500 text-primary-500 hover:bg-primary-50 focus:ring-primary-500",danger:"bg-red-500 hover:bg-red-600 text-white focus:ring-red-500"},d={sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"},f=`${c} ${u[n]} ${d[r]} ${o}`;return p.jsx("button",{type:a,onClick:t,disabled:s||i,className:f,...l,children:i?p.jsxs("div",{className:"flex items-center",children:[p.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[p.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),p.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Loading..."]}):e})},Lt=({label:e,placeholder:t,value:n,onChange:r,type:i="text",error:s,required:a=!1,disabled:o=!1,className:l="",...c})=>{const u=`w-full px-3 py-2 border-2 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed ${s?"border-red-500":"border-gray-600 focus:border-primary-500"} ${l}`;return p.jsxs("div",{className:"w-full",children:[e&&p.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:[e,a&&p.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),p.jsx("input",{type:i,value:n,onChange:d=>r(d.target.value),placeholder:t,disabled:o,className:u,...c}),s&&p.jsx("p",{className:"mt-1 text-sm text-red-500",children:s})]})},rw=()=>{const[e,t]=_.useState(""),[n,r]=_.useState(""),[i,s]=_.useState({}),{login:a,isLoading:o}=ha(),l=Et(),c=()=>{const d={};return e?/\S+@\S+\.\S+/.test(e)||(d.email="Email is invalid"):d.email="Email is required",n||(d.password="Password is required"),s(d),Object.keys(d).length===0},u=async d=>{if(d.preventDefault(),!c())return;const f=await a(e,n);f.success?l("/dashboard"):s({general:f.error})};return p.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:p.jsxs("div",{className:"max-w-md w-full space-y-8 p-8",children:[p.jsxs("div",{children:[p.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Sign in to ChewyAI"}),p.jsxs("p",{className:"mt-2 text-center text-sm text-gray-400",children:["Or"," ",p.jsx(qf,{to:"/signup",className:"font-medium text-primary-500 hover:text-primary-400",children:"create a new account"})]})]}),p.jsxs("form",{className:"mt-8 space-y-6",onSubmit:u,children:[i.general&&p.jsx("div",{className:"bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded",children:i.general}),p.jsxs("div",{className:"space-y-4",children:[p.jsx(Lt,{label:"Email address",type:"email",value:e,onChange:t,error:i.email,placeholder:"Enter your email",required:!0}),p.jsx(Lt,{label:"Password",type:"password",value:n,onChange:r,error:i.password,placeholder:"Enter your password",required:!0})]}),p.jsx(te,{type:"submit",isLoading:o,className:"w-full",size:"lg",children:"Sign in"})]})]})})},iw=()=>{const[e,t]=_.useState({name:"",email:"",password:"",confirmPassword:""}),[n,r]=_.useState({}),{signup:i,isLoading:s}=ha(),a=Et(),o=()=>{const u={};return e.email?/\S+@\S+\.\S+/.test(e.email)||(u.email="Email is invalid"):u.email="Email is required",e.password?e.password.length<6&&(u.password="Password must be at least 6 characters"):u.password="Password is required",e.password!==e.confirmPassword&&(u.confirmPassword="Passwords do not match"),r(u),Object.keys(u).length===0},l=async u=>{if(u.preventDefault(),!o())return;const d=await i(e.email,e.password,e.name||void 0);d.success?a("/dashboard"):r({general:d.error||"Signup failed"})},c=(u,d)=>t(f=>({...f,[u]:d}));return p.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:p.jsxs("div",{className:"max-w-md w-full space-y-8 p-8",children:[p.jsxs("div",{children:[p.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Create your account"}),p.jsxs("p",{className:"mt-2 text-center text-sm text-gray-400",children:["Or"," ",p.jsx(qf,{to:"/login",className:"font-medium text-primary-500 hover:text-primary-400",children:"sign in to your existing account"})]})]}),p.jsxs("form",{className:"mt-8 space-y-6",onSubmit:l,children:[n.general&&p.jsx("div",{className:"bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded",children:n.general}),p.jsxs("div",{className:"space-y-4",children:[p.jsx(Lt,{label:"Full Name (Optional)",value:e.name,onChange:u=>c("name",u),placeholder:"Enter your full name"}),p.jsx(Lt,{label:"Email address",type:"email",value:e.email,onChange:u=>c("email",u),error:n.email,placeholder:"Enter your email",required:!0}),p.jsx(Lt,{label:"Password",type:"password",value:e.password,onChange:u=>c("password",u),error:n.password,placeholder:"Create a password",required:!0}),p.jsx(Lt,{label:"Confirm Password",type:"password",value:e.confirmPassword,onChange:u=>c("confirmPassword",u),error:n.confirmPassword,placeholder:"Confirm your password",required:!0})]}),p.jsx(te,{type:"submit",isLoading:s,className:"w-full",size:"lg",children:"Create Account"})]})]})})},An=({children:e})=>{const{isAuthenticated:t,isLoading:n,checkAuth:r}=ha(),i=mr();return _.useEffect(()=>{r()},[r]),n?p.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:p.jsx("div",{className:"text-white",children:"Loading..."})}):t?p.jsx(p.Fragment,{children:e}):p.jsx(Wf,{to:"/login",state:{from:i},replace:!0})},sw=()=>p.jsx("div",{className:"min-h-screen bg-background-primary text-white flex items-center justify-center",children:p.jsx("h1",{className:"text-3xl font-bold",children:"Welcome to ChewyAI Dashboard"})});var xh={exports:{}},aw="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",ow=aw,lw=ow;function wh(){}function _h(){}_h.resetWarningCache=wh;var cw=function(){function e(r,i,s,a,o,l){if(l!==lw){var c=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}e.isRequired=e;function t(){return e}var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:_h,resetWarningCache:wh};return n.PropTypes=n,n};xh.exports=cw();var uw=xh.exports;const B=vl(uw);function Sn(e,t,n,r){function i(s){return s instanceof n?s:new n(function(a){a(s)})}return new(n||(n=Promise))(function(s,a){function o(u){try{c(r.next(u))}catch(d){a(d)}}function l(u){try{c(r.throw(u))}catch(d){a(d)}}function c(u){u.done?s(u.value):i(u.value).then(o,l)}c((r=r.apply(e,t||[])).next())})}const dw=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function cr(e,t,n){const r=pw(e),{webkitRelativePath:i}=e,s=typeof t=="string"?t:typeof i=="string"&&i.length>0?i:`./${e.name}`;return typeof r.path!="string"&&cd(r,"path",s),n!==void 0&&Object.defineProperty(r,"handle",{value:n,writable:!1,configurable:!1,enumerable:!0}),cd(r,"relativePath",s),r}function pw(e){const{name:t}=e;if(t&&t.lastIndexOf(".")!==-1&&!e.type){const r=t.split(".").pop().toLowerCase(),i=dw.get(r);i&&Object.defineProperty(e,"type",{value:i,writable:!1,configurable:!1,enumerable:!0})}return e}function cd(e,t,n){Object.defineProperty(e,t,{value:n,writable:!1,configurable:!1,enumerable:!0})}const fw=[".DS_Store","Thumbs.db"];function hw(e){return Sn(this,void 0,void 0,function*(){return Ls(e)&&mw(e.dataTransfer)?xw(e.dataTransfer,e.type):vw(e)?gw(e):Array.isArray(e)&&e.every(t=>"getFile"in t&&typeof t.getFile=="function")?yw(e):[]})}function mw(e){return Ls(e)}function vw(e){return Ls(e)&&Ls(e.target)}function Ls(e){return typeof e=="object"&&e!==null}function gw(e){return dl(e.target.files).map(t=>cr(t))}function yw(e){return Sn(this,void 0,void 0,function*(){return(yield Promise.all(e.map(n=>n.getFile()))).map(n=>cr(n))})}function xw(e,t){return Sn(this,void 0,void 0,function*(){if(e.items){const n=dl(e.items).filter(i=>i.kind==="file");if(t!=="drop")return n;const r=yield Promise.all(n.map(ww));return ud(kh(r))}return ud(dl(e.files).map(n=>cr(n)))})}function ud(e){return e.filter(t=>fw.indexOf(t.name)===-1)}function dl(e){if(e===null)return[];const t=[];for(let n=0;n<e.length;n++){const r=e[n];t.push(r)}return t}function ww(e){if(typeof e.webkitGetAsEntry!="function")return dd(e);const t=e.webkitGetAsEntry();return t&&t.isDirectory?Sh(t):dd(e,t)}function kh(e){return e.reduce((t,n)=>[...t,...Array.isArray(n)?kh(n):[n]],[])}function dd(e,t){return Sn(this,void 0,void 0,function*(){var n;if(globalThis.isSecureContext&&typeof e.getAsFileSystemHandle=="function"){const s=yield e.getAsFileSystemHandle();if(s===null)throw new Error(`${e} is not a File`);if(s!==void 0){const a=yield s.getFile();return a.handle=s,cr(a)}}const r=e.getAsFile();if(!r)throw new Error(`${e} is not a File`);return cr(r,(n=t==null?void 0:t.fullPath)!==null&&n!==void 0?n:void 0)})}function _w(e){return Sn(this,void 0,void 0,function*(){return e.isDirectory?Sh(e):kw(e)})}function Sh(e){const t=e.createReader();return new Promise((n,r)=>{const i=[];function s(){t.readEntries(a=>Sn(this,void 0,void 0,function*(){if(a.length){const o=Promise.all(a.map(_w));i.push(o),s()}else try{const o=yield Promise.all(i);n(o)}catch(o){r(o)}}),a=>{r(a)})}s()})}function kw(e){return Sn(this,void 0,void 0,function*(){return new Promise((t,n)=>{e.file(r=>{const i=cr(r,e.fullPath);t(i)},r=>{n(r)})})})}var io=function(e,t){if(e&&t){var n=Array.isArray(t)?t:t.split(",");if(n.length===0)return!0;var r=e.name||"",i=(e.type||"").toLowerCase(),s=i.replace(/\/.*$/,"");return n.some(function(a){var o=a.trim().toLowerCase();return o.charAt(0)==="."?r.toLowerCase().endsWith(o):o.endsWith("/*")?s===o.replace(/\/.*$/,""):i===o})}return!0};function pd(e){return jw(e)||bw(e)||jh(e)||Sw()}function Sw(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function bw(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function jw(e){if(Array.isArray(e))return pl(e)}function fd(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function hd(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?fd(Object(n),!0).forEach(function(r){bh(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):fd(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function bh(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function fi(e,t){return Pw(e)||Cw(e,t)||jh(e,t)||Ew()}function Ew(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function jh(e,t){if(e){if(typeof e=="string")return pl(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return pl(e,t)}}function pl(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Cw(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var r=[],i=!0,s=!1,a,o;try{for(n=n.call(e);!(i=(a=n.next()).done)&&(r.push(a.value),!(t&&r.length===t));i=!0);}catch(l){s=!0,o=l}finally{try{!i&&n.return!=null&&n.return()}finally{if(s)throw o}}return r}}function Pw(e){if(Array.isArray(e))return e}var Tw=typeof io=="function"?io:io.default,Ow="file-invalid-type",Nw="file-too-large",Iw="file-too-small",Rw="too-many-files",Aw=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",n=t.split(","),r=n.length>1?"one of ".concat(n.join(", ")):n[0];return{code:Ow,message:"File type must be ".concat(r)}},md=function(t){return{code:Nw,message:"File is larger than ".concat(t," ").concat(t===1?"byte":"bytes")}},vd=function(t){return{code:Iw,message:"File is smaller than ".concat(t," ").concat(t===1?"byte":"bytes")}},Dw={code:Rw,message:"Too many files"};function Eh(e,t){var n=e.type==="application/x-moz-file"||Tw(e,t);return[n,n?null:Aw(t)]}function Ch(e,t,n){if(cn(e.size))if(cn(t)&&cn(n)){if(e.size>n)return[!1,md(n)];if(e.size<t)return[!1,vd(t)]}else{if(cn(t)&&e.size<t)return[!1,vd(t)];if(cn(n)&&e.size>n)return[!1,md(n)]}return[!0,null]}function cn(e){return e!=null}function $w(e){var t=e.files,n=e.accept,r=e.minSize,i=e.maxSize,s=e.multiple,a=e.maxFiles,o=e.validator;return!s&&t.length>1||s&&a>=1&&t.length>a?!1:t.every(function(l){var c=Eh(l,n),u=fi(c,1),d=u[0],f=Ch(l,r,i),g=fi(f,1),y=g[0],x=o?o(l):null;return d&&y&&!x})}function Us(e){return typeof e.isPropagationStopped=="function"?e.isPropagationStopped():typeof e.cancelBubble<"u"?e.cancelBubble:!1}function Yi(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(t){return t==="Files"||t==="application/x-moz-file"}):!!e.target&&!!e.target.files}function gd(e){e.preventDefault()}function zw(e){return e.indexOf("MSIE")!==-1||e.indexOf("Trident/")!==-1}function Lw(e){return e.indexOf("Edge/")!==-1}function Uw(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.navigator.userAgent;return zw(e)||Lw(e)}function ot(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(r){for(var i=arguments.length,s=new Array(i>1?i-1:0),a=1;a<i;a++)s[a-1]=arguments[a];return t.some(function(o){return!Us(r)&&o&&o.apply(void 0,[r].concat(s)),Us(r)})}}function Fw(){return"showOpenFilePicker"in window}function Mw(e){if(cn(e)){var t=Object.entries(e).filter(function(n){var r=fi(n,2),i=r[0],s=r[1],a=!0;return Ph(i)||(console.warn('Skipped "'.concat(i,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),a=!1),(!Array.isArray(s)||!s.every(Th))&&(console.warn('Skipped "'.concat(i,'" because an invalid file extension was provided.')),a=!1),a}).reduce(function(n,r){var i=fi(r,2),s=i[0],a=i[1];return hd(hd({},n),{},bh({},s,a))},{});return[{description:"Files",accept:t}]}return e}function Bw(e){if(cn(e))return Object.entries(e).reduce(function(t,n){var r=fi(n,2),i=r[0],s=r[1];return[].concat(pd(t),[i],pd(s))},[]).filter(function(t){return Ph(t)||Th(t)}).join(",")}function Ww(e){return e instanceof DOMException&&(e.name==="AbortError"||e.code===e.ABORT_ERR)}function qw(e){return e instanceof DOMException&&(e.name==="SecurityError"||e.code===e.SECURITY_ERR)}function Ph(e){return e==="audio/*"||e==="video/*"||e==="image/*"||e==="text/*"||e==="application/*"||/\w+\/[-+.\w]+/g.test(e)}function Th(e){return/^.*\.[\w]+$/.test(e)}var Hw=["children"],Vw=["open"],Kw=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],Qw=["refKey","onChange","onClick"];function Gw(e){return Xw(e)||Yw(e)||Oh(e)||Jw()}function Jw(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Yw(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Xw(e){if(Array.isArray(e))return fl(e)}function so(e,t){return t_(e)||e_(e,t)||Oh(e,t)||Zw()}function Zw(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Oh(e,t){if(e){if(typeof e=="string")return fl(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return fl(e,t)}}function fl(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function e_(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var r=[],i=!0,s=!1,a,o;try{for(n=n.call(e);!(i=(a=n.next()).done)&&(r.push(a.value),!(t&&r.length===t));i=!0);}catch(l){s=!0,o=l}finally{try{!i&&n.return!=null&&n.return()}finally{if(s)throw o}}return r}}function t_(e){if(Array.isArray(e))return e}function yd(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function J(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?yd(Object(n),!0).forEach(function(r){hl(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):yd(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function hl(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Fs(e,t){if(e==null)return{};var n=n_(e,t),r,i;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(i=0;i<s.length;i++)r=s[i],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function n_(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,s;for(s=0;s<r.length;s++)i=r[s],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}var wc=_.forwardRef(function(e,t){var n=e.children,r=Fs(e,Hw),i=Ih(r),s=i.open,a=Fs(i,Vw);return _.useImperativeHandle(t,function(){return{open:s}},[s]),Bs.createElement(_.Fragment,null,n(J(J({},a),{},{open:s})))});wc.displayName="Dropzone";var Nh={disabled:!1,getFilesFromEvent:hw,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};wc.defaultProps=Nh;wc.propTypes={children:B.func,accept:B.objectOf(B.arrayOf(B.string)),multiple:B.bool,preventDropOnDocument:B.bool,noClick:B.bool,noKeyboard:B.bool,noDrag:B.bool,noDragEventsBubbling:B.bool,minSize:B.number,maxSize:B.number,maxFiles:B.number,disabled:B.bool,getFilesFromEvent:B.func,onFileDialogCancel:B.func,onFileDialogOpen:B.func,useFsAccessApi:B.bool,autoFocus:B.bool,onDragEnter:B.func,onDragLeave:B.func,onDragOver:B.func,onDrop:B.func,onDropAccepted:B.func,onDropRejected:B.func,onError:B.func,validator:B.func};var ml={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function Ih(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=J(J({},Nh),e),n=t.accept,r=t.disabled,i=t.getFilesFromEvent,s=t.maxSize,a=t.minSize,o=t.multiple,l=t.maxFiles,c=t.onDragEnter,u=t.onDragLeave,d=t.onDragOver,f=t.onDrop,g=t.onDropAccepted,y=t.onDropRejected,x=t.onFileDialogCancel,k=t.onFileDialogOpen,v=t.useFsAccessApi,m=t.autoFocus,h=t.preventDropOnDocument,w=t.noClick,S=t.noKeyboard,b=t.noDrag,E=t.noDragEventsBubbling,C=t.onError,N=t.validator,I=_.useMemo(function(){return Bw(n)},[n]),M=_.useMemo(function(){return Mw(n)},[n]),le=_.useMemo(function(){return typeof k=="function"?k:xd},[k]),he=_.useMemo(function(){return typeof x=="function"?x:xd},[x]),ce=_.useRef(null),Pe=_.useRef(null),yr=_.useReducer(r_,ml),bn=so(yr,2),T=bn[0],R=bn[1],$=T.isFocused,K=T.isFileDialogActive,G=_.useRef(typeof window<"u"&&window.isSecureContext&&v&&Fw()),tn=function(){!G.current&&K&&setTimeout(function(){if(Pe.current){var A=Pe.current.files;A.length||(R({type:"closeDialog"}),he())}},300)};_.useEffect(function(){return window.addEventListener("focus",tn,!1),function(){window.removeEventListener("focus",tn,!1)}},[Pe,K,he,G]);var Te=_.useRef([]),jn=function(A){ce.current&&ce.current.contains(A.target)||(A.preventDefault(),Te.current=[])};_.useEffect(function(){return h&&(document.addEventListener("dragover",gd,!1),document.addEventListener("drop",jn,!1)),function(){h&&(document.removeEventListener("dragover",gd),document.removeEventListener("drop",jn))}},[ce,h]),_.useEffect(function(){return!r&&m&&ce.current&&ce.current.focus(),function(){}},[ce,m,r]);var Se=_.useCallback(function(P){C?C(P):console.error(P)},[C]),nn=_.useCallback(function(P){P.preventDefault(),P.persist(),Ei(P),Te.current=[].concat(Gw(Te.current),[P.target]),Yi(P)&&Promise.resolve(i(P)).then(function(A){if(!(Us(P)&&!E)){var ie=A.length,ue=ie>0&&$w({files:A,accept:I,minSize:a,maxSize:s,multiple:o,maxFiles:l,validator:N}),ze=ie>0&&!ue;R({isDragAccept:ue,isDragReject:ze,isDragActive:!0,type:"setDraggedFiles"}),c&&c(P)}}).catch(function(A){return Se(A)})},[i,c,Se,E,I,a,s,o,l,N]),_c=_.useCallback(function(P){P.preventDefault(),P.persist(),Ei(P);var A=Yi(P);if(A&&P.dataTransfer)try{P.dataTransfer.dropEffect="copy"}catch{}return A&&d&&d(P),!1},[d,E]),kc=_.useCallback(function(P){P.preventDefault(),P.persist(),Ei(P);var A=Te.current.filter(function(ue){return ce.current&&ce.current.contains(ue)}),ie=A.indexOf(P.target);ie!==-1&&A.splice(ie,1),Te.current=A,!(A.length>0)&&(R({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),Yi(P)&&u&&u(P))},[ce,u,E]),Si=_.useCallback(function(P,A){var ie=[],ue=[];P.forEach(function(ze){var xr=Eh(ze,I),Pn=so(xr,2),ga=Pn[0],ya=Pn[1],xa=Ch(ze,a,s),Ci=so(xa,2),wa=Ci[0],_a=Ci[1],ka=N?N(ze):null;if(ga&&wa&&!ka)ie.push(ze);else{var Sa=[ya,_a];ka&&(Sa=Sa.concat(ka)),ue.push({file:ze,errors:Sa.filter(function(zh){return zh})})}}),(!o&&ie.length>1||o&&l>=1&&ie.length>l)&&(ie.forEach(function(ze){ue.push({file:ze,errors:[Dw]})}),ie.splice(0)),R({acceptedFiles:ie,fileRejections:ue,isDragReject:ue.length>0,type:"setFiles"}),f&&f(ie,ue,A),ue.length>0&&y&&y(ue,A),ie.length>0&&g&&g(ie,A)},[R,o,I,a,s,l,f,g,y,N]),bi=_.useCallback(function(P){P.preventDefault(),P.persist(),Ei(P),Te.current=[],Yi(P)&&Promise.resolve(i(P)).then(function(A){Us(P)&&!E||Si(A,P)}).catch(function(A){return Se(A)}),R({type:"reset"})},[i,Si,Se,E]),En=_.useCallback(function(){if(G.current){R({type:"openDialog"}),le();var P={multiple:o,types:M};window.showOpenFilePicker(P).then(function(A){return i(A)}).then(function(A){Si(A,null),R({type:"closeDialog"})}).catch(function(A){Ww(A)?(he(A),R({type:"closeDialog"})):qw(A)?(G.current=!1,Pe.current?(Pe.current.value=null,Pe.current.click()):Se(new Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):Se(A)});return}Pe.current&&(R({type:"openDialog"}),le(),Pe.current.value=null,Pe.current.click())},[R,le,he,v,Si,Se,M,o]),Sc=_.useCallback(function(P){!ce.current||!ce.current.isEqualNode(P.target)||(P.key===" "||P.key==="Enter"||P.keyCode===32||P.keyCode===13)&&(P.preventDefault(),En())},[ce,En]),bc=_.useCallback(function(){R({type:"focus"})},[]),jc=_.useCallback(function(){R({type:"blur"})},[]),Ec=_.useCallback(function(){w||(Uw()?setTimeout(En,0):En())},[w,En]),Cn=function(A){return r?null:A},va=function(A){return S?null:Cn(A)},ji=function(A){return b?null:Cn(A)},Ei=function(A){E&&A.stopPropagation()},Ah=_.useMemo(function(){return function(){var P=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},A=P.refKey,ie=A===void 0?"ref":A,ue=P.role,ze=P.onKeyDown,xr=P.onFocus,Pn=P.onBlur,ga=P.onClick,ya=P.onDragEnter,xa=P.onDragOver,Ci=P.onDragLeave,wa=P.onDrop,_a=Fs(P,Kw);return J(J(hl({onKeyDown:va(ot(ze,Sc)),onFocus:va(ot(xr,bc)),onBlur:va(ot(Pn,jc)),onClick:Cn(ot(ga,Ec)),onDragEnter:ji(ot(ya,nn)),onDragOver:ji(ot(xa,_c)),onDragLeave:ji(ot(Ci,kc)),onDrop:ji(ot(wa,bi)),role:typeof ue=="string"&&ue!==""?ue:"presentation"},ie,ce),!r&&!S?{tabIndex:0}:{}),_a)}},[ce,Sc,bc,jc,Ec,nn,_c,kc,bi,S,b,r]),Dh=_.useCallback(function(P){P.stopPropagation()},[]),$h=_.useMemo(function(){return function(){var P=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},A=P.refKey,ie=A===void 0?"ref":A,ue=P.onChange,ze=P.onClick,xr=Fs(P,Qw),Pn=hl({accept:I,multiple:o,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:Cn(ot(ue,bi)),onClick:Cn(ot(ze,Dh)),tabIndex:-1},ie,Pe);return J(J({},Pn),xr)}},[Pe,n,o,bi,r]);return J(J({},T),{},{isFocused:$&&!r,getRootProps:Ah,getInputProps:$h,rootRef:ce,inputRef:Pe,open:Cn(En)})}function r_(e,t){switch(t.type){case"focus":return J(J({},e),{},{isFocused:!0});case"blur":return J(J({},e),{},{isFocused:!1});case"openDialog":return J(J({},ml),{},{isFileDialogActive:!0});case"closeDialog":return J(J({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return J(J({},e),{},{isDragActive:t.isDragActive,isDragAccept:t.isDragAccept,isDragReject:t.isDragReject});case"setFiles":return J(J({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections,isDragReject:t.isDragReject});case"reset":return J({},ml);default:return e}}function xd(){}const ma=aa(e=>({documents:[],selectedDocuments:new Set,isLoading:!1,uploadProgress:{},fetchDocuments:async()=>{e({isLoading:!0});try{const t=localStorage.getItem("auth_token"),n=await fetch("/api/documents",{headers:{Authorization:`Bearer ${t}`}});if(!n.ok)throw new Error("Failed to fetch documents");const r=await n.json();if(r.success)e({documents:r.data,isLoading:!1});else throw new Error(r.error)}catch(t){throw console.error("Fetch documents error:",t),e({isLoading:!1}),t}},uploadDocument:async t=>{const n=new FormData;n.append("document",t);try{const r=localStorage.getItem("auth_token"),i=await fetch("/api/documents/upload",{method:"POST",headers:{Authorization:`Bearer ${r}`},body:n});if(!i.ok){const a=await i.json();throw new Error(a.error||"Upload failed")}const s=await i.json();if(s.success)return e(a=>({documents:[s.data,...a.documents],uploadProgress:{...a.uploadProgress,[t.name]:100}})),s.data;throw new Error(s.error)}catch(r){throw console.error("Upload document error:",r),r}},deleteDocument:async t=>{try{const n=localStorage.getItem("auth_token"),r=await fetch(`/api/documents/${t}`,{method:"DELETE",headers:{Authorization:`Bearer ${n}`}});if(!r.ok){const i=await r.json();throw new Error(i.error||"Delete failed")}e(i=>({documents:i.documents.filter(s=>s.id!==t),selectedDocuments:new Set([...i.selectedDocuments].filter(s=>s!==t))}))}catch(n){throw console.error("Delete document error:",n),n}},searchDocuments:async t=>{try{const n=localStorage.getItem("auth_token"),r=await fetch(`/api/documents/search?q=${encodeURIComponent(t)}`,{headers:{Authorization:`Bearer ${n}`}});if(!r.ok)throw new Error("Search failed");const i=await r.json();return i.success?i.data:[]}catch(n){return console.error("Search documents error:",n),[]}},getDocument:async t=>{try{const n=localStorage.getItem("auth_token"),r=await fetch(`/api/documents/${t}`,{headers:{Authorization:`Bearer ${n}`}});if(!r.ok)return null;const i=await r.json();return i.success?i.data:null}catch(n){return console.error("Get document error:",n),null}},toggleDocumentSelection:t=>{e(n=>{const r=new Set(n.selectedDocuments);return r.has(t)?r.delete(t):r.add(t),{selectedDocuments:r}})},clearSelection:()=>{e({selectedDocuments:new Set})},selectAll:()=>{e(t=>({selectedDocuments:new Set(t.documents.map(n=>n.id))}))},setUploadProgress:(t,n)=>{e(r=>({uploadProgress:{...r.uploadProgress,[t]:n}}))}})),i_=()=>{const[e,t]=_.useState(!1),[n,r]=_.useState([]),{uploadDocument:i,setUploadProgress:s}=ma(),a=_.useCallback(async u=>{t(!0),r([]);const d=[];for(const f of u)try{if(f.size>10*1024*1024){d.push(`${f.name}: File size exceeds 10MB limit`);continue}if(!["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","text/plain","application/vnd.openxmlformats-officedocument.presentationml.presentation"].includes(f.type)){d.push(`${f.name}: Unsupported file type. Please upload PDF, DOCX, TXT, or PPTX files.`);continue}s(f.name,0),await i(f),s(f.name,100)}catch(g){d.push(`${f.name}: ${g instanceof Error?g.message:"Unknown error"}`)}r(d),t(!1)},[i,s]),{getRootProps:o,getInputProps:l,isDragActive:c}=Ih({onDrop:a,accept:{"application/pdf":[".pdf"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":[".docx"],"text/plain":[".txt"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":[".pptx"]},multiple:!0,disabled:e});return p.jsxs("div",{className:"space-y-4",children:[p.jsxs("div",{...o(),className:`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
          ${c?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-primary-500 hover:bg-primary-500/5"}
          ${e?"opacity-50 cursor-not-allowed":""}
        `,children:[p.jsx("input",{...l()}),p.jsxs("div",{className:"space-y-2",children:[p.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:p.jsx("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})}),c?p.jsx("p",{className:"text-primary-400",children:"Drop the files here..."}):p.jsxs("div",{children:[p.jsxs("p",{className:"text-gray-300",children:["Drag & drop files here, or"," ",p.jsx("span",{className:"text-primary-500 font-medium",children:"browse"})]}),p.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Supports PDF, DOCX, TXT, PPTX (max 10MB each)"})]})]})]}),e&&p.jsxs("div",{className:"bg-background-secondary rounded-lg p-4",children:[p.jsx("p",{className:"text-sm text-gray-300 mb-2",children:"Uploading files..."}),p.jsx("div",{className:"space-y-2"})]}),n.length>0&&p.jsxs("div",{className:"bg-red-900/20 border border-red-700 rounded-lg p-4",children:[p.jsx("h4",{className:"text-red-400 font-medium mb-2",children:"Upload Errors:"}),p.jsx("ul",{className:"text-sm text-red-300 space-y-1",children:n.map((u,d)=>p.jsxs("li",{children:["• ",u]},d))})]})]})},s_=({document:e})=>{const{selectedDocuments:t,toggleDocumentSelection:n,deleteDocument:r}=ma(),[i,s]=_.useState(!1),a=t.has(e.id),o=async()=>{if(window.confirm(`Are you sure you want to delete "${e.filename}"? This action cannot be undone.`)){s(!0);try{await r(e.id)}catch(f){console.error("Delete error:",f),alert("Failed to delete document. Please try again.")}finally{s(!1)}}},l=d=>{if(d===0)return"0 Bytes";const f=1024,g=["Bytes","KB","MB","GB"],y=Math.floor(Math.log(d)/Math.log(f));return parseFloat((d/Math.pow(f,y)).toFixed(2))+" "+g[y]},c=d=>new Date(d).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),u=d=>({pdf:"📄",docx:"📝",txt:"📃",pptx:"📊"})[d]||"📄";return p.jsxs("div",{className:`
        bg-background-secondary rounded-lg p-4 border-2 transition-all cursor-pointer
        ${a?"border-primary-500 bg-primary-500/10":"border-gray-700 hover:border-gray-600"}
      `,onClick:()=>n(e.id),children:[p.jsxs("div",{className:"flex items-start justify-between mb-3",children:[p.jsxs("div",{className:"flex items-center space-x-2 flex-1 min-w-0",children:[p.jsx("span",{className:"text-2xl",children:u(e.file_type)}),p.jsxs("div",{className:"min-w-0 flex-1",children:[p.jsx("h3",{className:"text-white font-medium truncate",title:e.filename,children:e.filename}),p.jsxs("p",{className:"text-sm text-gray-400",children:[e.file_type.toUpperCase()," • ",l(e.file_size)]})]})]}),p.jsx("div",{className:`
            w-5 h-5 rounded border-2 flex items-center justify-center
            ${a?"bg-primary-500 border-primary-500":"border-gray-500"}
          `,children:a&&p.jsx("svg",{className:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:p.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]}),!e.is_processed&&p.jsx("div",{className:"mb-3",children:p.jsxs("div",{className:"flex items-center space-x-2 text-yellow-400",children:[p.jsx("div",{className:"w-2 h-2 bg-yellow-400 rounded-full animate-pulse"}),p.jsx("span",{className:"text-sm",children:"Processing..."})]})}),e.processing_error&&p.jsx("div",{className:"mb-3",children:p.jsxs("div",{className:"text-red-400 text-sm",children:["⚠️ Processing failed: ",e.processing_error]})}),p.jsxs("div",{className:"text-xs text-gray-500 mb-3",children:["Uploaded ",c(e.uploaded_at)]}),p.jsx("div",{className:"flex justify-end space-x-2",onClick:d=>d.stopPropagation(),children:p.jsx(te,{onClick:o,variant:"danger",size:"sm",isLoading:i,children:"Delete"})})]})},a_=()=>{const{documents:e,selectedDocuments:t,isLoading:n,fetchDocuments:r,searchDocuments:i,clearSelection:s,selectAll:a,deleteDocument:o}=ma(),[l,c]=_.useState(""),[u,d]=_.useState(null),[f,g]=_.useState(!1);_.useEffect(()=>{r()},[r]);const y=async()=>{if(l.trim().length<2){d(null);return}g(!0);try{const h=await i(l.trim());d(h)}catch(h){console.error("Search error:",h)}finally{g(!1)}},x=()=>{c(""),d(null)},k=async()=>{if(!(t.size===0||!window.confirm(`Are you sure you want to delete ${t.size} document(s)? This action cannot be undone.`)))try{const w=Array.from(t).map(S=>o(S));await Promise.all(w),s()}catch(w){console.error("Bulk delete error:",w),alert("Some documents could not be deleted. Please try again.")}},v=u||e,m=t.size>0;return n&&e.length===0?p.jsx("div",{className:"flex items-center justify-center py-12",children:p.jsx("div",{className:"text-gray-400",children:"Loading documents..."})}):p.jsxs("div",{className:"space-y-6",children:[p.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[p.jsx("div",{className:"flex-1",children:p.jsxs("div",{className:"flex gap-2",children:[p.jsx(Lt,{placeholder:"Search documents...",value:l,onChange:c}),p.jsx(te,{onClick:y,isLoading:f,disabled:l.trim().length<2,children:"Search"}),u&&p.jsx(te,{onClick:x,variant:"secondary",children:"Clear"})]})}),m&&p.jsxs("div",{className:"flex gap-2",children:[p.jsx(te,{onClick:a,variant:"secondary",size:"sm",children:"Select All"}),p.jsxs(te,{onClick:s,variant:"secondary",size:"sm",children:["Clear (",t.size,")"]}),p.jsx(te,{onClick:k,variant:"danger",size:"sm",children:"Delete Selected"})]})]}),u&&p.jsxs("div",{className:"text-sm text-gray-400",children:["Found ",u.length,' document(s) matching "',l,'"']}),v.length===0?p.jsxs("div",{className:"text-center py-12",children:[p.jsx("div",{className:"text-gray-400 mb-4",children:u?"No documents found matching your search.":"No documents uploaded yet."}),!u&&p.jsx("p",{className:"text-sm text-gray-500",children:"Upload your first document to get started with AI-powered study materials."})]}):p.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:v.map(h=>p.jsx(s_,{document:h},h.id))})]})},o_=()=>p.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:p.jsxs("div",{className:"space-y-8",children:[p.jsxs("div",{children:[p.jsx("h1",{className:"text-3xl font-bold text-white",children:"Documents"}),p.jsx("p",{className:"mt-2 text-gray-400",children:"Upload and manage your study documents. Supported formats: PDF, DOCX, TXT, PPTX"})]}),p.jsxs("div",{className:"bg-background-secondary rounded-lg p-6",children:[p.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Upload Documents"}),p.jsx(i_,{})]}),p.jsxs("div",{children:[p.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Your Documents"}),p.jsx(a_,{})]})]})}),l_=aa(e=>({isGenerating:!1,generationProgress:"",lastGenerated:null,generateFlashcards:async t=>{e({isGenerating:!0,generationProgress:"Preparing documents..."});try{const n=localStorage.getItem("auth_token");e({generationProgress:"Generating flashcards with AI..."});const r=await fetch("/api/ai/generate-flashcards",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${n}`},body:JSON.stringify(t)});if(!r.ok){const s=await r.json();throw new Error(s.error||"Generation failed")}const i=await r.json();if(i.success)return e({lastGenerated:{studySet:i.data.studySet,content:i.data.flashcards,type:"flashcards"},isGenerating:!1,generationProgress:""}),{studySet:i.data.studySet,flashcards:i.data.flashcards,creditsRemaining:i.data.creditsRemaining};throw new Error(i.error)}catch(n){throw e({isGenerating:!1,generationProgress:""}),n}},generateQuiz:async t=>{e({isGenerating:!0,generationProgress:"Preparing documents..."});try{const n=localStorage.getItem("auth_token");e({generationProgress:"Generating quiz questions with AI..."});const r=await fetch("/api/ai/generate-quiz",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${n}`},body:JSON.stringify(t)});if(!r.ok){const s=await r.json();throw new Error(s.error||"Generation failed")}const i=await r.json();if(i.success)return e({lastGenerated:{studySet:i.data.studySet,content:i.data.questions,type:"quiz"},isGenerating:!1,generationProgress:""}),{studySet:i.data.studySet,questions:i.data.questions,creditsRemaining:i.data.creditsRemaining};throw new Error(i.error)}catch(n){throw e({isGenerating:!1,generationProgress:""}),n}},clearLastGenerated:()=>{e({lastGenerated:null})}})),c_=({selectedDocuments:e,onSelectionChange:t,maxSelection:n=5})=>{const{documents:r,fetchDocuments:i,isLoading:s}=ma(),[a,o]=_.useState("");_.useEffect(()=>{r.length===0&&i()},[r.length,i]);const l=r.filter(d=>d.is_processed&&d.filename.toLowerCase().includes(a.toLowerCase())),c=d=>{e.includes(d)?t(e.filter(g=>g!==d)):e.length<n&&t([...e,d])},u=()=>r.filter(d=>e.includes(d.id));return s?p.jsx("div",{className:"flex items-center justify-center py-8",children:p.jsx("div",{className:"text-gray-400",children:"Loading documents..."})}):r.length===0?p.jsxs("div",{className:"text-center py-8",children:[p.jsx("div",{className:"text-gray-400 mb-4",children:"No documents found"}),p.jsx("p",{className:"text-sm text-gray-500",children:"Upload some documents first to generate study materials."})]}):p.jsxs("div",{className:"space-y-4",children:[p.jsx("div",{children:p.jsx("input",{type:"text",placeholder:"Search documents...",value:a,onChange:d=>o(d.target.value),className:"w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"})}),e.length>0&&p.jsxs("div",{className:"bg-primary-500/10 border border-primary-500/30 rounded-lg p-3",children:[p.jsxs("div",{className:"text-sm text-primary-400 mb-2",children:["Selected ",e.length," of ",n," documents:"]}),p.jsx("div",{className:"space-y-1",children:u().map(d=>p.jsxs("div",{className:"text-sm text-gray-300 flex items-center justify-between",children:[p.jsx("span",{className:"truncate",children:d.filename}),p.jsx("button",{onClick:()=>c(d.id),className:"text-red-400 hover:text-red-300 ml-2",children:"✕"})]},d.id))})]}),p.jsx("div",{className:"max-h-64 overflow-y-auto space-y-2",children:l.map(d=>{const f=e.includes(d.id),g=!f&&e.length<n;return p.jsx("div",{className:`
                p-3 rounded-lg border cursor-pointer transition-all
                ${f?"bg-primary-500/20 border-primary-500":g?"bg-background-secondary border-gray-600 hover:border-gray-500":"bg-gray-800 border-gray-700 opacity-50 cursor-not-allowed"}
              `,onClick:()=>g||f?c(d.id):null,children:p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsx("div",{className:"flex-1 min-w-0",children:p.jsxs("div",{className:"flex items-center space-x-2",children:[p.jsx("span",{className:"text-lg",children:d.file_type==="pdf"?"📄":d.file_type==="docx"?"📝":d.file_type==="txt"?"📃":"📊"}),p.jsxs("div",{className:"min-w-0 flex-1",children:[p.jsx("p",{className:"text-white font-medium truncate",children:d.filename}),p.jsxs("p",{className:"text-sm text-gray-400",children:[d.file_type.toUpperCase()," • ",Math.round(d.file_size/1024)," KB"]})]})]})}),p.jsx("div",{className:`
                  w-5 h-5 rounded border-2 flex items-center justify-center
                  ${f?"bg-primary-500 border-primary-500":"border-gray-500"}
                `,children:f&&p.jsx("svg",{className:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:p.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]})},d.id)})}),l.length===0&&a&&p.jsx("div",{className:"text-center py-4 text-gray-400",children:"No documents match your search."})]})},Rh=({type:e})=>{const[t,n]=_.useState([]),[r,i]=_.useState(""),[s,a]=_.useState(10),[o,l]=_.useState(""),[c,u]=_.useState({}),{generateFlashcards:d,generateQuiz:f,isGenerating:g,generationProgress:y}=l_(),{user:x,updateUser:k}=ha(),v=Et(),m=()=>{const b={};return t.length===0&&(b.documents="Please select at least one document"),r.trim()||(b.name="Study set name is required"),(s<1||s>50)&&(b.count="Item count must be between 1 and 50"),u(b),Object.keys(b).length===0},h=async b=>{if(b.preventDefault(),!!m()){if(!x||x.credits_remaining<1){alert("Insufficient credits. Please purchase more credits to continue.");return}try{const E={documentIds:t,name:r.trim(),count:s,customPrompt:o.trim()||void 0};let C;e==="flashcards"?C=await d(E):C=await f(E),k({credits_remaining:C.creditsRemaining}),v(`/study-sets/${C.studySet.id}`)}catch(E){console.error("Generation error:",E),alert(E.message||"Failed to generate study materials. Please try again.")}}},w=1,S=x&&x.credits_remaining>=w;return p.jsx("div",{className:"max-w-2xl mx-auto",children:p.jsxs("form",{onSubmit:h,className:"space-y-6",children:[p.jsxs("div",{className:"text-center",children:[p.jsxs("h2",{className:"text-2xl font-bold text-white mb-2",children:["Generate ",e==="flashcards"?"Flashcards":"Quiz Questions"]}),p.jsx("p",{className:"text-gray-400",children:"Create AI-powered study materials from your documents"})]}),p.jsx("div",{className:"bg-background-secondary rounded-lg p-4",children:p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsxs("div",{children:[p.jsxs("p",{className:"text-sm text-gray-300",children:["Cost: ",p.jsxs("span",{className:"font-medium text-primary-400",children:[w," credit"]})]}),p.jsxs("p",{className:"text-sm text-gray-400",children:["Your balance: ",(x==null?void 0:x.credits_remaining)||0," credits"]})]}),!S&&p.jsx(te,{onClick:()=>v("/credits"),variant:"secondary",size:"sm",children:"Buy Credits"})]})}),p.jsxs("div",{children:[p.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Documents *"}),p.jsx("div",{className:"bg-background-secondary rounded-lg p-4",children:p.jsx(c_,{selectedDocuments:t,onSelectionChange:n,maxSelection:5})}),c.documents&&p.jsx("p",{className:"mt-1 text-sm text-red-500",children:c.documents})]}),p.jsx(Lt,{label:"Study Set Name",value:r,onChange:i,placeholder:`My ${e==="flashcards"?"Flashcards":"Quiz"}`,error:c.name,required:!0}),p.jsxs("div",{children:[p.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:["Number of ",e==="flashcards"?"Flashcards":"Questions"]}),p.jsx("input",{type:"number",min:"1",max:"50",value:s,onChange:b=>a(parseInt(b.target.value)||10),className:"w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary-500"}),c.count&&p.jsx("p",{className:"mt-1 text-sm text-red-500",children:c.count})]}),p.jsxs("div",{children:[p.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Custom Instructions (Optional)"}),p.jsx("textarea",{value:o,onChange:b=>l(b.target.value),placeholder:"Add specific instructions for the AI (e.g., focus on key concepts, include examples, etc.)",rows:3,className:"w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),g&&p.jsx("div",{className:"bg-primary-500/10 border border-primary-500/30 rounded-lg p-4",children:p.jsxs("div",{className:"flex items-center space-x-3",children:[p.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-primary-500"}),p.jsx("span",{className:"text-primary-400",children:y})]})}),p.jsx(te,{type:"submit",isLoading:g,disabled:!S,className:"w-full",size:"lg",children:g?"Generating...":`Generate ${e==="flashcards"?"Flashcards":"Quiz"} (${w} credit)`}),!S&&p.jsx("p",{className:"text-center text-sm text-red-400",children:"Insufficient credits. Please purchase more credits to continue."})]})})},u_=()=>p.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:p.jsx(Rh,{type:"flashcards"})}),d_=()=>p.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:p.jsx(Rh,{type:"quiz"})}),hi=aa((e,t)=>({currentSession:null,studySetContent:null,isLoading:!1,error:null,fetchStudySetContent:async n=>{e({isLoading:!0,error:null});try{const r=localStorage.getItem("auth_token"),i=await fetch(`/api/study-sets/${n}/content`,{headers:{Authorization:`Bearer ${r}`}});if(!i.ok){const a=await i.json();throw new Error(a.error||"Failed to fetch study set content")}const s=await i.json();if(s.success)e({studySetContent:{studySet:s.data.studySet,flashcards:s.data.studySet.type==="flashcards"?s.data.content:void 0,questions:s.data.studySet.type==="quiz"?s.data.content:void 0},isLoading:!1});else throw new Error(s.error)}catch(r){throw e({error:r.message||"Failed to fetch study set content",isLoading:!1}),r}},startStudySession:async(n,r)=>{var l,c,u;const{studySetContent:i,fetchStudySetContent:s}=t();(!i||((l=i.studySet)==null?void 0:l.id)!==n)&&await s(n);const a=t().studySetContent;if(!a)throw new Error("Failed to load study set content");const o=r==="flashcards"?((c=a.flashcards)==null?void 0:c.length)||0:((u=a.questions)==null?void 0:u.length)||0;if(o===0)throw new Error("No study materials found in this set");e({currentSession:{studySetId:n,type:r,startTime:new Date,currentIndex:0,totalItems:o,reviewedItems:[],flaggedItems:[],correctAnswers:r==="quiz"?0:void 0,timeSpent:0}})},endStudySession:()=>{e({currentSession:null})},nextItem:()=>{const{currentSession:n}=t();if(!n)return;const r=Math.min(n.currentIndex+1,n.totalItems-1);e({currentSession:{...n,currentIndex:r}})},previousItem:()=>{const{currentSession:n}=t();if(!n)return;const r=Math.max(n.currentIndex-1,0);e({currentSession:{...n,currentIndex:r}})},goToItem:n=>{const{currentSession:r}=t();if(!r)return;const i=Math.max(0,Math.min(n,r.totalItems-1));e({currentSession:{...r,currentIndex:i}})},toggleFlag:n=>{const{currentSession:r}=t();if(!r)return;const i=r.flaggedItems.includes(n)?r.flaggedItems.filter(s=>s!==n):[...r.flaggedItems,n];e({currentSession:{...r,flaggedItems:i}})},markReviewed:n=>{const{currentSession:r}=t();r&&(r.reviewedItems.includes(r.currentIndex)||e({currentSession:{...r,reviewedItems:[...r.reviewedItems,r.currentIndex]}}))},submitQuizAnswer:(n,r,i)=>{const{currentSession:s,markReviewed:a}=t();!s||s.type!=="quiz"||(a(n),i&&e({currentSession:{...s,correctAnswers:(s.correctAnswers||0)+1}}))},updateTimeSpent:n=>{const{currentSession:r}=t();r&&e({currentSession:{...r,timeSpent:r.timeSpent+n}})}})),p_=()=>{const{id:e}=Uf(),t=Et(),{studySetContent:n,isLoading:r,error:i,fetchStudySetContent:s}=hi(),[a,o]=_.useState(null);_.useEffect(()=>{e&&s(e).catch(console.error)},[e,s]);const l=async()=>{if(!(!e||!a))try{await hi.getState().startStudySession(e,a),t(`/study/${e}/${a}`)}catch(y){alert(y.message||"Failed to start study session")}};if(r)return p.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:p.jsxs("div",{className:"flex items-center justify-center py-12",children:[p.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),p.jsx("span",{className:"ml-3 text-gray-400",children:"Loading study set..."})]})});if(i||!(n!=null&&n.studySet))return p.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:p.jsxs("div",{className:"text-center py-12",children:[p.jsx("div",{className:"text-red-400 mb-4",children:i||"Study set not found"}),p.jsx(te,{onClick:()=>t("/dashboard"),variant:"secondary",children:"Back to Dashboard"})]})});const{studySet:c,flashcards:u,questions:d}=n,f=u&&u.length>0,g=d&&d.length>0;return p.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[p.jsxs("div",{className:"mb-8",children:[p.jsx("button",{onClick:()=>t("/dashboard"),className:"text-gray-400 hover:text-white mb-4 flex items-center",children:"← Back to Dashboard"}),p.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:c.name}),p.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-400",children:[p.jsx("span",{className:"capitalize",children:c.type}),c.is_ai_generated&&p.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),p.jsxs("span",{children:["Created ",new Date(c.created_at).toLocaleDateString()]})]})]}),p.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 mb-6",children:[p.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Choose Study Mode"}),p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[f&&p.jsx("div",{className:`
                p-4 rounded-lg border-2 cursor-pointer transition-all
                ${a==="flashcards"?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500"}
              `,onClick:()=>o("flashcards"),children:p.jsxs("div",{className:"flex items-center space-x-3",children:[p.jsx("div",{className:"text-2xl",children:"🃏"}),p.jsxs("div",{children:[p.jsx("h3",{className:"font-medium text-white",children:"Flashcard Review"}),p.jsxs("p",{className:"text-sm text-gray-400",children:[u==null?void 0:u.length," flashcards • Interactive review"]})]})]})}),g&&p.jsx("div",{className:`
                p-4 rounded-lg border-2 cursor-pointer transition-all
                ${a==="quiz"?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500"}
              `,onClick:()=>o("quiz"),children:p.jsxs("div",{className:"flex items-center space-x-3",children:[p.jsx("div",{className:"text-2xl",children:"📝"}),p.jsxs("div",{children:[p.jsx("h3",{className:"font-medium text-white",children:"Quiz Practice"}),p.jsxs("p",{className:"text-sm text-gray-400",children:[d==null?void 0:d.length," questions • Test your knowledge"]})]})]})})]}),p.jsx(te,{onClick:l,disabled:!a,className:"w-full",size:"lg",children:a?`Start ${a==="flashcards"?"Flashcard Review":"Quiz Practice"}`:"Select a study mode"})]}),p.jsxs("div",{className:"bg-background-secondary rounded-lg p-6",children:[p.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"Study Set Details"}),p.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[p.jsxs("div",{children:[p.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Content"}),p.jsxs("div",{className:"space-y-1 text-sm text-gray-400",children:[f&&p.jsxs("div",{children:[u==null?void 0:u.length," flashcards"]}),g&&p.jsxs("div",{children:[d==null?void 0:d.length," quiz questions"]})]})]}),c.source_documents&&c.source_documents.length>0&&p.jsxs("div",{children:[p.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Source Documents"}),p.jsx("div",{className:"space-y-1 text-sm text-gray-400",children:c.source_documents.map((y,x)=>p.jsx("div",{children:y.filename},x))})]}),c.custom_prompt&&p.jsxs("div",{className:"md:col-span-2",children:[p.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions"}),p.jsx("p",{className:"text-sm text-gray-400",children:c.custom_prompt})]})]})]})]})},f_=()=>{var E;const e=Et(),{currentSession:t,studySetContent:n,nextItem:r,previousItem:i,toggleFlag:s,markReviewed:a,endStudySession:o,updateTimeSpent:l}=hi(),[c,u]=_.useState(!1),[d,f]=_.useState(Date.now());if(_.useEffect(()=>{const C=setInterval(()=>{l(1)},1e3);return()=>clearInterval(C)},[l]),_.useEffect(()=>{u(!1),f(Date.now())},[t==null?void 0:t.currentIndex]),_.useEffect(()=>{const C=N=>{N.key==="ArrowLeft"?w():N.key==="ArrowRight"?h():N.key===" "?(N.preventDefault(),u(!c)):N.key==="f"&&S()};return window.addEventListener("keydown",C),()=>window.removeEventListener("keydown",C)},[c]),!t||!(n!=null&&n.flashcards))return p.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:p.jsxs("div",{className:"text-center py-12",children:[p.jsx("div",{className:"text-gray-400 mb-4",children:"No flashcard session found"}),p.jsx(te,{onClick:()=>e("/dashboard"),variant:"secondary",children:"Back to Dashboard"})]})});const y=n.flashcards[t.currentIndex],x=(t.currentIndex+1)/t.totalItems*100,k=t.currentIndex===0,v=t.currentIndex===t.totalItems-1,m=t.flaggedItems.includes(y.id),h=()=>{c&&a(y.id),v?b():r()},w=()=>{k||i()},S=()=>{s(y.id)},b=()=>{const C=Math.floor((Date.now()-d)/1e3),N=t.reviewedItems.length,I=t.flaggedItems.length;o(),alert(`Study session complete!

Reviewed: ${N}/${t.totalItems} cards
Flagged: ${I} cards
Time spent: ${Math.floor(C/60)}m ${C%60}s`),e(`/study-sets/${t.studySetId}`)};return p.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[p.jsxs("div",{className:"flex items-center justify-between mb-6",children:[p.jsx("button",{onClick:()=>e(`/study-sets/${t.studySetId}`),className:"text-gray-400 hover:text-white flex items-center",children:"← Back to Study Set"}),p.jsxs("div",{className:"text-center",children:[p.jsx("h1",{className:"text-xl font-semibold text-white",children:(E=n.studySet)==null?void 0:E.name}),p.jsxs("p",{className:"text-sm text-gray-400",children:["Card ",t.currentIndex+1," of ",t.totalItems]})]}),p.jsx(te,{onClick:b,variant:"secondary",size:"sm",children:"Finish"})]}),p.jsxs("div",{className:"mb-6",children:[p.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2",children:p.jsx("div",{className:"bg-primary-500 h-2 rounded-full transition-all duration-300",style:{width:`${x}%`}})}),p.jsxs("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[p.jsxs("span",{children:["Progress: ",Math.round(x),"%"]}),p.jsxs("span",{children:["Time: ",Math.floor(t.timeSpent/60),":",(t.timeSpent%60).toString().padStart(2,"0")]})]})]}),p.jsx("div",{className:"mb-8 flashcard-container",children:p.jsxs("div",{className:`
            relative w-full h-96 cursor-pointer transition-transform duration-500 transform-style-preserve-3d
            ${c?"rotate-y-180":""}
          `,onClick:()=>u(!c),children:[p.jsx("div",{className:`
            absolute inset-0 w-full h-full backface-hidden
            bg-background-secondary border border-gray-600 rounded-lg p-8
            flex items-center justify-center text-center
            ${c?"rotate-y-180":""}
          `,children:p.jsxs("div",{children:[p.jsx("div",{className:"text-sm text-gray-400 mb-4",children:"FRONT"}),p.jsx("div",{className:"text-xl text-white leading-relaxed",children:y.front}),!c&&p.jsx("div",{className:"text-sm text-gray-500 mt-6",children:"Click to reveal answer"})]})}),p.jsx("div",{className:`
            absolute inset-0 w-full h-full backface-hidden rotate-y-180
            bg-primary-500/10 border border-primary-500/30 rounded-lg p-8
            flex items-center justify-center text-center
            ${c?"":"rotate-y-180"}
          `,children:p.jsxs("div",{children:[p.jsx("div",{className:"text-sm text-primary-400 mb-4",children:"BACK"}),p.jsx("div",{className:"text-xl text-white leading-relaxed",children:y.back}),c&&p.jsx("div",{className:"text-sm text-gray-500 mt-6",children:"Click to flip back"})]})})]})}),p.jsxs("div",{className:"flex items-center justify-between",children:[p.jsx(te,{onClick:w,disabled:k,variant:"secondary",children:"← Previous"}),p.jsxs("div",{className:"flex items-center space-x-4",children:[p.jsx(te,{onClick:S,variant:m?"primary":"secondary",size:"sm",children:m?"🚩 Flagged":"🏳️ Flag"}),p.jsx(te,{onClick:()=>u(!c),variant:"secondary",children:c?"Show Front":"Show Back"})]}),p.jsx(te,{onClick:h,variant:"primary",children:v?"Finish":"Next →"})]}),p.jsx("div",{className:"mt-8 text-center text-sm text-gray-500",children:p.jsx("p",{children:"Keyboard shortcuts: ← → (navigate) • Space (flip) • F (flag)"})})]})},h_=()=>{var C;const e=Et(),{currentSession:t,studySetContent:n,nextItem:r,submitQuizAnswer:i,endStudySession:s,updateTimeSpent:a}=hi(),[o,l]=_.useState([]),[c,u]=_.useState(!1),[d,f]=_.useState(!1);if(_.useEffect(()=>{const N=setInterval(()=>{a(1)},1e3);return()=>clearInterval(N)},[a]),_.useEffect(()=>{l([]),u(!1),f(!1)},[t==null?void 0:t.currentIndex]),!t||!(n!=null&&n.questions))return p.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:p.jsxs("div",{className:"text-center py-12",children:[p.jsx("div",{className:"text-gray-400 mb-4",children:"No quiz session found"}),p.jsx(te,{onClick:()=>e("/dashboard"),variant:"secondary",children:"Back to Dashboard"})]})});const y=n.questions[t.currentIndex],x=(t.currentIndex+1)/t.totalItems*100,k=t.currentIndex===t.totalItems-1,v=N=>{c||(y.question_type==="multiple_choice"||y.question_type==="true_false"?l([N]):y.question_type==="select_all"&&l(I=>I.includes(N)?I.filter(M=>M!==N):[...I,N]))},m=N=>{c||l([N])},h=()=>{if(c||o.length===0)return;const N=w();i(y.id,o,N),u(!0),f(!0)},w=()=>{var I;const N=y.correct_answers;if(y.question_type==="short_answer"){const M=((I=o[0])==null?void 0:I.toLowerCase().trim())||"";return N.some(le=>M.includes(le.toLowerCase().trim())||le.toLowerCase().trim().includes(M))}else return o.length===N.length&&o.every(M=>N.includes(M))},S=()=>{k?b():r()},b=()=>{const N=t.totalItems,I=t.correctAnswers||0,M=Math.round(I/N*100),le=t.timeSpent;s(),alert(`Quiz complete!

Score: ${I}/${N} (${M}%)
Time spent: ${Math.floor(le/60)}m ${le%60}s`),e(`/study-sets/${t.studySetId}`)},E=()=>y.options?y.options.map((N,I)=>{const M=o.includes(N),le=y.correct_answers.includes(N);let he="w-full text-left p-4 rounded-lg border transition-all ";return c?le?he+="border-green-500 bg-green-500/20 text-green-400":M&&!le?he+="border-red-500 bg-red-500/20 text-red-400":he+="border-gray-600 bg-background-secondary text-gray-400":M?he+="border-primary-500 bg-primary-500/20 text-primary-400":he+="border-gray-600 bg-background-secondary text-white hover:border-gray-500",p.jsx("button",{onClick:()=>v(N),disabled:c,className:he,children:p.jsxs("div",{className:"flex items-center space-x-3",children:[p.jsx("div",{className:`
              w-5 h-5 rounded border-2 flex items-center justify-center
              ${M?"border-current":"border-gray-500"}
            `,children:M&&p.jsx("div",{className:"w-2 h-2 rounded bg-current"})}),p.jsx("span",{children:N})]})},I)}):null;return p.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[p.jsxs("div",{className:"flex items-center justify-between mb-6",children:[p.jsx("button",{onClick:()=>e(`/study-sets/${t.studySetId}`),className:"text-gray-400 hover:text-white flex items-center",children:"← Back to Study Set"}),p.jsxs("div",{className:"text-center",children:[p.jsx("h1",{className:"text-xl font-semibold text-white",children:(C=n.studySet)==null?void 0:C.name}),p.jsxs("p",{className:"text-sm text-gray-400",children:["Question ",t.currentIndex+1," of ",t.totalItems]})]}),p.jsx(te,{onClick:b,variant:"secondary",size:"sm",children:"Finish Quiz"})]}),p.jsxs("div",{className:"mb-6",children:[p.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2",children:p.jsx("div",{className:"bg-primary-500 h-2 rounded-full transition-all duration-300",style:{width:`${x}%`}})}),p.jsxs("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[p.jsxs("span",{children:["Progress: ",Math.round(x),"%"]}),p.jsxs("span",{children:["Score: ",t.correctAnswers||0,"/",t.currentIndex+(c?1:0)]}),p.jsxs("span",{children:["Time: ",Math.floor(t.timeSpent/60),":",(t.timeSpent%60).toString().padStart(2,"0")]})]})]}),p.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 mb-6",children:[p.jsx("div",{className:"mb-4",children:p.jsx("span",{className:"text-sm text-gray-400 uppercase tracking-wide",children:y.question_type.replace("_"," ")})}),p.jsx("h2",{className:"text-xl text-white mb-6 leading-relaxed",children:y.question_text}),p.jsx("div",{className:"space-y-3",children:y.question_type==="short_answer"?p.jsx("textarea",{value:o[0]||"",onChange:N=>m(N.target.value),disabled:c,placeholder:"Type your answer here...",rows:3,className:"w-full px-4 py-3 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50"}):E()}),!c&&p.jsx("div",{className:"mt-6",children:p.jsx(te,{onClick:h,disabled:o.length===0,className:"w-full",children:"Submit Answer"})}),c&&d&&y.explanation&&p.jsxs("div",{className:"mt-6 p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg",children:[p.jsx("h4",{className:"text-blue-400 font-medium mb-2",children:"Explanation"}),p.jsx("p",{className:"text-gray-300",children:y.explanation})]})]}),c&&p.jsx("div",{className:"flex justify-center",children:p.jsx(te,{onClick:S,variant:"primary",size:"lg",children:k?"Finish Quiz":"Next Question →"})})]})},m_=()=>{const{id:e,mode:t}=Uf(),n=Et(),{currentSession:r,startStudySession:i}=hi();return _.useEffect(()=>{(!r||r.studySetId!==e||r.type!==t)&&(e&&t&&(t==="flashcards"||t==="quiz")?i(e,t).catch(s=>{console.error("Failed to start study session:",s),alert(s.message||"Failed to start study session"),n(`/study-sets/${e}`)}):n("/dashboard"))},[e,t,r,i,n]),r?t==="flashcards"?p.jsx(f_,{}):t==="quiz"?p.jsx(h_,{}):(n("/dashboard"),null):p.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:p.jsxs("div",{className:"flex items-center justify-center py-12",children:[p.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),p.jsx("span",{className:"ml-3 text-gray-400",children:"Starting study session..."})]})})};function v_(){return p.jsx(cy,{children:p.jsxs(ny,{children:[p.jsx(lt,{path:"/",element:p.jsx(Wf,{to:"/login"})}),p.jsx(lt,{path:"/login",element:p.jsx(rw,{})}),p.jsx(lt,{path:"/signup",element:p.jsx(iw,{})}),p.jsx(lt,{path:"/dashboard",element:p.jsx(An,{children:p.jsx(sw,{})})}),p.jsx(lt,{path:"/documents",element:p.jsx(An,{children:p.jsx(o_,{})})}),p.jsx(lt,{path:"/generate/flashcards",element:p.jsx(An,{children:p.jsx(u_,{})})}),p.jsx(lt,{path:"/generate/quiz",element:p.jsx(An,{children:p.jsx(d_,{})})}),p.jsx(lt,{path:"/study-sets/:id",element:p.jsx(An,{children:p.jsx(p_,{})})}),p.jsx(lt,{path:"/study/:id/:mode",element:p.jsx(An,{children:p.jsx(m_,{})})})]})})}ao.createRoot(document.getElementById("root")).render(p.jsx(Bs.StrictMode,{children:p.jsx(v_,{})}));
